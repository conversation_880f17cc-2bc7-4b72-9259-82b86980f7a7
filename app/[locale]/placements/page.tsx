'use client'

import { useTranslation } from 'react-i18next'

import withPermissionAgencyProvider from 'src/hoc/with-permission-agency'
import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { AGENCY_TENANT } from '~/core/constants/enum'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import PlacementAgencyContainer from '~/features/placements/PlacementAgencyContainer'
import PlacementContainer from '~/features/placements/PlacementContainer'

function PlacementsPage() {
  const { t } = useTranslation()
  const currentRole = useBoundStore((state) => state.currentRole)
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:placements`, { PUBLIC_APP_NAME })}`} />

      <If condition={currentRole?.id}>{isCompanyKind ? <PlacementAgencyContainer /> : <PlacementContainer />}</If>
    </>
  )
}

export default withPermissionAgencyProvider(withQueryClientProvider(PlacementsPage), PLAN_FEATURE_KEYS.placement)
