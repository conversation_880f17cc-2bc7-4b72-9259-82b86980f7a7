'use client'

import { useTranslation } from 'react-i18next'

import { PUBLIC_APP_NAME } from '~/core/constants/env'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import AuthenticationErrorContainer from '~/features/auth/error'

function AuthenticationErrorPage() {
  const { t } = useTranslation()
  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:authError`, { PUBLIC_APP_NAME })}`} />
      <AuthenticationErrorContainer />
    </>
  )
}

export default AuthenticationErrorPage
