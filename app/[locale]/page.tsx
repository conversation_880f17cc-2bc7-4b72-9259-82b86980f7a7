'use server'

import { headers } from 'next/headers'
import { permanentRedirect } from 'next/navigation'

import configuration from '~/configuration'
import { NODE_ENV, PUBLIC_APP_URL } from '~/core/constants/env'

export async function getRedirect() {
  const header = await headers()
  const isCustomDomain =
    NODE_ENV === 'development'
      ? !['localhost:3000'].includes(header.get('host') as string)
      : !PUBLIC_APP_URL?.includes(header.get('host') as string)

  return isCustomDomain
}

export default async function HomePage() {
  const isCustomDomain = await getRedirect()
  permanentRedirect(isCustomDomain ? configuration.path.career.list : configuration.path.dashboard)
}
