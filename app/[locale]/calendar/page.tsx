'use client'

import { useTranslation } from 'react-i18next'

import { PUBLIC_APP_NAME } from '~/core/constants/env'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import InterviewsContainer from '~/features/calendar'

function InterviewsPage() {
  const { t } = useTranslation()
  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:calendar`, { PUBLIC_APP_NAME })}`} />

      <InterviewsContainer />
    </>
  )
}

export default InterviewsPage
