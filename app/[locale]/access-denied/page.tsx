'use client'

import { useRouter } from 'next/navigation'
import { useCallback, useEffect } from 'react'
import { Trans, useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import { PUBLIC_APP_NAME, PUBLIC_EMAIL_SUPPORT } from '~/core/constants/env'
import { Button } from '~/core/ui/Button'
import { Container } from '~/core/ui/Container'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import LayoutHybrid from '~/components/Layout/LayoutHybrid'

function ErrorAccessDeniedPage() {
  const { t } = useTranslation()
  const router = useRouter()

  const navigateToReturnPage = useCallback(() => {
    router.push(configuration.path.default)
  }, [])

  useEffect(() => {
    if (window) {
      window.addEventListener('popstate', function (e) {
        window.location.href = configuration.path.default
      })
    }
  }, [])

  return (
    <>
      <AppHeadMetaTags title={`${t('common:seo:accessDenied', { PUBLIC_APP_NAME })}`} />

      <LayoutHybrid>
        <Container overrideClass="max-w-[375px] tablet:max-w-[440px] min-h-screen px-4 tablet:px-0">
          <div className="flex min-h-screen flex-col">
            <div className="tablet:mt-10 mt-6 mb-5 h-5" />

            <div className="flex shrink-0 grow flex-col items-center justify-center">
              <svg width="113" height="112" viewBox="0 0 113 112" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle opacity=".5" cx="56.5" cy="56" r="56" fill="#EDEFFF" />
                <circle opacity=".6" cx="56.5" cy="56" r="44" fill="#DFE3FF" />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M46.806 34.586A2 2 0 0 1 48.22 34h16.56a2 2 0 0 1 1.414.586l11.72 11.72a2 2 0 0 1 .586 1.414v16.56a2 2 0 0 1-.586 1.414l-11.72 11.72A2 2 0 0 1 64.78 78H48.22a2 2 0 0 1-1.414-.586l-11.72-11.72a2 2 0 0 1-.586-1.414V47.72a2 2 0 0 1 .586-1.414l11.72-11.72zM49.048 38 38.5 48.548v14.904L49.048 74h14.904L74.5 63.452V48.548L63.952 38H49.048zm14.866 10.586a2 2 0 0 1 0 2.828L59.328 56l4.586 4.586a2 2 0 1 1-2.828 2.828L56.5 58.828l-4.586 4.586a2 2 0 1 1-2.828-2.828L53.672 56l-4.586-4.586a2 2 0 1 1 2.828-2.828l4.586 4.586 4.586-4.586a2 2 0 0 1 2.828 0z"
                  fill="#5D5AF9"
                />
              </svg>

              <div className="my-[44px]">
                <div className="text-center">
                  <b className="tablet:text-3xl text-2xl font-semibold text-gray-900">
                    {t('common:error:access_denied:title')}
                  </b>
                </div>

                <div className="mt-5">
                  <p className="text-center text-base text-gray-900">
                    {t('common:error:access_denied:description')}
                    <br />
                    <Trans i18nKey={'common:error:access_denied:contact'} values={{ email: PUBLIC_EMAIL_SUPPORT }}>
                      <a href={`mailto:${configuration.mailto}`} className="underline" />
                    </Trans>
                  </p>
                </div>
              </div>

              <Button
                size="lg"
                type="primary"
                onClick={navigateToReturnPage}
                className="w-full"
                label={`${t('common:error:access_denied:button')}`}
              />
            </div>

            <div className="tablet:mb-10 mt-5 mb-6 flex h-5 w-full items-center justify-center space-x-8" />
          </div>
        </Container>
      </LayoutHybrid>
    </>
  )
}

export default ErrorAccessDeniedPage
