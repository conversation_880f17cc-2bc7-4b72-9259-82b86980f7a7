'use client'

import { useTranslation } from 'react-i18next'

import withPermissionEmployerProvider from 'src/hoc/with-permission-employer'
import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import RequisitionContainer from '~/features/requisitions'

function RequisitionPage() {
  const { t } = useTranslation()
  const currentRole = useBoundStore((state) => state.currentRole)

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:requisition`, { PUBLIC_APP_NAME })}`} />

      <If condition={currentRole?.id}>
        <RequisitionContainer />
      </If>
    </>
  )
}

export default withPermissionEmployerProvider(withQueryClientProvider(RequisitionPage))
