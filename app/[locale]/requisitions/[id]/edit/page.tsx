'use client'

import withPermissionEmployerProvider from 'src/hoc/with-permission-employer'
import If from '~/core/ui/If'

import useBoundStore from '~/lib/store'

import RequisitionEditManagementContainer from '~/features/requisitions/[id]/edit'

function RequisitionEditPage() {
  const currentRole = useBoundStore((state) => state.currentRole)

  return (
    <If condition={currentRole?.id}>
      <RequisitionEditManagementContainer />
    </If>
  )
}

export default withPermissionEmployerProvider(RequisitionEditPage)
