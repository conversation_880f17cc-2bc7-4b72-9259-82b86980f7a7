'use client'

import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { PUBLIC_APP_NAME } from '~/core/constants/env'

import { useRouterContext } from '~/lib/next/use-router-context'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import RequisitionPublicDetailContainer from '~/features/requisitions/[id]/[userToken]'

function RequisitionPage() {
  const { t } = useTranslation()
  const { asPath } = useRouterContext()

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:requisition`, { PUBLIC_APP_NAME })}`} url={asPath} />

      <RequisitionPublicDetailContainer />
    </>
  )
}

export default withQueryClientProvider(RequisitionPage)
