'use client'

import Gleap from 'gleap'
import { useTranslation } from 'react-i18next'

import { PUBLIC_APP_NAME } from '~/core/constants/env'
import { Button } from '~/core/ui/Button'
import { Container } from '~/core/ui/Container'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import LayoutGrid from '~/components/Layout/LayoutGrid'

function ErrorBlockedPage() {
  const { t } = useTranslation()

  const contactUsBtn = () => {
    if (Gleap.getInstance().initialized) {
      Gleap.open()
    }
  }

  return (
    <>
      <AppHeadMetaTags
        title={`${t('common:seo:tenantBlocked', {
          PUBLIC_APP_NAME
        })}`}
      />

      <LayoutGrid background="bg-gray-50">
        <Container overrideClass="max-w-[375px] tablet:max-w-[392px] min-h-screen px-4 tablet:px-0">
          <div className="flex min-h-screen flex-col">
            <div className="tablet:mt-10 mt-6 mb-5 h-5" />

            <div className="flex shrink-0 grow flex-col items-center justify-center">
              <svg width="112" height="112" viewBox="0 0 112 112" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle opacity="0.5" cx="56" cy="56" r="56" fill="#EDEFFF" />
                <circle opacity="0.6" cx="56" cy="56" r="44" fill="#DFE3FF" />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M50.3431 40.3431C51.8434 38.8429 53.8783 38 56 38C58.1217 38 60.1566 38.8429 61.6569 40.3431C63.1571 41.8434 64 43.8783 64 46V52H48V46C48 43.8783 48.8429 41.8434 50.3431 40.3431ZM44 52V46C44 42.8174 45.2643 39.7652 47.5147 37.5147C49.7652 35.2643 52.8174 34 56 34C59.1826 34 62.2348 35.2643 64.4853 37.5147C66.7357 39.7652 68 42.8174 68 46V52H70C73.3137 52 76 54.6863 76 58V72C76 75.3137 73.3137 78 70 78H42C38.6863 78 36 75.3137 36 72V58C36 54.6863 38.6863 52 42 52H44ZM66 56H46H42C40.8954 56 40 56.8954 40 58V72C40 73.1046 40.8954 74 42 74H70C71.1046 74 72 73.1046 72 72V58C72 56.8954 71.1046 56 70 56H66Z"
                  fill="#5D5AF9"
                />
              </svg>
              <div className="my-[44px]">
                <div className="text-center">
                  <b className="tablet:text-3xl text-2xl font-semibold text-gray-900">
                    {t('common:error:tenant_blocked:title')}
                  </b>
                </div>

                <div className="mt-5">
                  <p className="text-center text-base text-gray-900">{t('common:error:tenant_blocked:description')}</p>
                </div>
              </div>

              <Button
                size="lg"
                type="primary"
                onClick={contactUsBtn}
                className="w-full"
                label={`${t('common:error:tenant_blocked:button')}`}
              />
            </div>

            <div className="tablet:mb-10 mt-5 mb-6 flex h-5 w-full items-center justify-center space-x-8" />
          </div>
        </Container>
      </LayoutGrid>
    </>
  )
}

export default ErrorBlockedPage
