'use server'

import { cookies as cookiesHeader, headers } from 'next/headers'
import { redirect } from 'next/navigation'

import CustomDomainPublicCareerApp from 'src/app/career/[jobId]'
import configuration from '~/configuration'
import type { PageProps, PageQueryProps } from '~/core/@types/global'
import { SESSION_COOKIE_IP } from '~/core/constants/cookies'
import { initUrqlClient } from '~/core/hooks/use-initialize-client'

import QueryJob from '~/lib/features/apply/jobId/graphql/query-job'
import QueryCareersSetting from '~/lib/features/careers/[id]/graphql/query-careers-setting'
import QueryPublicCareersTemplateShow from '~/lib/features/careers/[id]/graphql/query-public-career-template'
import { mappingTemplateRespToSectionAndConfigData } from '~/lib/features/settings/careers/mapping/editor-mapping'
import { getServerContext } from '~/lib/next/get-server-context'

import type { SettingEditorFormType } from '~/components/Settings/Careers/Editor/CustomizeSettingTab'

async function getCustomDomainCareerData(ctx: PageQueryProps) {
  const cookies = await cookiesHeader()
  const header = await headers()
  const ip = String(cookies.get(SESSION_COOKIE_IP)?.value)
  const host = header.get('host') as string

  const { urqlClient } = initUrqlClient(configuration.api.graphQL, undefined, ctx.locale, ip, `${host}/career`)

  const careerPageSettingResults = await urqlClient
    .query(QueryCareersSetting, {
      tenantSlug: '',
      domainUrl: host
    })
    .toPromise()

  const tenantSlug = careerPageSettingResults.data?.publicSettingsCareerSite?.tenant_slug

  //call episodes query here
  const results = await urqlClient
    .query(QueryJob, {
      id: ctx.params?.jobId,
      tenantSlug
    })
    .toPromise()

  const careerPageTemplateResults = !!careerPageSettingResults?.data?.publicSettingsCareerSite?.enabling
    ? await urqlClient
        .query(QueryPublicCareersTemplateShow, {
          tenantSlug
        })
        .toPromise()
    : null

  if (results.error || careerPageSettingResults.error) {
    redirect(configuration.path.error404)
  }
  // handle feature flag for custom_domain
  if (
    !careerPageSettingResults?.data?.publicSettingsCareerSite?.custom_domain_settings?.enabled ||
    !careerPageSettingResults?.data?.publicSettingsCareerSite?.custom_domain_settings?.unlocked
  ) {
    redirect(configuration.path.error404)
  }

  if (
    !!careerPageSettingResults.data?.publicSettingsCareerSite.languages &&
    !careerPageSettingResults.data?.publicSettingsCareerSite.languages?.[ctx.locale || 'en']?.enable
  ) {
    redirect(configuration.path.error404)
  }

  return {
    jobRes: results.data,
    careerPageSetting: careerPageSettingResults.data?.publicSettingsCareerSite,
    templateConfig: careerPageTemplateResults?.data?.publicCareerTemplatesShow
      ? mappingTemplateRespToSectionAndConfigData(careerPageTemplateResults?.data?.publicCareerTemplatesShow)
          ?.templateConfig
      : null
  }
}

export default async function CustomDomainPublicCareerPage({ searchParams, params }: PageProps) {
  const { jobRes, careerPageSetting, templateConfig } = await getCustomDomainCareerData({
    params: await params,
    query: await searchParams,
    ...(await getServerContext())
  })

  return (
    <CustomDomainPublicCareerApp
      jobRes={jobRes}
      careerPageSetting={careerPageSetting}
      templateConfig={templateConfig as SettingEditorFormType}
    />
  )
}
