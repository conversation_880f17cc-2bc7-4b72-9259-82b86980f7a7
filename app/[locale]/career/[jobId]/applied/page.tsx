'use server'

import { cookies as cookiesHeader, headers } from 'next/headers'

import CustomDomainCareerAppliedApp from 'src/app/career/[jobId]/applied'
import configuration from '~/configuration'
import type { PageProps, PageQueryProps } from '~/core/@types/global'
import { SESSION_COOKIE_IP } from '~/core/constants/cookies'
import { initUrqlClient } from '~/core/hooks/use-initialize-client'

import QueryJobApplied from '~/lib/features/careers/[id]/graphql/query-job'
import { getServerContext } from '~/lib/next/get-server-context'

async function getCustomDomainCareerData(ctx: PageQueryProps) {
  const cookies = await cookiesHeader()
  const header = await headers()
  const ip = String(cookies.get(SESSION_COOKIE_IP)?.value)
  const host = header.get('host') as string

  const { urqlClient } = initUrqlClient(configuration.api.graphQL, undefined, ctx.locale, ip, `${host}/career`)

  //call episodes query here
  const results = await urqlClient
    .query(QueryJobApplied, {
      id: ctx.params?.jobId,
      tenantSlug: '',
      domainUrl: host
    })
    .toPromise()

  if (results.error) {
    throw new Error(results.error.message)
  }

  return {
    jobRes: results.data
  }
}

export default async function CustomDomainCareerAppliedPage({ searchParams, params }: PageProps) {
  const { jobRes } = await getCustomDomainCareerData({
    params: await params,
    query: await searchParams,
    ...(await getServerContext())
  })

  return <CustomDomainCareerAppliedApp jobRes={jobRes} />
}
