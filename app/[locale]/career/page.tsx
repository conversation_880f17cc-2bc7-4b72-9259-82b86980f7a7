'use server'

import { cookies as cookiesHeader, headers } from 'next/headers'
import { redirect } from 'next/navigation'

import CustomDomainSimpleCareerApp from 'src/app/career'
import configuration from '~/configuration'
import type { PageProps, PageQueryProps } from '~/core/@types/global'
import { SESSION_COOKIE_IP } from '~/core/constants/cookies'
import { initUrqlClient } from '~/core/hooks/use-initialize-client'
import { convertToSelectOptions } from '~/core/utilities/common'

import QueryCareersSetting from '~/lib/features/careers/[id]/graphql/query-careers-setting'
import QueryPublicCareersTemplateShow from '~/lib/features/careers/[id]/graphql/query-public-career-template'
import type { ICareerForm } from '~/lib/features/careers/[id]/types'
import type { TemplateResponseType } from '~/lib/features/settings/careers/types/editor'
import { getServerContext } from '~/lib/next/get-server-context'

async function getCustomDomainCareerData(ctx: PageQueryProps) {
  const cookies = await cookiesHeader()
  const header = await headers()
  const ip = String(cookies.get(SESSION_COOKIE_IP)?.value)
  const { query } = ctx
  const host = header.get('host') as string

  const { urqlClient } = initUrqlClient(configuration.api.graphQL, undefined, ctx.locale, ip, `${host}/career`)

  // fetch tenant info by domainUrl
  const careerPageSettingResults = await urqlClient
    .query(QueryCareersSetting, {
      tenantSlug: '',
      domainUrl: host
    })
    .toPromise()
  if (careerPageSettingResults.error) {
    redirect(configuration.path.error404)
  }
  // handle feature flag for custom_domain
  if (
    !careerPageSettingResults?.data?.publicSettingsCareerSite?.custom_domain_settings?.enabled ||
    !careerPageSettingResults?.data?.publicSettingsCareerSite?.custom_domain_settings?.unlocked
  ) {
    redirect(configuration.path.error404)
  }

  // get tenant slug from canonical_url
  const tenantSlug = careerPageSettingResults.data?.publicSettingsCareerSite?.tenant_slug

  // check enable & fetch template career default
  const careerPageTemplateResults = !!careerPageSettingResults.data?.publicSettingsCareerSite?.enabling
    ? await urqlClient
        .query(QueryPublicCareersTemplateShow, {
          tenantSlug
        })
        .toPromise()
    : null

  if (
    !!careerPageSettingResults.data?.publicSettingsCareerSite.languages &&
    !careerPageSettingResults.data?.publicSettingsCareerSite.languages?.[ctx.locale || 'en']?.enable
  ) {
    redirect(configuration.path.error404)
  }

  const stringArrayFields = ['departmentIds']

  stringArrayFields.forEach((field) => {
    const value = query[field]
    if (typeof value === 'string') {
      query[field] = value.split(',')
    }
  })

  return {
    careerPageSetting: careerPageSettingResults.data?.publicSettingsCareerSite,
    careerTemplate: careerPageTemplateResults?.data || null,
    queryStringParams: {
      search: query.search || '',
      location: query?.location ? { id: query?.location } : '',
      departmentIds: convertToSelectOptions(query.departmentIds) ?? undefined,
      remoteStatus: query?.remoteStatus ? { value: query?.remoteStatus } : '',
      jobLevel: query?.jobLevel ? { value: query?.jobLevel } : ''
    }
  }
}

export default async function CustomDomainSimpleCareerPage({ searchParams, params }: PageProps) {
  const { careerPageSetting, careerTemplate, queryStringParams } = await getCustomDomainCareerData({
    params: await params,
    query: await searchParams,
    ...(await getServerContext())
  })

  return (
    <CustomDomainSimpleCareerApp
      queryStringParams={queryStringParams as unknown as ICareerForm}
      careerPageSetting={careerPageSetting}
      careerTemplate={
        careerTemplate as unknown as {
          publicCareerTemplatesShow: TemplateResponseType
        }
      }
    />
  )
}
