'use client'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import If from '~/core/ui/If'

import useBoundStore from '~/lib/store'

import JobDetailManagementContainer from '~/features/jobs/[id]'

function JobDetailPage() {
  const currentRole = useBoundStore((state) => state.currentRole)

  return (
    <If condition={currentRole?.id}>
      <JobDetailManagementContainer />
    </If>
  )
}

export default withQueryClientProvider(JobDetailPage)
