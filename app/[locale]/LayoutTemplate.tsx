'use client'

import { SpeedInsights } from '@vercel/speed-insights/react'
import { setDefaultOptions } from 'date-fns'
import { enUS, ja } from 'date-fns/locale'
import Gleap from 'gleap'
import { SessionProvider } from 'next-auth/react'
import dynamic from 'next/dynamic'
import { useCallback, useEffect, useState } from 'react'
import { Toaster } from 'react-hot-toast'
import { Provider } from 'urql'

import { useLayoutData } from 'src/app/layoutApp'
import configuration from '~/configuration'
import { SESSION_VERSION } from '~/core/constants/cookies'
import { AGENCY_TENANT, GLEAP_EVENTS } from '~/core/constants/enum'
import { DEFAULT_LOCALE, NODE_ENV, PUBLIC_APP_URL } from '~/core/constants/env'
import { useGraphQLRequest } from '~/core/hooks/use-graphQL'
import { Toast as ToastWrapper } from '~/core/ui/Toast'

import { useSubmitSignOutCHUB } from '~/lib/features/career-hub/hook/use-submit-sign-out'
import useNotificationStore from '~/lib/features/notifications/store'
import { useSubmitSignOut } from '~/lib/features/sign-out/hooks/use-submit-sign-out'
import withClientAuthenticationMiddleware from '~/lib/next/with-client-authentication-middleware'
import useBoundStore from '~/lib/store'
import useLoadingBlockStore from '~/lib/store/loading-block'
import useToastStore from '~/lib/store/toast'

import GoogleTracking from '~/components/GoogleTracking'
import IdleTimerSettingWrapper from '~/components/IdleTimerSetting'
import NoSupportMobileDetectView from '~/components/Layout/NoSupportMobileDetectView'
import NetworkWatcher from '~/components/NetworkWatcher'
import NotificationBadgeCountWrapper from '~/components/Notifications/NotificationBadgeCount'
import NotificationDrawerContainer from '~/components/Notifications/NotificationDrawerContainer'
import PermissionSettingWrapper from '~/components/PermissionSetting'
import { SearchProvider } from '~/components/UniversalSearch/USearchContext'

const AppRouteLoadingIndicator = dynamic(() => import('~/core/ui/AppRouteLoadingIndicator'), {
  ssr: false
})

const AppRouteLoadingBlockApp = dynamic(() => import('~/core/ui/AppRouteLoadingBlockApp'), {
  ssr: false
})

const ClearCacheData = ({ isCHUB }: { isCHUB?: boolean }) => {
  // Function to clear complete cache data
  const { handleSignOut } = useSubmitSignOut()
  const { handleSignOut: handleSignOutCHUB } = useSubmitSignOutCHUB()

  const signOut = useCallback(async () => {
    await handleSignOut()
    if (Gleap && Gleap.getInstance().initialized === true) {
      Gleap.clearIdentity()
    }
  }, [])

  const signOutCHUB = useCallback(async () => {
    await handleSignOutCHUB()
    if (Gleap && Gleap.getInstance().initialized === true) {
      Gleap.clearIdentity()
    }
  }, [])

  const clearCache = () => {
    if (window) {
      const version = SESSION_VERSION
      const isCustomDomain =
        NODE_ENV === 'development'
          ? !['localhost:3000'].includes(window.location.host)
          : !PUBLIC_APP_URL?.includes(window.location.host)

      const storageVersion = localStorage.getItem('version') || null
      if (!isCustomDomain && storageVersion && storageVersion !== version) {
        if (isCHUB) {
          signOutCHUB()
        } else {
          signOut()
        }

        setTimeout(() => {
          localStorage.setItem('version', version)
        }, 100)
      } else if (!storageVersion) {
        localStorage.setItem('version', version)
      }
    }
  }

  useEffect(() => {
    clearCache()
  }, [])

  return null
}

function LayoutTemplate({ children }: { children: React.ReactNode }) {
  const { userInitialize: user, session, pathname, asPath, routerLocale } = useLayoutData()
  const { setUser } = useBoundStore()
  const { configToast, setToastClose, clearToast } = useToastStore()
  const { showLockApp, titleLockApp } = useLoadingBlockStore()
  const { openNotificationDrawer } = useNotificationStore()

  const [firstLoad, setFirstLoad] = useState(true)

  const isNotCareerPage = asPath.search('/careers/') === -1
  const isCareerHubPath = asPath.search('/careerhub/') > -1

  const client = useGraphQLRequest({
    isCareerHub: isCareerHubPath,
    language: isNotCareerPage ? user?.language : routerLocale
  })

  useEffect(() => {
    const planName = user?.currentTenant?.tenantPlan?.name

    if (
      configuration.key.gleapApiKey &&
      Gleap &&
      Gleap.getInstance().initialized === false &&
      user?.id &&
      user?.currentTenant?.id &&
      planName &&
      isNotCareerPage
    ) {
      Gleap.setLanguage(user?.language || DEFAULT_LOCALE)
      Gleap.initialize(configuration.key.gleapApiKey)
      Gleap.identify(user?.id, {
        name: user?.fullName,
        email: user?.email,
        companyId: user?.currentTenant.id,
        // @ts-ignore - doesn't need to fix
        companyName: user?.currentTenant?.name,
        plan: planName,
        customData: {
          type: user?.ownTenant ? `${user?.currentTenant.companyKind}-owner` : `${user?.currentTenant.companyKind}`,
          companyId: user?.currentTenant.id,
          companyName: user?.currentTenant?.name
        }
      })
    }
  }, [user])

  const updateCurrentUser = useCallback(() => {
    if (user) {
      setUser(user)
      if (isNotCareerPage) {
        if (user?.language === 'ja') {
          setDefaultOptions({ locale: ja })
        } else {
          setDefaultOptions({ locale: enUS })
        }
      } else {
        setDefaultOptions({
          locale: routerLocale === 'ja' ? ja : enUS
        })
      }
    }
  }, [setUser, user])

  useEffect(updateCurrentUser, [updateCurrentUser])

  useEffect(() => {
    if (Gleap.getInstance().initialized === true && user && firstLoad) {
      setFirstLoad(false)
      if (user?.currentTenant?.interacted) {
        Gleap.trackEvent(GLEAP_EVENTS.firstJobCreated)
        Gleap.trackEvent(GLEAP_EVENTS.firstCandidateAdded)
        Gleap.trackEvent(GLEAP_EVENTS.firstMemberInvited)
        if (user?.currentTenant?.companyKind !== AGENCY_TENANT) {
          Gleap.trackEvent(GLEAP_EVENTS.firstLocationCreated)
          Gleap.trackEvent(GLEAP_EVENTS.firstDepartmentCreated)
        }
      }
    }
  }, [Gleap, user])

  return (
    <Provider value={client}>
      <SessionProvider session={session}>
        <NetworkWatcher />
        {isNotCareerPage === false ? <GoogleTracking /> : <ClearCacheData isCHUB={isCareerHubPath} />}
        <AppRouteLoadingIndicator />

        <PermissionSettingWrapper>
          <NoSupportMobileDetectView>
            {children}
            <SearchProvider />
          </NoSupportMobileDetectView>
        </PermissionSettingWrapper>

        {/* Idle Timer SSO */}
        <IdleTimerSettingWrapper />

        {/* Notification Hub */}
        <NotificationBadgeCountWrapper />
        {isNotCareerPage === true && openNotificationDrawer ? <NotificationDrawerContainer /> : null}

        <Toaster position="bottom-right" />
        <ToastWrapper
          {...configToast}
          setOpen={() => {
            setToastClose()
            setTimeout(() => {
              clearToast()
            }, 100)
          }}
        />
        {showLockApp ? <AppRouteLoadingBlockApp title={titleLockApp} /> : null}

        {/* Vercel tracking perf */}
        {configuration.production === true ? <SpeedInsights route={pathname} /> : null}
      </SessionProvider>
    </Provider>
  )
}

export default withClientAuthenticationMiddleware(LayoutTemplate)
