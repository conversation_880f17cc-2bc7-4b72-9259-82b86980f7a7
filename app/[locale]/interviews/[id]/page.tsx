'use client'

import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import type { IRouterWithID } from '~/core/@types/global'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import InterviewFeedbackContainer from '~/features/interviews/[id]'

interface PageProps {
  params: { id: string }
}

function InterviewFeedbackPage({ params }: PageProps) {
  const { t } = useTranslation()
  const currentRole = useBoundStore((state) => state.currentRole)
  const id = params?.id

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:feedback`, { PUBLIC_APP_NAME })}`} />

      <If condition={currentRole?.id}>
        <InterviewFeedbackContainer id={id as IRouter<PERSON>ith<PERSON>} />
      </If>
    </>
  )
}

export default with<PERSON>ueryClientProvider(InterviewFeedbackPage)
