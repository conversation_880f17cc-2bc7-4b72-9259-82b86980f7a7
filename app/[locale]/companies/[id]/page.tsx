'use client'

import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { AGENCY_TENANT } from '~/core/constants/enum'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { useRouterContext } from '~/lib/next/use-router-context'
import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import { withLayoutGrid } from '~/components/Layout/LayoutGrid'
import CompaniesAgencyDetailContainer from '~/features/agency/companies/[id]'
import CompaniesDetailContainer from '~/features/companies/[id]'

function CompanyDetailAgencyPage() {
  const { t } = useTranslation()
  const currentRole = useBoundStore((state) => state.currentRole)
  const { params } = useRouterContext()
  const id = params?.id
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:agency:companies`, { PUBLIC_APP_NAME })}`} />

      <If condition={currentRole?.id}>
        {isCompanyKind ? (
          <CompaniesAgencyDetailContainer isDrawer={false} companyId={Number(id)} />
        ) : (
          <CompaniesDetailContainer isDrawer={false} companyId={Number(id)} />
        )}
      </If>
    </>
  )
}

export default withLayoutGrid(withQueryClientProvider(CompanyDetailAgencyPage))
