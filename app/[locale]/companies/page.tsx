'use client'

import { useTranslation } from 'react-i18next'

import withPermissionAgencyProvider from 'src/hoc/with-permission-agency'
import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { AGENCY_TENANT } from '~/core/constants/enum'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import CompaniesAgencyContainer from '~/features/agency/companies'
import CompaniesContainer from '~/features/companies'

function CompaniesAgencyPage() {
  const { t } = useTranslation()
  const currentRole = useBoundStore((state) => state.currentRole)
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:agency:companies`, { PUBLIC_APP_NAME })}`} />
      <If condition={currentRole?.id}>{isCompanyKind ? <CompaniesAgencyContainer /> : <CompaniesContainer />}</If>
    </>
  )
}

export default withPermissionAgencyProvider(withQueryClientProvider(CompaniesAgencyPage), PLAN_FEATURE_KEYS.company)
