'use client'

import { useTranslation } from 'react-i18next'

import withPermissionEmployerProvider from 'src/hoc/with-permission-employer'
import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import CompanySettingsContainer from '~/features/settings/company-settings'

function CompanySettingsPage() {
  const { t } = useTranslation()
  const currentRole = useBoundStore((state) => state.currentRole)

  return (
    <>
      <AppHeadMetaTags
        title={`${t(`common:seo:settingCompanySettings`, {
          PUBLIC_APP_NAME
        })}`}
      />

      <If condition={currentRole?.id}>
        <CompanySettingsContainer />
      </If>
    </>
  )
}

export default withPermissionEmployerProvider(withQueryClientProvider(CompanySettingsPage), PLAN_FEATURE_KEYS.company)
