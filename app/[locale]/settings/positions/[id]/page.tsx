'use client'

import { useTranslation } from 'react-i18next'

import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import PositionDetailsManagementContainer from '~/features/settings/positions/[id]'

function PositionsManagementPage() {
  const { t } = useTranslation()
  const currentRole = useBoundStore((state) => state.currentRole)

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:settingPositions`, { PUBLIC_APP_NAME })}`} />

      <If condition={currentRole?.id}>
        <PositionDetailsManagementContainer />
      </If>
    </>
  )
}

export default PositionsManagementPage
