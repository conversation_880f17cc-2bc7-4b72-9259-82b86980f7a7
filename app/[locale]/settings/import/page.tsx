'use client'

import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import { useRouterContext } from '~/lib/next/use-router-context'
import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import ImportManagerRouter from '~/components/Settings/Import/components/ImportManagerRouter'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'

const ImportManagementPage = () => {
  const { t } = useTranslation()
  const { searchParams } = useRouterContext()
  const currentRole = useBoundStore((state) => state.currentRole)
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const enableImportJobs =
    isFeatureEnabled(PLAN_FEATURE_KEYS.import_job) && isUnLockFeature(PLAN_FEATURE_KEYS.import_job)

  const enableImportCandidates =
    isFeatureEnabled(PLAN_FEATURE_KEYS.import_candidate) && isUnLockFeature(PLAN_FEATURE_KEYS.import_candidate)

  const enableImportCourses =
    isFeatureEnabled(PLAN_FEATURE_KEYS.learning_management_system) &&
    isUnLockFeature(PLAN_FEATURE_KEYS.learning_management_system)

  const query = {
    tab: searchParams?.get('tab') || 'import',
    object_kind: searchParams?.get('object_kind') || 'job'
  }

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:settingMembers`, { PUBLIC_APP_NAME })}`} />
      <If condition={currentRole?.id}>
        <LayoutGridSettings>
          <ImportManagerRouter
            enableImportJobs={enableImportJobs}
            enableImportCandidates={enableImportCandidates}
            enableImportCourses={enableImportCourses}
            query={query}
          />
        </LayoutGridSettings>
      </If>
    </>
  )
}

export default withQueryClientProvider(ImportManagementPage)
