'use client'

import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'
import { adminCanAction } from '~/core/utilities/permission'

import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import SecurityManagement from '~/features/settings/security'

function SecurityManagementPage() {
  const { t } = useTranslation()
  const router = useRouter()
  const { currentRole } = useBoundStore()
  const { setToast } = useToastStore()

  useEffect(() => {
    if (currentRole && !adminCanAction(currentRole?.code)) {
      setToast({
        open: true,
        type: 'error',
        title: `${t('notification:errorPermission')}`
      })
      router.push(configuration.path.errorAccessDenied)
    }
  }, [currentRole])

  return (
    <>
      <AppHeadMetaTags
        title={`${t(`common:seo:settingSecurity`, {
          PUBLIC_APP_NAME
        })}`}
      />

      <If condition={adminCanAction(currentRole?.code)}>
        <SecurityManagement />
      </If>
    </>
  )
}

export default SecurityManagementPage
