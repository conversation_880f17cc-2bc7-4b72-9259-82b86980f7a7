'use client'

import { useTranslation } from 'react-i18next'

import withPermissionAgencyProvider from 'src/hoc/with-permission-agency'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import TeamsManagementContainer from '~/features/settings/teams'

function TeamsManagementPage() {
  const { t } = useTranslation()
  const currentRole = useBoundStore((state) => state.currentRole)

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:settingTeams`, { PUBLIC_APP_NAME })}`} />

      <If condition={currentRole?.id}>
        <TeamsManagementContainer />
      </If>
    </>
  )
}

export default withPermissionAgencyProvider(TeamsManagementPage, PLAN_FEATURE_KEYS.company)
