'use client'

import { useTranslation } from 'react-i18next'

import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import EmailSettingContainer from '~/features/settings/email-setting'

function EmailSettingPage() {
  const { t } = useTranslation()
  const currentRole = useBoundStore((state) => state.currentRole)

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:settingEmailSignature`, { PUBLIC_APP_NAME })}`} />

      <If condition={currentRole?.id}>
        <EmailSettingContainer />
      </If>
    </>
  )
}

export default EmailSettingPage
