'use client'

import { useTranslation } from 'react-i18next'

import withPermissionAgencyProvider from 'src/hoc/with-permission-agency'
import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import CompanyStatusSettingContainer from '~/features/settings/company-status'

function CompanyStatusAgencyPage() {
  const { t } = useTranslation()
  const currentRole = useBoundStore((state) => state.currentRole)

  return (
    <>
      <AppHeadMetaTags
        title={`${t(`common:seo:agency:settings:companyStatus`, {
          PUBLIC_APP_NAME
        })}`}
      />

      <If condition={currentRole?.id}>
        <CompanyStatusSettingContainer />
      </If>
    </>
  )
}

export default withPermissionAgencyProvider(withQueryClientProvider(CompanyStatusAgencyPage))
