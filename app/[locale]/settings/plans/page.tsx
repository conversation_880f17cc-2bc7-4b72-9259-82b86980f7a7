'use client'

import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import PlansManagementContainer from '~/features/settings/plans'

function PlansManagementPage() {
  const { t } = useTranslation()
  const router = useRouter()
  const { user } = useBoundStore()
  const { setToast } = useToastStore()

  useEffect(() => {
    if (user.ownTenant !== undefined && user.ownTenant === false) {
      setToast({
        open: true,
        type: 'error',
        title: `${t('notification:errorPermission')}`
      })
      router.push(configuration.path.errorAccessDenied)
    }
  }, [user])

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:settingPlans`, { PUBLIC_APP_NAME })}`} />

      <If condition={user.ownTenant}>
        <PlansManagementContainer />
      </If>
    </>
  )
}

export default PlansManagementPage
