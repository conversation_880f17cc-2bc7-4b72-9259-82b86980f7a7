'use client'

import { useTranslation } from 'react-i18next'

import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import PlanExpiredManagementContainer from '~/features/settings/plans/expired'

function PlansExpiredManagementPage() {
  const { t } = useTranslation()
  const user = useBoundStore((state) => state.user)

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:settingExpiredPlans`, { PUBLIC_APP_NAME })}`} />

      <If condition={user.ownTenant}>
        <PlanExpiredManagementContainer />
      </If>
    </>
  )
}

export default PlansExpiredManagementPage
