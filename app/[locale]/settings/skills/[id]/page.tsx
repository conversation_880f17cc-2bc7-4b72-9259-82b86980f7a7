'use client'

import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import type { IRouterWithID } from '~/core/@types/global'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import { useRouterContext } from '~/lib/next/use-router-context'
import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import SkillDetails from '~/features/settings/skills/[id]'

function SkillDetailPage() {
  const { t } = useTranslation()
  const { params } = useRouterContext()
  const currentRole = useBoundStore((state) => state.currentRole)
  const id = params?.id

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:settingSkillReport`, { PUBLIC_APP_NAME })}`} />
      <If condition={currentRole?.id}>
        <SkillDetails id={id as IRouterWithID} />
      </If>
    </>
  )
}

export default withQueryClientProvider(SkillDetailPage)
