'use client'

import { useTranslation } from 'react-i18next'

import withPermissionEmployerProvider from 'src/hoc/with-permission-employer'
import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import ResumesEditCVContainer from '~/features/settings/profiles/edit'

function ResumesEditCVPage() {
  const { t } = useTranslation()
  const { currentRole, user } = useBoundStore()

  return (
    <>
      <AppHeadMetaTags
        title={`${t(`common:seo:resumesEditor`, {
          PUBLIC_APP_NAME
        })}`}
      />

      <If condition={currentRole?.id && user?.id}>
        <ResumesEditCVContainer user={user} />
      </If>
    </>
  )
}

export default withPermissionEmployerProvider(withQueryClientProvider(ResumesEditCVPage))
