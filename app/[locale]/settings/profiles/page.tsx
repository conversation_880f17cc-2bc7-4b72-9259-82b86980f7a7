'use client'

import { useTranslation } from 'react-i18next'

import withPermissionEmployerProvider from 'src/hoc/with-permission-employer'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import ProfilesContainer from '~/features/settings/profiles'

function ProfilesPage() {
  const { t } = useTranslation()
  const currentRole = useBoundStore((state) => state.currentRole)

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:settingProfiles`, { PUBLIC_APP_NAME })}`} />

      <If condition={currentRole?.id}>
        <ProfilesContainer />
      </If>
    </>
  )
}

export default withPermissionEmployerProvider(ProfilesPage)
