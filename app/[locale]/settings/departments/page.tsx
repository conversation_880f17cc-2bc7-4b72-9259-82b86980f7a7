'use client'

import { useTranslation } from 'react-i18next'

import withPermissionEmployerProvider from 'src/hoc/with-permission-employer'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import DepartmentsManagementContainer from '~/features/settings/departments'

function DepartmentsManagementPage() {
  const { t } = useTranslation()
  const currentRole = useBoundStore((state) => state.currentRole)

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:settingDepartments`, { PUBLIC_APP_NAME })}`} />

      <If condition={currentRole?.id}>
        <DepartmentsManagementContainer />
      </If>
    </>
  )
}

export default withPermissionEmployerProvider(DepartmentsManagementPage)
