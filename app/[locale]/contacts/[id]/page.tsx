'use client'

import { useTranslation } from 'react-i18next'

import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import { useRouterContext } from '~/lib/next/use-router-context'
import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import { withLayoutGrid } from '~/components/Layout/LayoutGrid'
import ContactAgencyDetailContainer from '~/features/agency/contacts/[id]'

function ContactDetailAgencyPage() {
  const { t } = useTranslation()
  const currentRole = useBoundStore((state) => state.currentRole)
  const { params } = useRouterContext()
  const id = params?.id

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:agency:contacts`, { PUBLIC_APP_NAME })}`} />

      <If condition={currentRole?.id}>
        <ContactAgencyDetailContainer isDrawer={false} contactId={Number(id)} />
      </If>
    </>
  )
}

export default withLayoutGrid(ContactDetailAgencyPage)
