'use client'

import { useTranslation } from 'react-i18next'

import withPermissionAgencyProvider from 'src/hoc/with-permission-agency'
import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import LayoutGrid from '~/components/Layout/LayoutGrid'
import ContactsAgencyContainer from '~/features/agency/contacts'

function ContactsAgencyPage() {
  const { t } = useTranslation()
  const currentRole = useBoundStore((state) => state.currentRole)

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:agency:contacts`, { PUBLIC_APP_NAME })}`} />
      <If condition={currentRole?.id}>
        <LayoutGrid>
          <ContactsAgencyContainer />
        </LayoutGrid>
      </If>
    </>
  )
}

export default withPermissionAgencyProvider(withQueryClientProvider(ContactsAgencyPage), PLAN_FEATURE_KEYS.company)
