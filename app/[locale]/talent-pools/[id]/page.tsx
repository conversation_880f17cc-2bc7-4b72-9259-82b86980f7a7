'use client'

import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import TalentPoolDetailContainer from '~/features/talent-pool/[id]'

function JobDetailPage() {
  const { t } = useTranslation()
  const currentRole = useBoundStore((state) => state.currentRole)

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:talentPool`, { PUBLIC_APP_NAME })}`} />

      <If condition={currentRole?.id}>
        <TalentPoolDetailContainer />
      </If>
    </>
  )
}

export default withQueryClientProvider(JobDetailPage)
