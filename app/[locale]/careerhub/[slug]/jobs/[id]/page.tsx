'use client'

import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import { withCHUBCheckPermissionFeature } from '~/components/Subscription/CHUBFeaturePermission'
import CareerHubDetailManagementContainer from '~/features/career-hub/[slug]/jobs/[id]'

const CareerHubPage = () => {
  const { t } = useTranslation()

  return (
    <>
      <AppHeadMetaTags title={t('settings:referrals:title')} hideFavicon={true} />

      <CareerHubDetailManagementContainer />
    </>
  )
}
export default withCHUBCheckPermissionFeature(withQueryClientProvider(CareerHubPage))
