'use client'

import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'

import type { IOpenJobsManagementFilter } from '~/lib/features/referrals/types'
import {
  convertQueryToSelectOptions,
  convertQueryToSingleSelectOption,
  processClientSideProps
} from '~/lib/next/client-side-props-migration'
import { useRouterContext } from '~/lib/next/use-router-context'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import { withCHUBCheckPermissionFeature } from '~/components/Subscription/CHUBFeaturePermission'
import CareerHubContainer from '~/features/career-hub/[slug]/jobs'

const CareerHubPage = () => {
  const { t } = useTranslation()
  const { searchParams } = useRouterContext()

  // Process searchParams using client-side migration utility
  const stringArrayFields = [
    'departmentIds',
    'locationIds',
    'talentPoolIds',
    'tagIds',
    'countryStateIds',
    'jobsKey',
    'companyId'
  ]
  const query = processClientSideProps(searchParams, stringArrayFields)

  // Convert to props format using utility functions
  const props: IOpenJobsManagementFilter = {
    search: query.search || query.q || '',
    jobsKey: convertQueryToSingleSelectOption(query.departmentIds),
    locationIds: convertQueryToSelectOptions(query.locationIds),
    departmentIds: convertQueryToSelectOptions(query.departmentIds),
    talentPoolIds: convertQueryToSelectOptions(query.talentPoolIds),
    countryStateIds: convertQueryToSelectOptions(query.countryStateIds),
    jobLevel: query?.jobLevel ? { value: query.jobLevel } : undefined,
    operator: query?.operator || '',
    tagIds: convertQueryToSelectOptions(query.tagIds),
    remoteStatus: query?.remoteStatus ? { value: query.remoteStatus } : undefined,
    companyId: query?.companyId ? { value: query.companyId } : undefined
  }

  return (
    <>
      <AppHeadMetaTags title={t('settings:referrals:title')} hideFavicon={true} />
      <CareerHubContainer {...props} />
    </>
  )
}

export default withCHUBCheckPermissionFeature(withQueryClientProvider(CareerHubPage))
