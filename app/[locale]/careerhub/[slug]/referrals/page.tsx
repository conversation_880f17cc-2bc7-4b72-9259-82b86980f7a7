'use client'

import { permanentRedirect } from 'next/navigation'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import configuration from '~/configuration'

import useReferralSettingRecallFocusWindow from '~/lib/features/settings/referrals/hooks/useReferralSettingRecallFocusWindow'
import { useRouterContext } from '~/lib/next/use-router-context'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import { withCHUBCheckPermissionFeature } from '~/components/Subscription/CHUBFeaturePermission'
import CareerHubReferralContainer from '~/features/career-hub/[slug]/referrals'

const CareerHubReferralPage = () => {
  const { t } = useTranslation()
  const { params } = useRouterContext()
  const { dataReferral } = useReferralSettingRecallFocusWindow()
  const slug = params?.slug

  useEffect(() => {
    if (!!dataReferral && dataReferral?.values?.referral_portal?.job_only) {
      permanentRedirect(configuration.path.careerHub.error404(String(slug)))
    }
  }, [dataReferral])

  if (!dataReferral || (!!dataReferral && dataReferral?.values?.referral_portal?.job_only)) return null

  return (
    <>
      <AppHeadMetaTags title={t('settings:referrals:title')} hideFavicon={true} />
      <CareerHubReferralContainer />
    </>
  )
}
export default withCHUBCheckPermissionFeature(withQueryClientProvider(CareerHubReferralPage))
