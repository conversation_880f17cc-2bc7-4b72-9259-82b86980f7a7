'use client'

import { useTranslation } from 'react-i18next'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import { withCHUBCheckPermissionFeature } from '~/components/Subscription/CHUBFeaturePermission'
import CareerHubAccountContainer from '~/features/career-hub/account'

function CareerHubAccountPage() {
  const { t } = useTranslation()

  return (
    <>
      <AppHeadMetaTags title={t('settings:referrals:title')} />
      <CareerHubAccountContainer />
    </>
  )
}

export default withCHUBCheckPermissionFeature(CareerHubAccountPage)
