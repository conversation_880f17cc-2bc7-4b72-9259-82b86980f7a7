'use client'

import { useTranslation } from 'react-i18next'

import { PUBLIC_APP_NAME } from '~/core/constants/env'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import { withCHUBCheckPermissionFeature } from '~/components/Subscription/CHUBFeaturePermission'
import VerifyEmailContainer from '~/features/verify-email'

function VerifyEmailPage() {
  const { t } = useTranslation()

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:verifyEmail`, { PUBLIC_APP_NAME })}`} />

      <VerifyEmailContainer />
    </>
  )
}

export default withCHUBCheckPermissionFeature(VerifyEmailPage)
