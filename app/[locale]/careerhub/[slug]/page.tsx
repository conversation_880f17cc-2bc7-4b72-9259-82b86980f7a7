'use server'

import { permanentRedirect } from 'next/navigation'

import configuration from '~/configuration'

interface PageProps {
  params: { slug: string }
}

export default async function CHUBHomePage({ params }: PageProps) {
  const { slug } = await params
  const decodeSlug = slug ? decodeURIComponent(slug) : undefined

  if (!slug) {
    permanentRedirect('/404')
  }

  permanentRedirect(configuration.path.careerHub.jobs(encodeURIComponent(slug)))
}
