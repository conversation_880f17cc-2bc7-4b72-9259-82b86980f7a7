'use client'

import { useTranslation } from 'react-i18next'

import { PUBLIC_APP_NAME } from '~/core/constants/env'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import { withCHUBCheckPermissionFeature } from '~/components/Subscription/CHUBFeaturePermission'
import CareerHubAuthenticationErrorPageContainer from '~/features/career-hub/auth/error'

function CareerHubAuthenticationErrorPage() {
  const { t } = useTranslation()

  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:careerHubAuthError`, { PUBLIC_APP_NAME })}`} />

      <CareerHubAuthenticationErrorPageContainer />
    </>
  )
}

export default withCHUBCheckPermissionFeature(CareerHubAuthenticationErrorPage)
