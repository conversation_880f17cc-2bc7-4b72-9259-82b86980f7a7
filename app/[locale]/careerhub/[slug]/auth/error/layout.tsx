'use server'

import { redirect } from 'next/navigation'

interface LayoutProps {
  children: React.ReactNode
  params: { slug: string }
}

async function CareerHubAuthenticationErrorLayout({ children, params }: LayoutProps) {
  const { slug } = await params
  const tenantSlug = slug ? decodeURIComponent(slug) : undefined

  // Basic validation
  if (!tenantSlug || tenantSlug.length === 0) {
    redirect('/404')
  }

  return <div className="career-hub-authentication-error-layout">{children}</div>
}

export default CareerHubAuthenticationErrorLayout
