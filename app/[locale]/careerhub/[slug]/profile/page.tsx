'use client'

import { useTranslation } from 'react-i18next'

import withPermissionEmployerProvider from 'src/hoc/with-permission-employer'
import { PUBLIC_APP_NAME } from '~/core/constants/env'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import CareerHubProfilesContainer from '~/features/career-hub/[slug]/profile'

function ProfilesPage() {
  const { t } = useTranslation()
  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:settingProfiles`, { PUBLIC_APP_NAME })}`} />
      <CareerHubProfilesContainer />
    </>
  )
}

export default withPermissionEmployerProvider(ProfilesPage)
