'use client'

import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { PUBLIC_APP_NAME } from '~/core/constants/env'

import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import CareerHubResumesEditCVContainer from '~/features/career-hub/[slug]/profile/editor/index'

function ResumesEditCVPage() {
  const { t } = useTranslation()
  const { currentRole, user } = useBoundStore()

  return (
    <>
      <AppHeadMetaTags
        title={`${t(`common:seo:resumesEditor`, {
          PUBLIC_APP_NAME
        })}`}
      />

      <CareerHubResumesEditCVContainer user={user} />
    </>
  )
}

export default withQueryClientProvider(ResumesEditCVPage)
