'use client'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import { withCHUBCheckPermissionFeature } from '~/components/Subscription/CHUBFeaturePermission'
import CareerHubMyJobsContainer from '~/features/career-hub/[slug]/my-jobs'

const CareerHubPage = () => {
  return (
    <>
      <AppHeadMetaTags title="Career Hub - my jobs" description="Career Hub - my jobs" hideFavicon={true} />

      <CareerHubMyJobsContainer />
    </>
  )
}
export default withCHUBCheckPermissionFeature(withQueryClientProvider(CareerHubPage))
