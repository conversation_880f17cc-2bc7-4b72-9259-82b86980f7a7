'use client'

import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { PUBLIC_APP_NAME } from '~/core/constants/env'

import type { CourseFilter } from '~/lib/features/career-hub/types'
import {
  convertQueryToSelectOptions,
  isFilterTouched,
  processClientSideProps
} from '~/lib/next/client-side-props-migration'
import { useRouterContext } from '~/lib/next/use-router-context'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import { withCHUBCheckPermissionFeature } from '~/components/Subscription/CHUBFeaturePermission'
import LearningLibrary from '~/features/career-hub/[slug]/learning-library'

const CareerHubPage = () => {
  const { t } = useTranslation()
  const { searchParams } = useRouterContext()

  // Process searchParams using client-side migration utility
  const stringArrayFields = ['skills', 'provider', 'level', 'type', 'languages']
  const query = processClientSideProps(searchParams, stringArrayFields)

  // Convert to props format using utility functions
  const props: CourseFilter & { sort: string } = {
    search: query.search || '',
    skills: convertQueryToSelectOptions(query.skills) ?? undefined,
    provider: convertQueryToSelectOptions(query.provider) ?? undefined,
    level: convertQueryToSelectOptions(query.level) ?? undefined,
    type: convertQueryToSelectOptions(query.type) ?? undefined,
    languages: convertQueryToSelectOptions(query.languages) ?? undefined,
    sort: query.sort || 'relevant',
    isFilterTouched: isFilterTouched(query, ['search', 'skills', 'provider', 'level', 'type', 'languages', 'sort'])
  }

  return (
    <>
      <AppHeadMetaTags title={`${t('common:seo:learningLibrary', { PUBLIC_APP_NAME })}`} hideFavicon={true} />
      <LearningLibrary {...props} />
    </>
  )
}

export default withCHUBCheckPermissionFeature(withQueryClientProvider(CareerHubPage))
