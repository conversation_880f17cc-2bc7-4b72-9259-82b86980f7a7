'use client'

import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { PUBLIC_APP_NAME } from '~/core/constants/env'

import HeadMetaTags from '~/components/HeadMetaTags'
import { withCHUBCheckPermissionFeature } from '~/components/Subscription/CHUBFeaturePermission'
import CareerHubPositionsContainer from '~/features/career-hub/[slug]/career-navigator/positions/[id]'

const CareerHubPositionPage = () => {
  const { t } = useTranslation()
  return (
    <>
      <HeadMetaTags title={`${t('common:seo:careerNavigator', { PUBLIC_APP_NAME })}`} hideFavicon={true} />
      <CareerHubPositionsContainer />
    </>
  )
}
export default withCHUBCheckPermissionFeature(withQueryClientProvider(CareerHubPositionPage))
