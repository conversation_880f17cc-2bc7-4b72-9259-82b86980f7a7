'use client'

import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { PUBLIC_APP_NAME } from '~/core/constants/env'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import { withCHUBCheckPermissionFeature } from '~/components/Subscription/CHUBFeaturePermission'
import CareerNavigator from '~/features/career-hub/[slug]/career-navigator'

const CareerHubPage = () => {
  const { t } = useTranslation()

  return (
    <>
      <AppHeadMetaTags title={`${t('common:seo:careerNavigator', { PUBLIC_APP_NAME })}`} hideFavicon={true} />
      <CareerNavigator />
    </>
  )
}

export default withCHUBCheckPermissionFeature(withQueryClientProvider(CareerHubPage))
