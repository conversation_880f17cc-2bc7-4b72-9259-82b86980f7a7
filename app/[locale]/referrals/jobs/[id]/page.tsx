'use client'

import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import ReferralDetailManagementContainer from '~/features/referrals/jobs/[id]'

function ReferralDetailPage() {
  const currentRole = useBoundStore((state) => state.currentRole)
  const { t } = useTranslation()

  return (
    <>
      <AppHeadMetaTags
        title={`${t(`common:seo:referrals`, {
          PUBLIC_APP_NAME
        })}`}
      />
      <If condition={currentRole?.id}>
        <ReferralDetailManagementContainer />
      </If>
    </>
  )
}

export default withQueryClientProvider(ReferralDetailPage)
