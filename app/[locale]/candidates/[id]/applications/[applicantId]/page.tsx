'use client'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import type { IRouterWithID } from '~/core/@types/global'
import If from '~/core/ui/If'

import { useRouterContext } from '~/lib/next/use-router-context'
import useBoundStore from '~/lib/store'

import CandidateProfileContainer from '~/features/candidates/[id]'

function CandidateProfileWithJobPage() {
  const { params } = useRouterContext()
  const currentRole = useBoundStore((state) => state.currentRole)
  const applicantId = params?.applicantId
  const id = params?.id

  return (
    <If condition={currentRole?.id}>
      <CandidateProfileContainer isDrawer={false} applicantId={applicantId as IRouterWithID} id={id as IRouterWithID} />
    </If>
  )
}

export default withQueryClientProvider(CandidateProfileWithJobPage)
