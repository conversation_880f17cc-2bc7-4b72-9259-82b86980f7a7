'use client'

import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import type { IRouterWithID } from '~/core/@types/global'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import { useRouterContext } from '~/lib/next/use-router-context'
import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import CandidateProfileEditCVContainer from '~/features/candidates/[id]/edit'

function CandidateProfileEditCVPage() {
  const { params } = useRouterContext()
  const { t } = useTranslation()
  const { currentRole } = useBoundStore()

  return (
    <>
      <AppHeadMetaTags
        title={`${t(`common:seo:candidateDetailEdit`, {
          PUBLIC_APP_NAME
        })}`}
      />

      <If condition={currentRole?.id}>
        <CandidateProfileEditCVContainer id={params?.id as IRouterWithID} />
      </If>
    </>
  )
}

export default withQueryClientProvider(CandidateProfileEditCVPage)
