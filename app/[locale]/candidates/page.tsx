'use client'

import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import useProfileViewDisplayStore from '~/lib/features/candidates/store/profile-view-display-slice'
import { useRouterContext } from '~/lib/next/use-router-context'
import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import LayoutGrid from '~/components/Layout/LayoutGrid'
import CandidatesManagementContainer, { CandidatesProfileViewDisplayConfig } from '~/features/candidates'

function CandidatesPage() {
  const { searchParams } = useRouterContext()
  const view = searchParams?.get('view')
  const { t } = useTranslation()
  const currentRole = useBoundStore((state) => state.currentRole)
  const { profileViewDisplay, setProfileViewDisplay } = useProfileViewDisplayStore()
  const [title, setTitle] = useState<string>('')

  useEffect(() => {
    if (profileViewDisplay?.name) {
      setTitle(
        `${t(`common:seo:candidatesWithView`, {
          VIEW_NAME: profileViewDisplay?.name,
          PUBLIC_APP_NAME
        })}`
      )
    } else {
      setTitle(`${t(`common:seo:candidates`, { PUBLIC_APP_NAME })}`)
    }
  }, [profileViewDisplay])

  return (
    <>
      <AppHeadMetaTags title={title} />

      <If condition={currentRole?.id}>
        <LayoutGrid>
          {view ? (
            <CandidatesProfileViewDisplayConfig
              currentViewId={Number(view)}
              setProfileViewDisplay={setProfileViewDisplay}>
              {() => {
                return (
                  <CandidatesManagementContainer
                    profileViewDisplay={profileViewDisplay}
                    setProfileViewDisplay={setProfileViewDisplay}
                  />
                )
              }}
            </CandidatesProfileViewDisplayConfig>
          ) : (
            <CandidatesManagementContainer
              profileViewDisplay={profileViewDisplay}
              setProfileViewDisplay={setProfileViewDisplay}
            />
          )}
        </LayoutGrid>
      </If>

      <div id="app-editing-table" className="pointer-events-auto!" />
    </>
  )
}

export default withQueryClientProvider(CandidatesPage)
