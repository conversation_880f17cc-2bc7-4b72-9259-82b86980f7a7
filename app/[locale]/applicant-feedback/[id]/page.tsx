'use client'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useTranslation } from 'react-i18next'

import type { IRouterWithID } from '~/core/@types/global'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'

import { useRouterContext } from '~/lib/next/use-router-context'
import useBoundStore from '~/lib/store'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import ApplicantFeedbackContainer from '~/features/applicant-feedback/[id]'

function ApplicantFeedbackPage() {
  const { t } = useTranslation()
  const queryClientInterviewFeedbackPage = new QueryClient()
  const { params } = useRouterContext()
  const currentRole = useBoundStore((state) => state.currentRole)

  return (
    <>
      <AppHeadMetaTags title={`${t('common:seo:feedback', { PUBLIC_APP_NAME })}`} />

      <If condition={currentRole?.id}>
        <QueryClientProvider client={queryClientInterviewFeedbackPage}>
          <ApplicantFeedbackContainer id={params?.id as IRouterWithID} />
        </QueryClientProvider>
      </If>
    </>
  )
}

export default ApplicantFeedbackPage
