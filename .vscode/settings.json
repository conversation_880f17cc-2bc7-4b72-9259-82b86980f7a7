{"eslint.format.enable": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll": "explicit"}, "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "files.autoSave": "onWindowChange", "editor.formatOnSave": true, "editor.defaultFormatter": "dbaeumer.vscode-eslint", "[javascript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[javascriptreact]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[typescript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[typescriptreact]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "javascript.validate.enable": true, "javascript.suggest.autoImports": true, "javascript.updateImportsOnFileMove.enabled": "always", "javascript.suggest.includeCompletionsForImportStatements": true, "typescript.validate.enable": true, "typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "typescript.suggest.classMemberSnippets.enabled": false, "typescript.updateImportsOnFileMove.enabled": "always", "typescript.suggest.completeFunctionCalls": true, "typescript.suggest.includeCompletionsForImportStatements": true, "tailwindCSS.includeLanguages": {"typescript": "javascript", "typescriptreact": "javascript"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]], "tailwindCSS.validate": true, "tailwindCSS.lint.cssConflict": "warning", "tailwindCSS.lint.invalidApply": "error", "tailwindCSS.lint.invalidScreen": "error", "tailwindCSS.lint.invalidVariant": "error", "tailwindCSS.lint.invalidConfigPath": "error", "tailwindCSS.lint.invalidTailwindDirective": "error", "tailwindCSS.lint.recommendedVariantOrder": "warning", "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html", "javascript": "html", "javascriptreact": "html"}, "emmet.triggerExpansionOnTab": true, "emmet.showSuggestionsAsSnippets": true, "prettier.requireConfig": true}