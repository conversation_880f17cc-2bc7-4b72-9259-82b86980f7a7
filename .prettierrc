{"$schema": "https://json.schemastore.org/prettierrc", "useTabs": false, "tabWidth": 2, "singleQuote": true, "trailingComma": "none", "bracketSpacing": true, "bracketSameLine": true, "semi": false, "endOfLine": "lf", "arrowParens": "always", "printWidth": 120, "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "experimentalTernaries": false, "plugins": ["@trivago/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"], "importOrder": ["<THIRD_PARTY_MODULES>", "^~/(core|hooks|hoc|cookies|configuration|config)(.*)$|^src/(.*)$", "^~/lib/(.*)$", "^~/(features|components)(.*)$", "^(types$)|^(types/(.*)$)", "^~/(.*)$", "^[./]"], "importOrderTypeScriptVersion": "5.0.0", "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"], "importOrderSeparation": true, "importOrderSortSpecifiers": true, "importOrderBuiltinModulesToTop": true, "importOrderCaseInsensitive": true, "importOrderMergeDuplicateImports": true, "importOrderCombineTypeAndValueImports": true, "tailwindConfig": "./tailwind.config.ts", "tailwindFunctions": ["clsx", "cn", "cva", "tw", "twMerge", "twJoin"], "attributeSort": "ASC"}