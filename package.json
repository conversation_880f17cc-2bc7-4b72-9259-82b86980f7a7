{"name": "freec-ats", "version": "1.0.0", "private": true, "scripts": {"analyze": "ANALYZE=true npm run build", "prepare-dev": "npm run fetch-enums && npm run fetch-static-data && npm run copy-firebase-staging", "dev": "next dev --turbo", "build": "npm run fetch-enums && npm run fetch-static-data && npm run copy-firebase-production && next build", "copy-firebase-staging": "cp ./src/firebase/sw/staging/firebase-messaging-sw.js public/", "copy-firebase-production": "cp ./src/firebase/sw/production/firebase-messaging-sw.js public/", "start": "next start", "type-check": "tsc --pretty --noEmit", "lint": "next lint --quiet", "lint:fix": "npx eslint --fix .", "health-check": "npm run lint && npm run type-check", "test-all": "npm run health-check && npm run build", "prepare": "husky install", "killports": "sh scripts/kill-ports.sh", "convert-excel-to-json": "node ./tools/convert-excel-to-json", "fetch-enums": "node ./tools/fetch-enums-data", "fetch-static-data": "node ./tools/fetch-static-data", "security-check": "yarn audit --level high critical"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --cache --max-warnings=0 --fix", "prettier --write"], "*.{json,md,mdx,yml,yaml}": ["prettier --write"], "*.{css,scss}": ["prettier --write"]}, "dependencies": {"@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@fullcalendar/core": "6.1.8", "@fullcalendar/daygrid": "6.1.8", "@fullcalendar/interaction": "6.1.8", "@fullcalendar/react": "6.1.8", "@fullcalendar/resource": "6.1.8", "@fullcalendar/resource-timeline": "^6.1.18", "@fullcalendar/timegrid": "6.1.8", "@hookform/devtools": "4.3.1", "@hookform/error-message": "2.0.1", "@hookform/resolvers": "2.9.11", "@nivo/bar": "0.88.0", "@nivo/core": "0.88.0", "@nivo/funnel": "0.88.0", "@nivo/line": "0.88.0", "@nivo/pie": "0.88.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "1.0.4", "@radix-ui/react-aspect-ratio": "1.0.2", "@radix-ui/react-avatar": "1.0.1", "@radix-ui/react-checkbox": "1.0.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "1.0.2", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "1.0.3", "@radix-ui/react-tabs": "1.0.4", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-visually-hidden": "^1.2.3", "@sentry/nextjs": "^7.120.0", "@tanstack/react-query": "^4.40.0", "@tanstack/react-table": "8.9.2", "@tanstack/react-virtual": "^3.13.12", "@tiptap/core": "2.0.3", "@tiptap/extension-blockquote": "^2.26.1", "@tiptap/extension-bold": "2.0.3", "@tiptap/extension-character-count": "2.0.3", "@tiptap/extension-color": "2.0.3", "@tiptap/extension-document": "2.0.3", "@tiptap/extension-dropcursor": "2.0.3", "@tiptap/extension-gapcursor": "2.0.3", "@tiptap/extension-highlight": "2.0.3", "@tiptap/extension-image": "2.0.3", "@tiptap/extension-link": "2.0.3", "@tiptap/extension-list-item": "2.0.3", "@tiptap/extension-mention": "^2.26.1", "@tiptap/extension-paragraph": "2.0.3", "@tiptap/extension-placeholder": "2.0.3", "@tiptap/extension-table": "^2.26.1", "@tiptap/extension-table-cell": "^2.26.1", "@tiptap/extension-table-header": "^2.26.1", "@tiptap/extension-table-row": "^2.26.1", "@tiptap/extension-text": "2.0.3", "@tiptap/extension-text-align": "2.0.3", "@tiptap/extension-text-style": "2.0.3", "@tiptap/extension-underline": "2.0.3", "@tiptap/pm": "2.0.3", "@tiptap/react": "2.0.3", "@tiptap/starter-kit": "2.0.3", "@tiptap/suggestion": "^2.26.1", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-color": "^3.0.13", "@types/react-dom": "19.1.2", "@vercel/speed-insights": "^1.2.0", "canvas": "^3.1.2", "class-variance-authority": "^0.6.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "commandbar": "^1.14.0", "csrf": "^3.1.0", "date-fns": "2.29.3", "date-fns-tz": "^2.0.1", "docx": "^9.5.1", "docx-preview": "^0.1.20", "dompurify": "^3.2.6", "dotenv": "^16.6.1", "emoji-mart": "^5.6.0", "eslint": "^8.57.0", "firebase": "^10.14.1", "gleap": "^12.1.0", "graphql": "^16.11.0", "husky": "^8.0.3", "i18next": "^22.4.11", "i18next-resources-to-backend": "^1.2.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jszip": "^3.10.1", "jwt-decode": "^3.1.2", "lucide-react": "0.209.0", "next": "15.3.0", "next-auth": "^4.24.9", "next-i18n-router": "^5.5.3", "nookies": "^2.5.2", "path-to-regexp": "^6.3.0", "pdfjs-dist": "^3.10.111", "pino": "^8.21.0", "react": "19.1.0", "react-color": "^2.19.3", "react-cool-onclickoutside": "1.7.0", "react-day-picker": "8.8.0", "react-device-detect": "^2.2.3", "react-dom": "19.1.0", "react-dropzone": "14.2.3", "react-easy-crop": "4.7.5", "react-ga4": "^2.1.0", "react-hook-form": "7.43.5", "react-hot-toast": "^2.5.2", "react-i18next": "^15.7.3", "react-idle-timer": "^5.7.2", "react-image-file-resizer": "^0.4.8", "react-markdown": "^10.1.0", "react-phone-input-2": "2.15.1", "react-select": "5.7.0", "react-share": "^4.4.1", "react-spring": "9.7.1", "react-top-loading-bar": "^2.3.1", "react-virtual": "2.10.4", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "remark-gfm": "^4.0.1", "swr": "^2.3.4", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "tippy.js": "^6.3.7", "typescript": "^5.0.0", "urql": "^4.1.0", "zod": "3.21.4", "zustand": "^4.3.6"}, "devDependencies": {"@babel/core": "^7.28.0", "@hello-pangea/dnd": "^18.0.1", "@next/bundle-analyzer": "15.3.0", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/dompurify": "^3.2.0", "@types/google.maps": "^3.58.1", "@types/jspdf": "^2.0.0", "@types/react-window": "^1.8.8", "@types/react-window-infinite-loader": "^1.0.9", "@typescript-eslint/eslint-plugin": "^8.40.0", "@typescript-eslint/parser": "^8.40.0", "autoprefixer": "^10.4.21", "babel-loader": "^8.4.1", "eslint-config-next": "^15.0.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-better-tailwindcss": "^3.7.5", "eslint-plugin-prettier": "^5.5.4", "fs": "^0.0.1-security", "postcss": "^8.5.6", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.11", "typescript": "^5.0.0"}, "resolutions": {"webpack": "^5", "@types/react": "19.1.1", "@types/react-dom": "19.1.2"}, "engines": {"node": "22.x"}, "readme": "ERROR: No README data found!", "_id": "freec-ats@1.0.0"}