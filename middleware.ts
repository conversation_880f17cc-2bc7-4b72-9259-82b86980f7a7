import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { enhanceI18nResponse, processI18nMiddleware } from './src/lib/i18n/i18nMiddleware'

export async function middleware(request: NextRequest) {
  try {
    // Security: HTTPS Enforcement
    if (request.nextUrl.protocol === 'http:' && process.env.NODE_ENV !== 'development') {
      return NextResponse.redirect(`https://${request.nextUrl.hostname}${request.nextUrl.pathname}`)
    }

    console.log('Processing middleware for:', request.nextUrl.pathname)

    // Process i18n routing
    const i18nResponse = await processI18nMiddleware(request)

    // If i18n returns a redirect, handle it properly
    if (i18nResponse.status >= 300 && i18nResponse.status < 400) {
      console.log('i18n redirect detected')
      return enhanceI18nResponse(i18nResponse)
    }

    // Create enhanced response with custom headers
    const requestHeaders = new Headers(request.headers)
    requestHeaders.set('x-pathname', request.nextUrl.pathname)
    requestHeaders.set('x-search-params', request.nextUrl.searchParams.toString())
    requestHeaders.set('x-url', request.url)

    // Return the i18n response with enhanced headers
    const finalResponse = enhanceI18nResponse(i18nResponse)

    // Add custom headers to the final response
    finalResponse.headers.set('x-pathname', request.nextUrl.pathname)
    finalResponse.headers.set('x-search-params', request.nextUrl.searchParams.toString())
    finalResponse.headers.set('x-url', request.url)

    return finalResponse
  } catch (error) {
    console.error('Middleware Error:', error)
    return NextResponse.next()
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - files with extensions (images, css, js, etc.)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\..*).*)'
  ]
}
