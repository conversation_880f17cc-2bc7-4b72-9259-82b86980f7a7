{"user_add_job": "User added job", "user_update_job_status": "User updated Job Status", "user_add_job_note": "User added job note", "user_update_job_info": "User updated job", "user_assign_job_recruiter": "User added <PERSON><PERSON><PERSON><PERSON> to Job", "user_assign_job_hiring_manager": "User added Hiring manager to Job", "user_assign_job_hiring_member": "User added Hiring member to Job", "user_change_job_role": "User updated hiring role of member", "user_remove_job_recruiter": "User removed <PERSON><PERSON><PERSON><PERSON> from job", "user_remove_job_hiring_manager": "User removed Hiring manager from Job", "user_remove_job_hiring_members": "User removed Hiring member from Job", "user_edit_hiring_pipeline": "User updated hiring process of the Job", "user_remove_job_note": "User removed a job note", "candidate_apply": "User applied for a job", "user_sourced_candidate": "User sourced candidate to Job", "user_add_candidate": "User added a new candidate", "user_move_stage_candidate": "User moved stage applicant", "user_disqualify_candidate": "User disqualified the applicant", "user_requalify_candidate": "User re-qualified the applicant", "user_create_task": "User added a task", "user_assigned_task": "User assigned the task", "user_complete_task": "User completed a task", "user_delete_task": "User removed a task", "user_add_profile_note": "User added a note", "user_add_applicant_note": "User added applicant note", "user_remove_applicant_note": "User removed applicant note", "user_remove_profile_note": "User removed a note", "user_setup_interview": "User scheduled the interview", "user_reschedule_interview": "User re-scheduled the interview", "user_cancel_interview": "User cancelled the interview", "user_refer_candidate": "User referred the candidate", "user_add_candidate_by_referral": "User referred the candidate", "user_submit_interview_feedback": "User gave an evaluation to interview", "user_update_interview_feedback": "User updated feedback interview", "user_give_feedback_interview": "User gave an evaluation to interview", "user_update_feedback_interview": "User updated feedback interview", "user_submit_requisition": "User submitted requisition", "user_delete_requisition": "User deleted requisition", "user_approve_requisition": "User approved requisition", "user_reject_requisition": "User rejected requisition", "user_create_job_requisition": "User created job from requisition", "user_send_self_schedule_interview": "User scheduled self interview", "user_cancel_self_schedule_interview": "User cancelled self schedule interview", "user_add_feedback_without_interview": "User added feedback", "user_update_feedback_without_interview": "User updated feedback", "user_delete_feedback_without_interview": "User deleted feedback", "user_delete_feedback_with_interview": "User delete feedback from Interview", "user_delete_interview_feedback": "User delete feedback from Interview", "profile_add_file": "User uploaded file", "user_update_file_permission": "User updated file permission", "user_delete_file": "User deleted file", "profile_update_open_to_work": "User updated Open to work", "user_agreed_terms_and_conditions": "User agreed terms and conditions", "user_create_employee_profile": "User activated employee profile", "user_add_company": "User added a new company", "user_update_company_status": "User updated a company status", "user_delete_company": "User deleted a company", "user_add_company_note": "User added a note in company", "user_remove_company_note": "User removed a note in company", "user_change_company_ownership": "User changed company ownership", "user_create_placement": "User created placement", "user_edit_placement": "User edited placement", "user_delete_placement": "User deleted placement", "user_add_company_contact": "User added company contact", "user_remove_company_contact": "User removed company contact", "user_delete_contact_from_tenant": "User deleted company contact", "user_send_email_to_client": "User sent the candidate", "user_invite_job_client_contact": "User invited client contact to job", "user_remove_job_client_contact": "User removed client contact from job", "user_created_self_profile": "User created self profile", "user_create_talent_pool": "User created talent pool", "user_change_pool_status": "User changed talent pool status", "user_assign_talent_pool_member": "User assigned member to talent pool", "user_remove_talent_pool_member": "User removed member in talent pool", "user_add_talent_pool_note": "User added a note to talent pool", "user_remove_talent_pool_note": "User removed a note talent pool", "user_archive_talent_pool": "User archived the talent pool", "user_retrieve_talent_pool": "User retrieved the talent pool", "user_add_profile_talent_pool": "User added candidate to talent pool", "user_remove_profile_talent_pool": "User removed candidate from talent pool", "user_marked_as_hire": "User marked candidate as hired", "user_edit_hire_info": "User edited candidate hired info", "user_login": "User Login", "user_logout": "User <PERSON>", "user_sign_up": "User Signup", "user_verified_email": "User verified email", "user_invite_member": "User invited new member", "user_resent_invitation": "User resent the invitation", "user_cancel_invitation": "User cancelled the invitation", "user_joined_tenant": "User joined the company", "user_updated_role_member": "User updated level access of member", "user_removed_member": "User removed member", "user_update_tenant_name": "User updated company name", "user_update_tenant_logo": "User uploaded company logo", "user_import_job": "User imported job", "user_import_candidate": "User imported cadidates", "user_enable_feature": "User enabled the feature", "user_disable_feature": "User disabled the feature", "user_assign_department": "User assigned the department", "user_unassign_department": "User unassigned the department", "user_enable_permission": "User enabled the permission", "user_disable_permission": "User disabled the permission", "user_update_referral_setting": "User updated referral setting", "user_add_requisition_flow": "User added requisition flow", "user_update_requisition_flow": "User updated requisition flow", "user_delete_requisition_flow": "User deleted requisition flow", "user_update_career_setting": "User updated career setting", "user_update_term_condition": "User updated terms and conditions", "user_create_additional_field": "User created an additional field", "user_delete_additional_field": "User deleted an additional field", "user_update_additional_field": "User updated an additional field", "user_update_candidate_field_visibility": "User updated candidate's field visibility", "user_update_job_field_visibility": "User updated job's field visibility", "user_update_candidate_field_visible": "User updated candidate's field visbile. ", "user_create_location": "User created the location", "user_update_location": "User update the location", "user_delete_location": "User deleted the location", "user_transfer_location": "User transferred the location", "user_create_department": "User created the department", "user_update_department": "User updated the department", "user_delete_department": "User deleted the department", "user_moved_department": "User moved the department", "user_create_profile_tag": "User created candidate's tag", "user_update_profile_tag": "User updated candidate's tag", "user_delete_profile_tag": "User deleted candidate's tag", "user_create_job_tag": "User created job's tag", "user_update_job_tag": "User updated job's tag", "user_delete_job_tag": "User deleted jobs's tag", "user_create_email_template": "User created the email template", "user_update_email_template": "User updated the email template", "user_delete_email_template": "User deleted the email template", "user_create_disqualify_reason": "User created the disqualify reason", "user_update_disqualify_reason": "User updated the disqualify reason", "user_delete_disqualify_reason": "User deleted the disqualify reason", "user_create_hiring_pipeline": "User created the hiring pipeline", "user_update_hiring_pipeline": "User updated the hiring pipeline", "user_delete_hiring_pipeline": "User deleted the hiring pipeline", "user_create_interview_kit": "User created the interview kit", "user_update_interview_kit": "User updated the interview kit", "user_delete_interview_kit": "User deleted the interview kit", "user_create_profile_template": "User created the profile template", "user_update_profile_template": "User updated the profile template", "user_delete_profile_template": "User deleted the profile template", "user_update_candidate_field_profile": "User updated candidate's field profile", "user_update_job_additional_field": "User updated a job additional field", "user_delete_job": "User deleted the job", "user_delete_profile": "User deleted candidate profile", "profile_insert_ai_summary": "Insert summary using AI", "profile_regenerate_ai_summary": "Edit summary using AI", "profile_resume_ai_reparser": "Re-parse resume", "user_update_career_page_builder": "User updated the Career Page Builder.", "chub_invite_member": "User invites a new Career Hub member", "chub_resent_invitation": "User resent the invitation to the career hub", "chub_cancel_invitation": "User canceled the invitation to the career hub", "chub_member_joined": "Career hub member joined", "chub_removed_member": "User removed a member from the career hub", "company_add_file": "User uploaded company file", "user_delete_company_file": "User deleted company file", "user_create_team": "Use<PERSON> created the team", "user_update_team": "User updated the team", "user_delete_team": "User deleted the team", "user_move_team": "User moved the team", "user_export_candidate_listing": "User exported candidate listing", "user_export_candidate_template_listing": "User exported candidate template listing", "user_export_report_data": "User exports report data", "user_used_credit_to_get_info": "User has used a credit to get contact info", "user_uploaded_cv": "User has uploaded a resume/cv.", "contact_call_log": "User called the contact", "candidate_call_log": "User called the candidate", "user_send_email_to_candidate": "User sent an email to the candidate.", "user_create_position": "User created a position", "user_edit_position": "User edited the position", "user_delete_position": "User deleted the position", "user_send_email_to_profile": "User sent an email to the profile.", "user_add_new_ip_whitelisting": "User added the IP(s) whitelisting", "user_enable_ip_whitelisting": "User enabled the IP whitelisting", "user_disable_ip_whitelisting": "User disabled the IP whitelisting", "user_remove_ip_whitelisting": "User removed the IP(s) whitelisting", "user_create_tenant_course": "User created a course", "user_edit_tenant_course": "User edited a course", "user_delete_tenant_course": "User deleted a course", "user_import_tenant_course": "User imported courses", "user_update_course_status": "User updated course status", "user_enable_hiring_portal": "User enabled clients setting.", "user_disable_hiring_portal": "User disabled clients setting.", "user_export_skill_listing_count": "User exported a list of {{number}} skills from Skills management settings", "user_export_skill_listing": "User exported skill listing", "user_export_job_listing": "User exported jobs listing.", "user_create_career_path": "User created a career path", "user_edit_career_path": "User edited a career path", "user_delete_career_path": "User deleted a career path"}