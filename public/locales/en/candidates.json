{"CreateCandidateModal": {"title": "Add candidate", "success": "Candidate added!", "buttonViewDetail": "View detail", "buttonViewProfile": "View profile"}, "candidateTable": {"tooltips": {"internal": "Internal"}, "emptyData": {"title": "No candidates", "description": "Looks like you don't add any candidates!", "descriptionForLimitedMember": "Currently, there are no candidates available for you. Please check back later for updates.", "descriptionTalentPool": "Looks like your team don’t add any candidates!", "buttonAddCandidate": "Add candidate"}, "emptySearch": {"title": "No results found", "description": "There are no results that match your search.", "buttonClear": "Clear filters"}, "matchRank": "Match", "fullName": "Full name", "departments": "Departments", "preferredWorkStates": "Preferred locations", "email": "Email", "headline": "Position", "links": "Links", "location": "Location", "phoneNumber": "Phone number", "jobs": "Jobs", "stage": "Stage", "resume": "CV/Resume", "tag": "Tags", "skill": "Skills", "createAt": "Created at", "lastActivity": "Last activity", "countJobs": "{{count}} jobs", "multipleStage": "Multiple", "owner": "Owner", "heading": "Candidates", "filter": {"search": "Search", "job": "Job", "stage": "Stage"}, "talentPools": "Talent pools", "summary": "Summary", "skills": "Skills", "openToWork": "Open to work", "languages": "Languages", "nationality": "Nationality", "birthday": "Birthday", "willingToRelocate": "Willing to relocate", "noticePeriod": "Notice of period", "currentSalary": "Current salary", "expectedSalary": "Expected salary", "experienceLevel": "Experience level", "yearsOfExperience": "Years of experience", "publicId": "ID", "workExperiences": "Work experiences", "educations": "Education"}, "candidateSendMail": {"composeEmail": "Compose email", "viewJobDetail": "view job detail", "pleaseSelectJob": "Please select job.", "send": "Send", "others": "+{{num}} others", "candidate": "Candidate", "emailSent": "<PERSON><PERSON> sent.", "to": "To:", "cc": "Cc:", "bcc": "Bcc:", "empty": {"title": "No emails", "description": "Send your first email and keep tracking the email history here.", "compose": "Compose"}, "candidateName": "Candi<PERSON>'s name"}, "rejectCandidate": {"disqualifiedCandidate": "Disqualify candidate", "scheduleEmail": "Schedule email", "scheduleDisqualifyEmail": "Schedule disqualify email", "selectDateTime": "Select date & time to schedule ({{timezone}}).", "candidatesLackEmail": {"title": "Some candidates lack email addresses", "description": "Applicants without email will still be disqualified but will not receive a disqualification email."}, "selectedCandidatesDoNotHaveEmail": "The selected candidates do not have email addresses available for notifications."}, "candidateProfileHeader": {"candidateRequalified": "Candidate requalified!", "profile": "Profile", "jobRelated": "Job-Related", "disqualified": "Disqualified", "candidateProfile": "Candidate profile", "interview": "Interview", "email": "Email"}, "candidateProfile": {"adjustProfilePicture": "Adjust profile picture", "jobs": "Jobs", "overview": "Overview", "interview": "Interview", "email": "Email", "activities": "Activities", "notes": "Notes", "tasks": "Tasks"}, "tabs": {"overview": "Overview", "files": "Files", "jobs": "Jobs", "interview": "Interview", "email": "Email", "activities": "Activities", "notes": "Notes", "tasks": "Tasks", "header": {"profile": "Profile", "jobRelated": "Job-Related", "candidateProfile": "Candidate profile", "upgradePlan": "Upgrade plan to use"}, "candidateNote": {"addNotePlaceholder": "Write a note here...", "empty": {"title": "No notes yet", "description": "Take notes about this  candidate to keep track of important info.", "create": "Create note"}, "hiringMemberSelect": {"defaultName": "Everyone", "defaultDescription": "Anyone with access to this candidate"}, "savingNote": "Saving note", "noteCreated": "Note created.", "noteUpdated": "Note updated", "noteDeleted": "Note deleted.", "attachmentDeleted": "Attachment deleted.", "attachmentFailed": "Attachment failed", "max10Files": "Maximum 10 files can be uploaded.", "everyone": "Everyone", "selectAtLeastAssignee": "Please select at least 1 assignee.", "visibleTo": "Visible to", "followUpTask": "Follow up task", "dueDate": "due date", "confirmDelete": {"title": "Delete note", "description": "You are about to delete the note. This action can not be undone."}}, "candidateOverview": {"viewMore": "View more", "readMore": "Read more", "notAvailable": "Not available", "contactNotAvailable": "Application details not shared. For more information, please contact your representative.", "visibleTo": "Visible to {{role}}", "addTag": "Add tag", "tooltipAddTag": "Maximum 10 tags", "search": "Search", "saveEnter": "Save (Enter)", "contactDetails": {"job": "Job", "heading": "Contact details", "email": "Email", "addEmail": "Add email", "phoneNumber": "Phone number", "addPhoneNumber": "Add phone number", "location": "Location", "addLocation": "Add location", "links": "Links", "addLinks": "Add link", "owner": {"search": "Search"}, "maximumLinks": "Maximum {{number}} links"}, "summary": {"heading": "Summary", "addSummary": "Add candidate's summary"}, "referral": {"heading": "Referrals Information"}, "resumeCv": {"heading": "Resume/CV", "deleteConfirm": {"title": "Delete CV", "description": " Are you sure you want to delete? This action cannot be undone."}, "re_parser_title": "Re-parse", "re_parser_description": "All fields in profile will be auto mapped and replaced with data from the CV/Resume. Are you sure you want to continue?", "data_successfully_parsed": "Data successfully parsed", "failed_to_parse": "Failed to parse data"}, "coverLetter": {"heading": "Cover letter"}, "profileInformation": {"heading": "Profile information", "sourced": "Source", "sourcedName": "Source detail", "selectSource": "Select source", "selectJob": "Select", "skills": "Skills", "addSkills": "Add skills", "languages": "Languages", "selectLanguages": "Select language", "nationality": "Nationality", "selectCountry": "Select country", "inputCountry": "Input country", "sourcedBy": "Sourced by", "createdBy": "Created by", "createdAt": "Created at", "selectUser": "Select user", "currentSalary": "Current salary", "typeOfCurrentSalaryMonthly": "Monthly", "typeOfCurrentSalaryAnnual": "Annual", "typeOfCurrentSalary": "Salary", "typeOfExpectedSalary": "typeOfExpectedSalary", "addCurrentSalary": "Add current salary", "expectedSalary": "Expected salary", "addExpectedSalary": "Add expected salary", "willingToRelocate": "Willing to relocate", "selectLocation": "Select location", "noticeOfPeriods": "Notice of periods", "selectTimePeriod": "Select or type your option", "selectTotalYearOfExp": "Select", "birthday": "Birthday", "selectBirthday": "Select", "owner": "Owner", "totalYoe": "Years of experience", "headline": "Headline", "addHeadline": "Add headline", "talentPools": "Talent pools", "talentPoolsNew": "Talent pools", "addTalentPool": "Add Talent Pool", "addTalentPoolNew": "Add Talent Pool", "today": "Today", "tomorrow": "Tomorrow", "hiredForJob": "<0>Hired</0> for the job <1>{{jobTitle}}</1> by <0>{{hiredBy}}</0>", "openToWork": "Open to work", "position": "Position", "department": "Departments", "preferredWorkStates": "Preferred locations", "addPreferredWorkStates": "Add preferred locations", "add_position": "Add position", "experienceLevel": "Experience level", "selectExperienceLevel": "Select experience level", "formatSupportBirthday": "{{date}} <0>({{years_old}} years old)</0>", "summary": "Summary", "noticePeriod": "Notice of period", "yearsOfExperience": "Years of experience", "publicId": "ID"}, "workExperiences": {"heading": "Work experiences", "addExperiences": "Add work experience", "editExperiences": "Edit work experience", "jobTitleLabel": "Job title", "jobTitlePlaceholder": "Input job title", "companyNameLabel": "Company name", "companyNamePlaceholder": "Input company name", "locationLabel": "Location", "locationPlaceholder": "Input location", "description": "Description", "descriptionPlaceholder": "Provide a description of the candidate's role and responsibilities. Highlight key achievements, skills utilized, and notable contributions.", "from": "Start date", "to": "End date", "selectDate": "Select date", "deleteTitle": "Delete work experience", "deleteDescription": "Are you sure you want to delete <0>{{title}}</0>? This action cannot be undone.", "month": "Month", "year": "Year", "currentWorkingHere": "Currently working here", "present": "Present", "currentlyWorkingHere": "Currently working here", "lessThan1Year": "Less than 1 year", "moreThan1Year": "more than 1 month", "months": "months", "years": "years", "above50Years": "Above 50 years", "sortNewestFirst": "Sort newest first"}, "education": {"heading": "Education", "addEducation": "Add education", "editEducation": "Edit education", "schoolNameLabel": "School name", "schoolNamePlaceholder": "Input school name", "degreeLabel": "Degree", "degreePlaceholder": "Select degree", "majorLabel": "Major", "majorPlaceholder": "Eg. Graphic Design", "description": "Description", "descriptionPlaceholder": "Provide a brief overview of the candidate's academic achievements, relevant coursework, honors, research projects, or any other pertinent details related to their education.", "from": "Start date", "to": "End date", "selectDate": "Select date", "deleteTitle": "Delete education", "deleteDescription": "Are you sure you want to delete <0>{{title}}</0>? This action cannot be undone.", "month": "month", "months": "months", "year": "year", "years": "years", "moreThanMonth": "more than 1 month", "lessThanYear": "Less than 1 year"}, "references": {"heading": "References", "addReferences": "Add references", "editReferences": "Edit references", "nameLabel": "Reference's name", "namePlaceholder": "Input references's name", "emailLabel": "Reference's email", "emailPlaceholder": "Input references's email", "deleteTitle": "Delete reference", "deleteDescription": "Are you sure you want to delete <0>{{title}}</0>? This action cannot be undone."}, "certificates": {"heading": "Certifications", "addCertificate": "Add certificate", "editCertificate": "Edit certificate", "nameLabel": "Certificate", "namePlaceholder": "Input certificate", "institutionLabel": "Institution", "institutionPlaceholder": "Input institution", "issuedDateLabel": "Issued date", "deleteTitle": "Delete certificate", "deleteDescription": "Are you sure you want to delete <0>{{title}}</0>? This action cannot be undone."}, "languages": {"addLanguage": "Add", "editLanguage": "Edit language", "languageLabel": "Languages", "languagePlaceholder": "Select language", "levelLabel": "Level", "levelPlaceholder": "Select level", "language_added": "Language added", "language_updated": "Language updated", "language_deleted": "Language deleted"}, "addSection": "Add section", "link": {"deleteTitle": "Delete link", "deleteDescription": "Are you sure you want to delete <0>{{link}}</0>? This action cannot be undone.", "max15Links": "Maximum 15 links"}}, "candidateJob": {"labelEmptyJob": "No jobs", "descriptionEmptyJob": "Click on the 'Assign Job' button below to view detailed applicant information relevant to specific job openings."}, "candidateInterview": {"labelEmptyInterview": "No interviews", "descriptionEmptyInterview": "Start scheduling interviews to track the candidate's interview history and view details here."}, "candidateEmail": {"candidateEmailRequired": "Candidate's email required", "candidateEmailProceed": "To proceed, please provide the candidate's email address", "labelEmptyEmail": "No emails", "descriptionEmptyEmail": "Send your first email and keep tracking the email history here.", "placeholdersAreNotSupportedFull": "Placeholders are not supported: [{{items}}]. {{name}}", "placeholdersAreNotSupported": "Placeholders are not supported: [{{items}}]"}, "candidateFeedback": {"noFeedbackTitle": "No feedback", "noFeedbackDescription": "Submit feedback about this candidate to keep track of important info.", "overallScore": "Overall score", "pendingFeedback": "Pending feedback", "feedbackAlert": "How was your <span class='font-medium'>{{eventTypeDescription}}</span> for this candidate on <span class='font-medium'>{{fromDatetime}}</span>?", "addNewEvaluation": "Add new evaluation", "completeFeedback": "Complete feedback"}, "candidateRecommendation": {"recommendation": "Recommendation", "level": "Level", "emptyJob": {"title": "No job recommendations found", "description": "To match the candidate with suitable jobs, please provide their position, level, expected salary, skills and languages."}, "detailModal": {"title": "Match criteria details", "description": "How the criteria of <0>{{job_title}}</0> match with your profile ", "updateProfile": "Update your profile", "upSkills": "Upskills"}, "jobSalaryHelpText": "Job's salary is {{salary}}"}, "candidateCareer": {"careerGoal": "Career goal", "career": "Career", "nextCareerGoal": "Next career goal", "currentSkills": "Current skills", "neededSkills": "Needed skills", "emptyCareer": {"title": "No career goal", "description": "Looks like no career goal has been set yet."}}}, "placeholder": {"addHeadline": "Add headline", "addTalentPool": "Add Talent Pool", "clickToEnterValue": "Click to enter a value", "clickToEnterCompany": "Click to enter a company name", "searchByJobTitle": "Search by job title", "searchByJobs": "Search jobs", "inputCandidateEmail": "Input candidate's email", "inputEmailSubject": "Input email subject", "inputEmailContent": "Input email content"}, "notifyCandidateByEmail": "Notify candidate by email", "notifyCandidateSourcedByUser": "The candidate was sourced by <span class='font-medium text-gray-900'> {{name}} </span> and has not applied for the job", "fileUploadCompleted": "{{fileUpload}} of {{files}} files uploaded.", "descriptionFileUploadCompleted": "After uploading, the parsing process will be run, extracting data from the CVs and added into the system.", "sendToClient": {"title": "Send candidate", "submitToClient": "Submit to client", "sendEmailNotification": "Send email notification", "sendEmailDescription": "You’re about to move this candidate to Client submission stage. Do you want to notify the client via email?"}, "feedback": {"strong_no": "Strong no", "no": "No", "yes": "Yes", "strong_yes": "Strong yes", "pending_feedback": "Pending feedback"}, "lastActivity": {"desc": "Newest first", "asc": "Oldest first"}, "trackingIP": {"desc": "A -> Z", "asc": "Z -> A"}, "publicIdOrder": {"desc": "Descending", "asc": "Ascending"}, "fullNameOrder": {"desc": "Z -> A", "asc": "A -> Z"}, "userSettingsDisplay": {"fullName": "Full name", "headline": "Position", "departments": "Departments", "preferredWorkStates": "Preferred locations", "email": "Email", "phoneNumber": "Phone number", "location": "Location", "links": "Links", "cv": "CV/Resume", "tags": "Tags", "owner": "Owner", "jobs": "Jobs", "stage": "Stage", "createdAt": "Created at", "lastActivity": "Last activity", "disqualifyCandidates": "Disqualify candidates", "archivedJobs": "Archived jobs", "talentPools": "Talent pools", "summary": "Summary", "skills": "Skills", "openToWork": "Open to work", "languages": "Languages", "nationality": "Nationality", "birthday": "Birthday", "willingToRelocate": "Willing to relocate", "noticeToPeriodDays": "Notice of period", "currentSalary": "Current salary", "expectedSalary": "Expected salary", "profileLevel": "Experience level", "totalYearsOfExp": "Years of experience", "publicId": "ID", "workExperiences": "Work experiences", "educations": "Education"}, "yoeOptions": {"0": "Less than one year", "1": "1 year", "2": "2 years", "3": "3 years", "4": "4 years", "5": "5 years", "6": "6 years", "7": "7 years", "8": "8 years", "9": "9 years", "10": "10 years", "11": "11 years", "12": "12 years", "13": "13 years", "14": "14 years", "15": "15 years", "16": "16 years", "17": "17 years", "18": "18 years", "19": "19 years", "20": "20 years", "21": "21 years", "22": "22 years", "23": "23 years", "24": "24 years", "25": "25 years", "26": "26 years", "27": "27 years", "28": "28 years", "29": "29 years", "30": "30 years", "31": "31 years", "32": "32 years", "33": "33 years", "34": "34 years", "35": "35 years", "36": "36 years", "37": "37 years", "38": "38 years", "39": "39 years", "40": "40 years", "41": "41 years", "42": "42 years", "43": "43 years", "44": "44 years", "45": "45 years", "46": "46 years", "47": "47 years", "48": "48 years", "49": "49 years", "50": "50 years", "51": "Above 50 years"}, "bulkDeleteCandidates": "You're about to delete <0>{{number}}</0> candidate(s) and all related data. The action cannot be undone. Are you sure you want to proceed?", "deleteCandidates": "Delete candidates", "candidateInformation": "Candidate information", "filterOperator": {"and": "AND", "or": "OR", "where": "WHERE"}, "filterOperatorCondition": {"equal": "Equal", "greater_than": "Greater than", "less_than": "Less than", "contains": "Contains", "is": "Is", "or": "Or", "and": "And", "all": "All", "any_of": "Any of", "is_not": "Is not", "not_contains": "Not contains", "is_any_of": "Is any of", "is_none_of": "Is not", "is_empty": "Is empty", "is_not_empty": "Is not empty", "range": "Is between", "any_of_all": "Any of/all"}, "profileViewDisplay": {"myViews": "My views", "otherViews": "Other views", "allCandidates": "All candidates", "form": {"state": {"public": "Public", "private": "Private"}}, "modal": {"modalCreated": {"title": "New view"}, "modalUpdated": {"title": "Edit view"}, "modalDeleted": {"title": "Delete view", "description": "Are you sure you want to delete <0>{{profileView}}</0>? The action cannot be undone."}}}, "exportLogContent": {"csv": "User exported a list of {{number}} candidates from the {{viewName}} view", "pdf": "User exported a list of {{number}} candidates using the {{templateName}} template"}, "modalExportPDF": {"title": "Export PDF", "description": "Choose a template to export <0>{{number}}</0> profiles with the right information for your needs.", "includeCandidateName": "Include candidate name in PDF filename"}, "callLogs": {"modal": {"update": {"title": "Edit call log"}, "create": {"title": "Log call"}}, "update": {"toastSuccess": "Call log updated", "toastFailed": "Call log updated"}, "delete": {"title": "Delete call log", "description": "Are you sure you want to delete <b>{{direction}}</b>? This action cannot be undone.", "toastSuccess": "Call log deleted!", "toastFailed": "Call log delete failed!"}}, "aiSuggestsSkills": "List key skills or certifications. Use 'AI Suggests' for relevant recommendations.", "headerTable": {"name": "Name", "email": "Email", "date": "Date", "education": "Education", "placeOfEmployment": "Place of employment and job details", "certificate": "Certificate - Institution"}}