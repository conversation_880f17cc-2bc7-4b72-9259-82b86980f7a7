{"sidebar": {"jobs": "Jobs", "referrals": "Referrals", "myJobs": "My jobs", "profile": "Profile", "careers": "Careers", "myCareers": "My career", "learningLibrary": "Learning library"}, "referral_candidate": "Refer candidate", "open_jobs": {"title": "All open jobs", "end_list": "You've reached the end of the list!", "empty_search": {"title": "No results found", "description": "There are no results that match your search."}, "empty_data": {"title": "No jobs", "description": "Looks like your company is not hiring at the moment!"}, "share": {"title": "Share job via Career page", "titleInternal": "Share job via Career Hub", "description": "Everyone can view the job on the company’s career page.", "descriptionInternal": "Use this link to share the job internally with your team.", "disabled_button_tooltip": "You cannot share this job because your company has not made it public. ", "copy_link": "Copy link", "copy_button": "Copy", "or_share_on": "Or share on social"}}, "my_referrals": {"title": "My referrals", "empty_data": {"title": "No referrals", "description": "Looks like you don’t make any referrals!"}, "notHired": "Not hired", "inProcess": "In-process", "hired": "<PERSON><PERSON>", "referredFor": "Referred for"}, "referral_modal": {"title": "Refer candidate", "description": "Refer someone who would be a good fit for the job", "full_name": "Full name", "full_name_placeholder": "e.g. <PERSON>", "email_placeholder": "e.g. <EMAIL>", "phone_number": "Phone number", "resume_cv": "Resume/CV", "introduction": "Introduction", "introduction_placeholder": "How do you know the person? What makes them a good fit for the job?", "job": "Job", "drag_n_drop_type_files": "PDF, DOCX, DOC up to 10MB", "add_button": "Add", "form": {"wrong_upload_file_format": "Only supported PDF, DOCX, DOC. Maximum size is 10MB."}}, "referral_link": {"title": "Referral link", "view_policy": "View policy", "description": "Applying through your link automatically attributes the application to you.", "linkCopied": "Link copied"}, "detail": {"btn_apply": "Apply", "btn_applied": "Applied", "btn_refer": "<PERSON><PERSON>", "reward_referral": "Reward/successful referral", "criteria_of_this_job": "How the criteria of this job match with your profile", "remote_status_label": "REMOTE STATUS", "job_id_label": "JOB ID"}, "filter": {"all_jobs_text": "All job", "applied_jobs_text": "Applied jobs", "not_applied_text": "Not applied", "title": "Filters", "department": "Department", "level": "Level", "tag": "Tag", "applied_not": "Applied/not", "location": "Location", "talent_pool": "Talent pool", "remoteStatus": "Remote status", "company": "Company"}, "tooltip": {"applied_tooltip_disable": "Applied {{date}}"}, "myJobs": {"tabs": {"savedJobs": {"title": "Saved jobs", "empty": {"title": "No saved jobs", "description": "To save a job for later, click the Save icon on the job."}}, "appliedJobs": {"title": "Applied jobs", "empty": {"title": "No applied jobs", "description": "Looks like you haven’t applied for any jobs yet."}}}}, "careerNavigator": {"myCareerGoalTitle": "My career goal", "myCareerGoalDescription": "Define my career aspirations and explore learning paths to achieve them.", "emptyStateTitle": "Set your career goal", "emptyStateDescription": "Define your career aspirations and explore learning paths to achieve them.", "addCareerGoal": "Add career goal", "careerGoalModalTitle": "Career goal", "skillsYouHave": "Skills you have", "nextCareerHub": "Next career goal", "skillDevelopment": "Skill development", "yourGoal": "I want to be <span class='font-medium'>{{positionName}}</span>", "careerGoalSaved": "Career goal saved.", "notAvailable": "Not available", "skillsYouNeed": "Skills you need", "allSkillsYouHave": "All the skills you have", "emptySkillsPosition": "No more skills to add—you've covered them all!", "recommendedCourses": "Recommended courses", "emptyRecommendedCourses": "No recommended courses available.", "statusCourses": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "expert": "Expert"}, "tabs": {"analysis": "Analysis", "jobs": "Jobs"}, "noOverviewAvailabel": "This position hasn’t been filled yet.", "aiAnalysis": "AI analysis", "jobOpenings": "Job openings"}, "newestToOldest": "Newest to Oldest", "oldestToNewest": "Oldest to Newest", "learningLibrary": {"emptySearch": {"title": "No results found", "description": "There are no results that match your search."}, "emptyData": {"title": "No courses", "description": "It looks like there’s no course to show right now."}, "skills": "Skills", "price": "Price", "language": "Language", "description": "Description", "durationFormat": "{{hour}}h{{minute}}m{{second}}s", "hour": "h", "minute": "m", "second": "s"}, "mostRelevant": "Most relevant", "analysis": {"generating": "Generating analysis...", "regenerate": "Regenerate", "retry": "Retry", "errorTitle": "Analysis Error", "generationError": "Failed to generate analysis. Please try again.", "profileNotFound": "Profile not found", "createProfile": "Create Profile", "completeProfileTitle": "Complete Your Profile", "completeProfileDescription": "To get AI career insights, please complete your profile with the required information.", "completeProfile": "Complete Profile", "missingProfileData": "Profile data is incomplete. Please complete your profile first.", "missingFields": {"skills": "Skills", "title": "Current Position", "workExperience": "Work Experience", "education": "Education", "certifications": "Certifications"}, "empty": {"title": "No data", "description": "Complete your profile with title, skills and work experience to get AI career insights.", "updateProfile": "Update profile"}, "header": "Here’s a detailed {{position}} based on your profile:", "errorAnalysis": "Failed to generate analysis. Please try again.", "noContent": "No content available."}}