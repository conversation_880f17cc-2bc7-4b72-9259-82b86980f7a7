{"CreateCandidateModal": {"title": "候補者を追加", "success": "候補者を追加しました！", "buttonViewDetail": "詳細を表示", "buttonViewProfile": "プロフィールを見る"}, "candidateTable": {"tooltips": {"internal": "内部"}, "emptyData": {"title": "候補者なし", "description": "候補者はいないようです。", "descriptionForLimitedMember": "現在、あなたが確認できる候補者はいないようです。アップデート情報があるまでお待ちください。", "descriptionTalentPool": "あなたのチームには候補者が追加されていないようです。", "buttonAddCandidate": "候補者を追加"}, "emptySearch": {"title": "結果が見つかりません", "description": "検索に一致する結果はありません。", "buttonClear": "フィルターをクリアする"}, "matchRank": "マッチ", "fullName": "氏名", "departments": "部門", "preferredWorkStates": "希望勤務先一覧", "email": "Email", "headline": "ポジション", "links": "リンク", "location": "場所", "phoneNumber": "電話番号", "jobs": "求人", "stage": "ステージ", "resume": "履歴書", "tag": "タグ", "skill": "スキル", "createAt": "作成日", "lastActivity": "最後のアクティビティ", "countJobs": "{{count}} 件の求人", "multipleStage": "複数", "owner": "所有者", "heading": "候補者", "filter": {"search": "検索", "job": "求人", "stage": "ステージ"}, "talentPools": "タレントプール", "summary": "サマリー", "skills": "スキル", "openToWork": "新規の仕事にオープンする", "languages": "言語", "nationality": "国籍", "birthday": "生年月日", "willingToRelocate": "転居可能", "noticePeriod": "期間のお知らせ", "currentSalary": "現在の給与", "expectedSalary": "希望給与", "experienceLevel": "経験レベル", "yearsOfExperience": "経験年数", "publicId": "ID", "workExperiences": "職務経験", "educations": "学歴"}, "candidateSendMail": {"composeEmail": "メールを作成する", "viewJobDetail": "求人詳細を見る", "pleaseSelectJob": "求人を選択して下さい", "send": "送信", "others": "他に {{num}} 人以上", "candidate": "候補者", "emailSent": "メールが送信されました。", "to": "宛先：", "cc": "CC:", "bcc": "Bcc:", "empty": {"title": "メールはありません", "description": "最初のメールを送信し、その後こちらでログを記録し続けます。", "compose": "作成する"}, "candidateName": "候補者の名前"}, "rejectCandidate": {"disqualifiedCandidate": "候補者を不採用とする", "scheduleEmail": "メールをスケジュールする", "scheduleDisqualifyEmail": "不採用通知メールをスケジュールする", "selectDateTime": "日付と時間を選択してスケジュールする ({{timezone}}).", "candidatesLackEmail": {"title": "一部の候補者にメールアドレスがありません", "description": "メールアドレスのない応募者も不合格となりますが、不合格通知のメールは送信されません。"}, "selectedCandidatesDoNotHaveEmail": "選択された候補者には通知用のメールアドレスがありません。"}, "candidateProfileHeader": {"candidateRequalified": "候補者が再度適格となりました！", "profile": "プロフィール", "jobRelated": "求人関連", "disqualified": "要件不合致", "candidateProfile": "候補者プロフィール", "interview": "面接", "email": "Email"}, "candidateProfile": {"adjustProfilePicture": "プロフィール写真を調整する", "jobs": "求人", "overview": "概要", "interview": "面接", "email": "Email", "activities": "アクティビティ", "notes": "ノート", "tasks": "タスク"}, "tabs": {"overview": "概要", "files": "ファイル", "jobs": "求人", "interview": "面接", "email": "Email", "activities": "アクティビティ", "notes": "ノート", "tasks": "タスク", "header": {"profile": "プロフィール", "jobRelated": "求人関連", "candidateProfile": "候補者プロフィール", "upgradePlan": "プランのアップグレード"}, "candidateNote": {"addNotePlaceholder": "こちらにノートを記載して下さい...", "empty": {"title": "ノートはありません", "description": "候補者の重要情報についてノートを取りましょう。", "create": "ノートを作成する"}, "hiringMemberSelect": {"defaultName": "全員", "defaultDescription": "この候補者にアクセスできるすべてのユーザー"}, "savingNote": "ノートを保存", "noteCreated": "ノートが作成されました。", "noteUpdated": "ノートが更新されました。", "noteDeleted": "ノートは削除されました。", "attachmentDeleted": "添付ファイルが削除されました。", "attachmentFailed": "添付に失敗しました", "max10Files": "最大 10 ファイルをアップロードできます。", "everyone": "全員", "selectAtLeastAssignee": "少なくとも1人を選択して下さい", "visibleTo": "閲覧者：", "followUpTask": "タスクのフォローアップ", "dueDate": "期日", "confirmDelete": {"title": "ノートを削除する", "description": "ノートを削除しようとしています。この操作は元に戻すことができません。"}}, "candidateOverview": {"viewMore": "詳しく見る", "readMore": "続きを読む", "notAvailable": "利用不可", "contactNotAvailable": "申請の詳細は非公開です。詳細については担当者にお問い合わせください。", "visibleTo": "{{role}} が閲覧可能", "addTag": "タグ付けする", "tooltipAddTag": "最大10タグ", "search": "検索", "saveEnter": "保存", "contactDetails": {"job": "求人", "heading": "連絡先詳細", "email": "Email", "addEmail": "メールアドレスを追加", "phoneNumber": "電話番号", "addPhoneNumber": "電話番号を追加", "location": "場所", "addLocation": "場所を追加", "links": "リンク", "addLinks": "リンクを追加", "owner": {"search": "検索"}, "maximumLinks": "最大 {{number}} リンク"}, "summary": {"heading": "サマリー", "addSummary": "候補者のサマリーを追加"}, "referral": {"heading": "リファラー情報"}, "resumeCv": {"heading": "履歴書", "deleteConfirm": {"title": "履歴書の削除", "description": "削除してもよろしいですか？この操作は元に戻すことができません。"}, "re_parser_title": "再解析", "re_parser_description": "プロフィールのすべての項目は、自動的にマッピングされ、CV/履歴書のデータで置き換えられます。本当に続行してよろしいですか？", "data_successfully_parsed": "データの解析に成功しました", "failed_to_parse": "データの解析に失敗しました"}, "coverLetter": {"heading": "カバーレター"}, "profileInformation": {"heading": "プロフィール情報", "sourced": "ソース", "sourcedName": "ソースの詳細", "selectSource": "ソースを選択", "selectJob": "選択する", "skills": "スキル", "addSkills": "スキルを追加する", "languages": "言語", "selectLanguages": "言語を選択する", "nationality": "国籍", "selectCountry": "国を選択", "inputCountry": "国を入力してください", "sourcedBy": "取得元", "createdBy": "作成者", "createdAt": "作成日", "selectUser": "ユーザーを選択", "currentSalary": "現給与", "typeOfCurrentSalaryMonthly": "月給", "typeOfCurrentSalaryAnnual": "年間", "addCurrentSalary": "現給与を追加", "expectedSalary": "希望給与", "addExpectedSalary": "予想給与を追加", "willingToRelocate": "転居可能", "selectLocation": "場所を選択する", "noticeOfPeriods": "告知期間", "selectTimePeriod": "オプションを選択または入力します", "selectTotalYearOfExp": "選択する", "birthday": "生年月日", "selectBirthday": "選択する", "owner": "所有者", "totalYoe": "経験年数", "headline": "見出し", "addHeadline": "見出しを追加する", "preferredWorkStates": "希望勤務先一覧", "addPreferredWorkStates": "希望勤務地を追加", "talentPools": "タレントプール", "talentPoolsNew": "希望職種（タレントプール）", "addTalentPool": "タレントプールの追加", "addTalentPoolNew": "希望職種（タレントプール)の追加", "today": "今日", "tomorrow": "明日", "hiredForJob": "<0>{{hiredBy}}</0> により求人 <0>{{jobTitle}}</0> に<0>採用</0>されました", "openToWork": "求職中", "position": "ポジション", "department": "部門", "add_position": "ポジションを追加する", "experienceLevel": "経験レベル", "selectExperienceLevel": "経験レベルを選択する", "formatSupportBirthday": "{{date}} <0>（{{years_old}}歳）</0>", "summary": "サマリー", "noticePeriod": "期間のお知らせ", "yearsOfExperience": "経験年数", "fullName": "氏名", "publicId": "ID"}, "workExperiences": {"heading": "職務経験", "addExperiences": "職務経験を追加する", "editExperiences": "職務経験を編集する", "jobTitleLabel": "職務タイトル", "jobTitlePlaceholder": "職務タイトルを入力してください", "companyNameLabel": "会社名", "companyNamePlaceholder": "会社名を入力してください", "locationLabel": "場所", "locationPlaceholder": "場所を入力する", "description": "詳細", "descriptionPlaceholder": "役割、責任、業績やスキルなど協調したい点を記載しましょう。", "from": "開始日", "to": "終了日", "selectDate": "日付を選択してください", "deleteTitle": "職務経験を削除する", "deleteDescription": "<0>{{title}}</0> を削除してもよろしいですか?この操作は元に戻すことができません。", "month": "月", "year": "年", "currentWorkingHere": "現在ここで働いています", "present": "現在", "currentlyWorkingHere": "現在ここで働いています", "lessThan1Year": "1年未満", "moreThan1Year": "1ヶ月以上", "months": "月", "years": "年", "above50Years": "50年以上", "sortNewestFirst": "最新の順に並べ替え"}, "education": {"heading": "学歴", "addEducation": "学歴を追加する", "editEducation": "学歴を編集する", "schoolNameLabel": "学校名", "schoolNamePlaceholder": "学校名を入力する", "degreeLabel": "学位", "degreePlaceholder": "学位を選択する", "majorLabel": "専攻科目", "majorPlaceholder": "例：グラフィックデザイン", "description": "詳細", "descriptionPlaceholder": "学校での研究や学んできたことを記載してください。", "from": "開始日", "to": "終了日", "selectDate": "日付を選択してください", "deleteTitle": "学歴の削除", "deleteDescription": "<0>{{title}}</0> を削除してもよろしいですか?この操作は元に戻すことができません。", "month": "月", "months": "月", "year": "年", "years": "年", "moreThanMonth": "1ヶ月以上", "lessThanYear": "1年未満"}, "references": {"heading": "リファレンス", "addReferences": "リファレンスを追加", "editReferences": "リファレンスを修正", "nameLabel": "推薦者の氏名", "namePlaceholder": "推薦者の氏名を入力する", "emailLabel": "推薦者のメールアドレス", "emailPlaceholder": "推薦者のメールアドレスを入力する", "deleteTitle": "リファレンスを削除", "deleteDescription": "<0>{{title}}</0> を削除してもよろしいですか?この操作は元に戻すことができません。"}, "certificates": {"heading": "免許・資格", "addCertificate": "証明・資格を追加", "editCertificate": "免許・資格の編集", "nameLabel": "免許・資格", "namePlaceholder": "免許・資格の入力", "institutionLabel": "発行機関", "institutionPlaceholder": "発行機関の入力", "issuedDateLabel": "発行日", "deleteTitle": "免許・資格の削除", "deleteDescription": "<0>{{title}}</0> を削除してもよろしいですか? この操作は元に戻せません。"}, "languages": {"addLanguage": "追加", "editLanguage": "言語を編集", "languageLabel": "言語", "languagePlaceholder": "言語を選択する", "levelLabel": "レベル", "levelPlaceholder": "レベルの選択", "language_added": "言語が追加されました", "language_updated": "言語が更新されました", "language_deleted": "言語が削除されました"}, "addSection": "セクションを追加", "link": {"deleteTitle": "リンクを削除", "deleteDescription": "<0>{{link}}</0> を削除してもよろしいですか?この操作は元に戻すことができません。", "max15Links": "最大15リンク"}}, "candidateJob": {"labelEmptyJob": "求人がありません", "descriptionEmptyJob": "特定の求人に関連する詳細な応募者情報を表示するには、下の [仕事を割り当てる] ボタンをクリックしてください。"}, "candidateInterview": {"labelEmptyInterview": "面接なし", "descriptionEmptyInterview": "面接を登録し、履歴をきちんと管理しましょう。"}, "candidateEmail": {"candidateEmailRequired": "候補者のメールアドレスが必要です", "candidateEmailProceed": "続行するには、候補者のメールアドレスを入力してください", "labelEmptyEmail": "メールはありません", "descriptionEmptyEmail": "最初のメールを送信し、メール履歴を管理しましょう。", "placeholdersAreNotSupportedFull": "プレースホルダーはサポートされていません: [{{items}}]。 {{name}}", "placeholdersAreNotSupported": "プレースホルダーはサポートされていません: [{{items}}]"}, "candidateFeedback": {"noFeedbackTitle": "フィードバックはありません。", "noFeedbackDescription": "重要な情報を管理する為、この候補者に関するフィードバックを記載する。", "overallScore": "総合スコア", "pendingFeedback": "フィードバックを保留中", "feedbackAlert": "<span class='font-medium'>{{fromDatetime}}</span> におけるこの候補者に対する <span class='font-medium'>{{eventTypeDescription}}</span> はどうでしたか？", "addNewEvaluation": "新しい評価を追加", "completeFeedback": "フィードバックを完了"}, "candidateRecommendation": {"recommendation": "リコメンド", "level": "経験レベル", "emptyJob": {"title": "リコメンド求人が見つかりません", "description": "適切な求人リコメンドの為に、職種、レベル、希望給与、スキル、言語を入力して下さい。"}, "detailModal": {"title": "マッチング詳細", "description": "<0>{{job_title}}</0> とあなたのプロフィールと比較してどれくらいマッチするかを示しています。", "updateProfile": "プロフィールを更新", "upSkills": "スキルアップ"}, "jobSalaryHelpText": "この求人の給料は{{salary}}です"}, "candidateCareer": {"careerGoal": "キャリア目標", "career": "キャリア", "nextCareerGoal": "次のキャリア目標", "currentSkills": "現在のスキル", "neededSkills": "必要なスキル", "emptyCareer": {"title": "キャリア目標なし", "description": "キャリア目標はまだ設定されていないようです"}}}, "placeholder": {"addHeadline": "ヘッドラインを追加する", "addTalentPool": "タレントプールの追加", "clickToEnterValue": "クリックして値を入力します", "clickToEnterCompany": "クリックして会社名を入力する", "searchByJobTitle": "求人タイトルから探す", "searchByJobs": "求人を検索", "inputCandidateEmail": "候補者のメールアドレスを入力する", "inputEmailSubject": "メール件名を入力", "inputEmailContent": "メール内容を入力"}, "notifyCandidateByEmail": "候補者にメールで通知する", "notifyCandidateSourcedByUser": "候補者は <span class='font-medium text-gray-900'> {{name}} </span> によって提供されましたが、その求人には応募していません", "fileUploadCompleted": "{{fileUpload}} / {{files}} ファイルがアップロードされました。", "descriptionFileUploadCompleted": "ファイルアップロード後、解析プロセスが実行されました。これにより、履歴書からデータが抽出され、システムに追加されました。", "sendToClient": {"title": "候補者に送信", "submitToClient": "クライアントに送信されました。", "sendEmailNotification": "メール通知を送信", "sendEmailDescription": "この候補者をクライアント提出段階に移行しようとしています。メールでクライアントに通知しますか?"}, "feedback": {"strong_no": "Strong no", "no": "No", "yes": "Yes", "strong_yes": "Strong yes", "pending_feedback": "フィードバックを保留"}, "lastActivity": {"desc": "新しい順", "asc": "古い順"}, "trackingIP": {"desc": "A -> Z", "asc": "Z -> A"}, "publicIdOrder": {"desc": "降順", "asc": "昇順"}, "fullNameOrder": {"desc": "Z -> A", "asc": "A -> Z"}, "userSettingsDisplay": {"fullName": "氏名", "headline": "ポジション", "departments": "部門", "preferredWorkStates": "希望勤務先一覧", "email": "Email", "phoneNumber": "電話番号", "location": "場所", "links": "リンク", "cv": "履歴書", "tags": "タグ", "owner": "所有者", "jobs": "求人", "stage": "ステージ", "createdAt": "作成日", "lastActivity": "最後のアクティビティ", "disqualifyCandidates": "候補者を不採用とする", "archivedJobs": "アーカイブされた求人", "talentPools": "タレントプール", "summary": "サマリー", "skills": "スキル", "openToWork": "求職中", "languages": "言語", "nationality": "国籍", "birthday": "生年月日", "willingToRelocate": "転居可能", "noticeToPeriodDays": "期間のお知らせ", "currentSalary": "現給与", "expectedSalary": "希望給与", "profileLevel": "経験レベル", "totalYearsOfExp": "経験年数", "publicId": "ID", "workExperiences": "職務経験", "educations": "学歴"}, "yoeOptions": {"0": "1年未満", "1": "1 年", "2": "2 年", "3": "3 年", "4": "4 年", "5": "5 年", "6": "6 年", "7": "7 年", "8": "8 年", "9": "9 年", "10": "10 年", "11": "11 年", "12": "12 年", "13": "13 年", "14": "14 年", "15": "15 年", "16": "16 年", "17": "17 年", "18": "18 年", "19": "19 年", "20": "20 年", "21": "21 年", "22": "22 年", "23": "23 年", "24": "24 年", "25": "25 年", "26": "26 年", "27": "27 年", "28": "28 年", "29": "29 年", "30": "30 年", "31": "31 年", "32": "32 年", "33": "33 年", "34": "34 年", "35": "35 年", "36": "36 年", "37": "37 年", "38": "38 年", "39": "39 年", "40": "40 年", "41": "41 年", "42": "42 年", "43": "43 年", "44": "44 年", "45": "45 年", "46": "46 年", "47": "47 年", "48": "48 年", "49": "49 年", "50": "50 年", "51": "50年以上"}, "bulkDeleteCandidates": "<0>{{number}}</0>名の候補者とすべての関連データを削除しようとしています。この操作は元に戻せません。続行してもよろしいですか?", "deleteCandidates": "候補者の削除", "candidateInformation": "本人の目線", "filterOperator": {"and": "そして", "or": "または", "where": "どこ"}, "filterOperatorCondition": {"equal": "等しい", "greater_than": "より大きい", "less_than": "より小さい", "contains": "含む", "is": "である", "or": "または", "and": "および", "all": "すべて", "any_of": "いずれか", "is_not": "ではない", "not_contains": "含まれない", "is_any_of": "いずれかである", "is_none_of": "ではない", "is_empty": "空である", "is_not_empty": "空ではない", "range": "範囲内である", "any_of_all": "いずれか/すべて"}, "profileViewDisplay": {"myViews": "自分のビュー", "otherViews": "その他のビュー", "allCandidates": "すべての候補者", "form": {"state": {"public": "公開", "private": "プライベート"}}, "modal": {"modalCreated": {"title": "新しいビュー"}, "modalUpdated": {"title": "編集ビュー"}, "modalDeleted": {"title": "ビューを削除", "description": "<0>{{profileView}}</0>を削除してもよろしいですか？この操作は元に戻せません。"}}}, "exportLogContent": {"csv": "ユーザーが{{viewName}}ビューから{{number}}名の候補者リストをエクスポートしました。", "pdf": "ユーザーが{{templateName}}テンプレートを使用して{{number}}名の候補者リストをエクスポートしました。"}, "modalExportPDF": {"title": "PDFをエクスポート", "description": "適切な情報を含むテンプレートを選択し、<0>{{number}}</0>件のプロフィールをエクスポートしてください。", "includeCandidateName": "PDFファイル名に候補者名を含める"}, "callLogs": {"modal": {"create": {"title": "通話をログに記録"}, "update": {"title": "通話履歴を編集"}}, "delete": {"title": "通話履歴を削除", "description": "<b>{{direction}}</b> を削除してもよろしいですか? この操作は元に戻せません。", "toastSuccess": "通話記録が削除されました!", "toastFailed": "通話履歴の削除に失敗しました。"}}, "aiSuggestsSkills": "主要なスキルや認定資格をリストアップしてください。適切な推奨を得るには「AIの提案」を使用してください。", "headerTable": {"name": "氏名", "email": "Email", "date": "日付", "education": "学歴", "placeOfEmployment": "勤務地・仕事の詳細", "certificate": "免許・資格 - 発行機関"}}