// Give the service worker access to Firebase Messaging.
// Note that you can only use Firebase Messaging here. Other Firebase libraries
// are not available in the service worker.
importScripts('https://www.gstatic.com/firebasejs/10.11.1/firebase-app-compat.js')
importScripts('https://www.gstatic.com/firebasejs/10.11.1/firebase-messaging-compat.js')

firebase.initializeApp({
  apiKey: 'AIzaSyD1NxLKxf4ssGOi8Tprb3jfANHQ-Soeidw',
  authDomain: 'hireforce-dev.firebaseapp.com',
  databaseURL: 'https://hireforce-dev-default-rtdb.asia-southeast1.firebasedatabase.app',
  projectId: 'hireforce-dev',
  storageBucket: 'hireforce-dev.appspot.com',
  messagingSenderId: '1078226294647',
  appId: '1:1078226294647:web:af2f04417cae69622ef372',
  measurementId: 'G-D1Z1P5LJPB'
})

const messaging = firebase.messaging()

messaging.onBackgroundMessage((payload) => {
  // block code
})
