import type { FC } from 'react'
import { useState } from 'react'
import { Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import type { IFormAction } from '~/core/@types/global'
import { Button } from '~/core/ui/Button'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import { AlertCircleFill } from '~/core/ui/FillIcons'
import { FormControlItem } from '~/core/ui/FormControlItem'
import If from '~/core/ui/If'
import { Input } from '~/core/ui/Input'

import schemaAddRecommendationSettingForm from '~/lib/features/recommendation-setting/schema/validation-recommendation-setting'
import type {
  FieldNames,
  IRecommendationSetting
} from '~/lib/features/recommendation-setting/type/recommendation-setting'
import { RECOMMENDATION_VALUE_SETTINGS_DEFAULT } from '~/lib/features/recommendation-setting/utilities/enum'
import useTenantSettingJobFieldsHook from '~/lib/features/settings/profile-fields/hooks/use-tenant-setting-job-field-hook'

import { InputRightElement } from '../../core/ui/InputElement'
import { InputGroup } from '../../core/ui/InputGroup'
import { TypographyText } from '../../core/ui/Text'
import { SYSTEM_JOB_FIELDS } from '../../lib/features/jobs/utilities/enum'

const RecommendationSettingForm: FC<{
  isLoading?: boolean
  formType: string
  defaultValue?: IRecommendationSetting
  btnSubmitLabel?: string
  onOpenChange: (open: boolean) => void
  onSubmit: (data: any, formAction?: IFormAction) => Promise<void>
}> = ({ isLoading, formType, defaultValue, btnSubmitLabel, onOpenChange, onSubmit }) => {
  const { t } = useTranslation()
  const { isShowSystemField } = useTenantSettingJobFieldsHook()

  const [isAllFieldZero, setAllFieldZero] = useState<boolean>(false)

  const fieldsConfig: {
    name: FieldNames
    label: string
    visible: boolean
  }[] = [
    {
      name: 'experienceLevel',
      label: t('label:experienceLevel'),
      visible: isShowSystemField(SYSTEM_JOB_FIELDS.job_level) || false
    },
    {
      name: 'talentPool',
      label: t('label:talentPools'),
      visible: isShowSystemField(SYSTEM_JOB_FIELDS.job_talent_pool_ids) || false
    },
    {
      name: 'salary',
      label: t('label:salary'),
      visible: isShowSystemField(SYSTEM_JOB_FIELDS.salary) || false
    },
    {
      name: 'language',
      label: t('label:language'),
      visible: true
    },
    {
      name: 'location',
      label: t('label:location'),
      visible: true
    },
    {
      name: 'skill',
      label: t('label:skills'),
      visible: true
    }
  ]
  const handleSubmit = async (data: IRecommendationSetting) => {
    const filteredData = Object.entries(data)
      .filter(([key]) => {
        const fieldConfig = fieldsConfig.find((field) => field.name === key)
        return fieldConfig?.visible === true
      })
      .reduce(
        (acc, [key, value]) => {
          acc[key] = value

          return acc
        },
        {} as Record<string, any>
      )

    const allFieldInFormZero = Object.values(filteredData).every((value) => Number(value) === 0)

    setAllFieldZero(allFieldInFormZero)

    if (allFieldInFormZero) return

    setAllFieldZero(allFieldInFormZero)

    if (allFieldInFormZero) return

    setAllFieldZero(allFieldInFormZero)

    if (allFieldInFormZero) return

    return onSubmit(filteredData)
  }

  return (
    <>
      <DynamicImportForm
        id="add-recommendation-setting-form"
        className="w-full"
        schema={schemaAddRecommendationSettingForm(t)}
        onSubmit={handleSubmit}
        defaultValue={defaultValue}
        noUseSubmit>
        {({ formState, control }) => {
          return (
            <>
              <div className={isAllFieldZero ? 'mb-4' : 'mb-6'}>
                <div className="grid grid-cols-2 gap-4">
                  {fieldsConfig.map((field) => (
                    <If condition={field.visible} key={field.name}>
                      <div>
                        <Controller
                          control={control}
                          name={field.name}
                          defaultValue={RECOMMENDATION_VALUE_SETTINGS_DEFAULT}
                          render={({ field: { onChange, value } }) => (
                            <FormControlItem
                              label={field.label}
                              destructive={formState.errors && !!formState.errors[field.name]}
                              destructiveText={formState.errors && (formState.errors[field.name]?.message as string)}>
                              <InputGroup size="sm">
                                <Input
                                  placeholder={`${t('label:namePlaceholder')}`}
                                  size="sm"
                                  inputType="number"
                                  min="0"
                                  onChange={(newValue) => {
                                    const value = Number(newValue)
                                    onChange(value)
                                    if (Number(newValue) > 0 && isAllFieldZero) {
                                      setAllFieldZero(false)
                                    }
                                  }}
                                  value={value}
                                  destructive={formState.errors && !!formState.errors[field.name]}
                                />
                                <InputRightElement className="right-[35px]">
                                  <div className="flex h-[30px] items-center">
                                    <TypographyText className="text-sm text-gray-500">%</TypographyText>
                                  </div>
                                </InputRightElement>
                              </InputGroup>
                            </FormControlItem>
                          )}
                        />
                      </div>
                    </If>
                  ))}
                </div>
              </div>
              {isAllFieldZero && (
                <div className="mb-6 flex items-center">
                  <div className="min-w-[16px]">
                    <AlertCircleFill />
                  </div>
                  <div className="ml-1">
                    <p className="text-sm font-normal text-red-500 dark:text-red-500">{`${t(
                      'form:requireAtLeastOneNonZero'
                    )}`}</p>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-end">
                <Button
                  type="secondary"
                  className="mr-3"
                  htmlType="button"
                  onClick={() => onOpenChange(false)}
                  size="sm"
                  label={`${t('button:cancel')}`}
                />

                <Button
                  isDisabled={isLoading}
                  isLoading={isLoading}
                  htmlType="submit"
                  onClick={() => handleSubmit}
                  size="sm"
                  label={btnSubmitLabel}
                />
              </div>
            </>
          )
        }}
      </DynamicImportForm>
    </>
  )
}

export default RecommendationSettingForm
