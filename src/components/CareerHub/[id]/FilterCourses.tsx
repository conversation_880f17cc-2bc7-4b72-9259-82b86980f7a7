'use client'

import type { Di<PERSON><PERSON>, <PERSON>, SetStateAction } from 'react'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import type { ISelectOption } from '~/core/ui/Select'

import QuerySkillsList from '~/lib/features/career-hub/graphql/query-skills-list'
import type { CourseFilter } from '~/lib/features/career-hub/types'

import Filter from '~/components/Filter/Filter'

const FilterCourses: FC<{
  filterControl: {
    value: CourseFilter | undefined
    onChange: Dispatch<SetStateAction<CourseFilter | undefined>>
  }
  viewList?: React.ReactNode
  filtersOptions?: {
    providerOptions: ISelectOption[]
    levelOptions: ISelectOption[]
    typeOptions: ISelectOption[]
    languagesOptions: ISelectOption[]
  }
  extras?: any
}> = ({ filterControl, viewList, filtersOptions, extras }) => {
  const { t } = useTranslation()
  const [filters, setFilters] = useState<
    | {
        providers: ISelectOption[]
        levels: ISelectOption[]
        types: ISelectOption[]
        languages: ISelectOption[]
      }
    | undefined
  >()
  const [needToSortSkill, setNeedToSortSkill] = useState<boolean>(true)

  useEffect(() => {
    if (filterControl.value && extras?.skills && filterControl.value.skills?.length) {
      const updatedSkills = filterControl.value.skills.map((item: any) => ({
        value: String(item.value),
        supportingObj: { name: extras.skills[item.value] }
      }))

      filterControl.onChange({
        ...filterControl.value,
        skills: updatedSkills,
        isFilterTouched: false
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [extras?.skills])

  useEffect(() => {
    if (extras?.facets) {
      const providerCounts = extras?.facets?.provider
      const courseLevelCounts = extras?.facets?.course_level
      const courseTypeCount = extras?.facets?.course_type
      const languageCount = extras?.facets?.language

      setFilters({
        providers: (filtersOptions?.providerOptions || []).map((item) => {
          const count = providerCounts?.[item.value] ?? 0 // default 0 if not found
          return {
            ...item,
            supportingObj: {
              name: `${item?.supportingObj?.name}`,
              helpName: `(${count})`
            }
          }
        }),
        levels: (filtersOptions?.levelOptions || []).map((item) => {
          const count = courseLevelCounts?.[item.value] ?? 0 // default 0 if not found
          return {
            ...item,
            supportingObj: {
              name: `${item?.supportingObj?.name}`,
              helpName: `(${count})`
            }
          }
        }),
        types: (filtersOptions?.typeOptions || []).map((item) => {
          const count = courseTypeCount?.[item.value] ?? 0 // default 0 if not found
          return {
            ...item,
            supportingObj: {
              name: `${item?.supportingObj?.name}`,
              helpName: `(${count})`
            }
          }
        }),
        languages: (filtersOptions?.languagesOptions || []).map((item) => {
          const count = languageCount?.[item.value] ?? 0 // default 0 if not found
          return {
            ...item,
            supportingObj: {
              name: `${item?.supportingObj?.name}`,
              helpName: `(${count})`
            }
          }
        })
      })
    }
  }, [extras?.facets])

  return (
    <div
      className="flex"
      onKeyDown={(e) => {
        if (e.key === 'Enter') {
          e.preventDefault()
          e.stopPropagation()
        }
      }}>
      <Filter
        value={filterControl.value}
        onChange={(filterVariable, name) => {
          filterControl.onChange({ ...filterVariable, isFilterTouched: true })
        }}>
        <div className="mr-[60px] rounded-lg border border-solid border-gray-100 bg-white p-4 pt-3">
          <div className="mb-3 text-base font-medium text-gray-900">{t('careerHub:filter:title')}</div>
          <div className="mt-4">
            <div className="mb-1 text-sm font-medium text-gray-700">{t('label:skills')}</div>
            <Filter.Combobox
              menuOptionAlign="end"
              menuOptionSide="bottom"
              isSearchable={true}
              isMulti
              buttonClassName="w-[249px] text-gray-900"
              buttonFontWeightClassName="font-normal"
              dropdownMenuClassName="w-[249px]!"
              size="md"
              countName={`${t('label:countName:skills')}`}
              configSelectOption={{
                supportingText: ['name', 'helpName']
              }}
              optionsFromDocumentNode={{
                documentNode: QuerySkillsList,
                variable: (searchParams) => {
                  const skillIds = (filterControl?.value?.skills || []).map((item) => Number(item.value))
                  return needToSortSkill
                    ? {
                        ...searchParams,
                        selectedIds: skillIds
                      }
                    : searchParams
                },
                mapping: (data) => {
                  setNeedToSortSkill(false)
                  const skillCount = extras?.facets?.skill_ids
                  return {
                    metadata: data?.skillsList.metadata,
                    collection: data?.skillsList.collection.map((item: { id: number; name: string }, index) => {
                      const { currentPage } = data?.skillsList.metadata
                      const lastItemSelectedIndex = 25 * (currentPage - 1) + index + 1
                      const numberOfSkillSelected = needToSortSkill ? Number(filterControl?.value?.skills?.length) : 0
                      const count = skillCount?.[item.id] ?? 0 // default 0 if not found

                      return {
                        value: String(item.id),
                        supportingObj: {
                          name: `${item.name}`,
                          helpName: `(${count})`
                        },
                        extras: {
                          separate: numberOfSkillSelected !== 0 && lastItemSelectedIndex === numberOfSkillSelected
                        }
                      }
                    })
                  }
                }
              }}
              placeholder={`${t('label:placeholder:select')}`}
              searchPlaceholder={`${t('label:placeholder:search')}`}
              loadingMessage={`${t('label:loading')}`}
              noOptionsMessage={`${t('label:noOptions')}`}
              name="skills"
              onBlur={() => setNeedToSortSkill(true)}
            />
          </div>
          <div className="mt-4">
            <div className="mb-1 text-sm font-medium text-gray-700">{t('label:provider')}</div>
            <Filter.Combobox
              menuOptionAlign="end"
              menuOptionSide="bottom"
              isSearchable={false}
              isMulti
              buttonClassName="w-[249px] text-gray-900"
              buttonFontWeightClassName="font-normal"
              dropdownMenuClassName="w-[249px]!"
              size="md"
              countName={`${t('label:countName:providers')}`}
              options={filters?.providers}
              placeholder={`${t('label:placeholder:select')}`}
              searchPlaceholder={`${t('label:placeholder:search')}`}
              loadingMessage={`${t('label:loading')}`}
              noOptionsMessage={`${t('label:noOptions')}`}
              name="provider"
              configSelectOption={{
                supportingText: ['name', 'helpName']
              }}
            />
          </div>
          <div className="mt-4">
            <div className="mb-1 text-sm font-medium text-gray-700">{t('label:level')}</div>
            <Filter.Combobox
              menuOptionAlign="end"
              menuOptionSide="bottom"
              isSearchable={false}
              isMulti
              buttonClassName="w-[249px] text-gray-900"
              buttonFontWeightClassName="font-normal"
              dropdownMenuClassName="w-[249px]!"
              size="md"
              countName={`${t('label:countName:levels')}`}
              options={filters?.levels}
              placeholder={`${t('label:placeholder:select')}`}
              searchPlaceholder={`${t('label:placeholder:search')}`}
              loadingMessage={`${t('label:loading')}`}
              noOptionsMessage={`${t('label:noOptions')}`}
              name="level"
              configSelectOption={{
                supportingText: ['name', 'helpName']
              }}
            />
          </div>
          <div className="mt-4">
            <div className="mb-1 text-sm font-medium text-gray-700">{t('label:type')}</div>
            <Filter.Combobox
              menuOptionAlign="end"
              menuOptionSide="bottom"
              isSearchable={false}
              isMulti
              buttonClassName="w-[249px] text-gray-900"
              buttonFontWeightClassName="font-normal"
              dropdownMenuClassName="w-[249px]!"
              size="md"
              countName={`${t('label:countName:types')}`}
              options={filters?.types}
              placeholder={`${t('label:placeholder:select')}`}
              searchPlaceholder={`${t('label:placeholder:search')}`}
              loadingMessage={`${t('label:loading')}`}
              noOptionsMessage={`${t('label:noOptions')}`}
              name="type"
              configSelectOption={{
                supportingText: ['name', 'helpName']
              }}
            />
          </div>
          <div className="mt-4">
            <div className="mb-1 text-sm font-medium text-gray-700">{t('label:languages')}</div>
            <Filter.Combobox
              menuOptionAlign="end"
              menuOptionSide="bottom"
              isSearchable={true}
              isMulti
              buttonClassName="w-[249px] text-gray-900"
              buttonFontWeightClassName="font-normal"
              dropdownMenuClassName="w-[249px]!"
              size="md"
              countName={`${t('label:countName:languages')}`}
              options={filters?.languages}
              placeholder={`${t('label:placeholder:select')}`}
              searchPlaceholder={`${t('label:placeholder:search')}`}
              loadingMessage={`${t('label:loading')}`}
              noOptionsMessage={`${t('label:noOptions')}`}
              name="languages"
              configSelectOption={{
                supportingText: ['name', 'helpName']
              }}
            />
          </div>
        </div>
      </Filter>
      <div className="mx-auto w-full">
        <Filter
          value={filterControl.value}
          onChange={(filterVariable) => {
            filterControl.onChange({
              ...filterVariable,
              isFilterTouched: true
            })
          }}>
          <div className="flex w-full items-center">
            <div className="w-full">
              <Filter.SearchText
                typingDebounceSubmit={500}
                maxLength={50}
                size="sm"
                placeholder={`${t('label:placeholder:searchLearningLibrary')}`}
                name="search"
              />
            </div>
          </div>
        </Filter>
        <div className="h-full">{viewList}</div>
      </div>
    </div>
  )
}

export default FilterCourses
