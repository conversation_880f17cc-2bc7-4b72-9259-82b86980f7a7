import { useRouter } from 'next/navigation'
import type { Disp<PERSON>, FC, SetStateAction } from 'react'
import { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'

import pathConfiguration from 'src/configuration/path'
import { AGENCY_TENANT } from '~/core/constants/enum'
import { PUBLIC_APP_URL } from '~/core/constants/env'
import { CAREERS_LIST_URL } from '~/core/constants/url'
import { ClipboardIdIcon } from '~/core/ui/FillIcons'
import type { IFormAction } from '~/core/ui/Form'
import type { LucideIconName } from '~/core/ui/IconWrapper'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import { SuggestionInlineChips } from '~/core/ui/SuggestionChips'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { truncateTextWithDot } from '~/core/utilities/common'
import { defaultFormatDate } from '~/core/utilities/format-date'

import schemaInternalApplicationForm from '~/lib/features/apply/jobId/schema/validation-internal-application-form'
import type { ApplicationFormType } from '~/lib/features/apply/jobId/types'
import useShareJobLogic from '~/lib/features/jobs/hooks/use-share-job-logic'
import { JOB_STATUS_ENUM, REFERRAL_REWARD_MONEY_VALUE, SYSTEM_JOB_FIELDS } from '~/lib/features/jobs/utilities/enum'
import CreateInternalApplicationMutation from '~/lib/features/referrals/graphql/create-internal-application-mutation'
import { MutationSavedJob } from '~/lib/features/referrals/graphql/save-job-mutation'
import useProfileEmployeeHook from '~/lib/features/referrals/hooks/use-profile-employee-hook'
import { mappingProfileEmployeeToApplicationForm } from '~/lib/features/referrals/mapping/mappingApplicationForm'
import type { JobDetailType, ReferralFormType } from '~/lib/features/referrals/types'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useTenantSettingJobFieldsHook from '~/lib/features/settings/profile-fields/hooks/use-tenant-setting-job-field-hook'
import useReferralSetting from '~/lib/features/settings/referrals/hooks/useReferralSetting'
import { convertMoneyNotRounded } from '~/lib/features/settings/referrals/utilities/enum'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import { useRouterContext } from '~/lib/next/use-router-context'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import WithComputedMaxItemsChips from '~/components/WithComputedMaxItemsChips'

import ApplicationFormModal from '../../Apply/[jobId]/components/ApplicationFormModal'
import TooltipWithButton from '../../Apply/[jobId]/components/TooltipWithButton'
import type { ModalMatchedRankType } from '../../Candidates/Profile/components/Recommendation/MatchedRankDetail'
import MatchedRankDetailWrapper from '../../Candidates/Profile/components/Recommendation/MatchedRankDetailWrapper'
import ShareJobModal from '../../Jobs/ShareJobModal'
import BookMarkedSVG from './BookMarkedSVG'
import BookMarkSVG from './BookMarkSVG'
import ReferralModal from './ReferralModal'

const JobInfoWrapper: FC<{
  iconMenu?: LucideIconName
  text: string
  tooltip?: React.ReactElement<any>
  className?: string
}> = ({ iconMenu, text, tooltip, className }) => {
  return (
    <div className={`flex max-w-[200px] flex-none items-center ${className}`}>
      <div className="mr-1.5">
        <IconWrapper name={iconMenu} size={12} className="text-gray-600" />
      </div>
      {tooltip ? (
        <Tooltip content={tooltip}>
          <TypographyText className="line-clamp-1 text-sm break-all text-gray-700">{text}</TypographyText>
        </Tooltip>
      ) : (
        <TypographyText className="text-sm text-gray-700">{text}</TypographyText>
      )}
    </div>
  )
}

const OpenJobItem: FC<{
  job: JobDetailType
  isJobsPositionMode?: boolean
  onHandleSelectSkill?: (skill: string) => void
  openMatchedRank?: Dispatch<SetStateAction<ModalMatchedRankType>>
  refetch: any
  currentTab?: string
  configHideInfo?: {
    tag?: boolean
    publicId?: boolean
  }
}> = ({
  job,
  openMatchedRank,
  refetch,
  isJobsPositionMode = false,
  configHideInfo,
  onHandleSelectSkill,
  currentTab
}) => {
  const { t } = useTranslation()
  const router = useRouter()
  const { searchParams } = useRouterContext()
  const { isShowSystemFieldWithCareerSite } = useTenantSettingJobFieldsHook()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })

  const { trigger: bookmarkAPI, isLoading: bookMarkLoading } = useSubmitCommon(MutationSavedJob)
  const [openShareModal, setOpenShareModal] = useState<boolean>(false)
  const [openApplicant, setOpenApplicant] = useState<boolean>(false)
  const [openReferralModal, setOpenReferralModal] = useState<boolean>(false)
  const [referralJob, setReferralJob] = useState<ReferralFormType>()
  const onOpenApplicationChange = useCallback((state: boolean) => {
    setOpenApplicant(state)
  }, [])
  const onClickApplyNow = useCallback(() => {
    onOpenApplicationChange(true)
  }, [onOpenApplicationChange])
  const { user } = useBoundStore()
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const careerSiteSettingsLanguages = user?.currentTenant?.careerSiteSettings?.languages
  const japaneseLanguageDefault = careerSiteSettingsLanguages?.ja?.default
  const { dataReferral } = useReferralSetting()
  const { valueShareJobWithCondition } = useShareJobLogic()
  const isBookMarked = (job?.savedReferralJobMemberIds || []).filter((id) => Number(id) === Number(user.id)).length > 0
  const checkConditionShareJob = valueShareJobWithCondition({
    enablingReferral: dataReferral?.values?.enabling || false,
    enablingCareerSite: user.currentTenant?.careerSiteSettings?.enablingCareerSiteSetting || false,
    enableJobReferral: job?.jobReferable || false,
    jobStatus: job?.status || ''
  })

  const formatQueryStringUrl = (searchStr: string) => {
    const allQueryParams: { [key: string]: string | string[] } = {}
    searchParams?.forEach((value, key) => {
      if (allQueryParams[key]) {
        // Handle multiple values for the same key (e.g., ?param=a&param=b)
        if (Array.isArray(allQueryParams[key])) {
          ;(allQueryParams[key] as string[]).push(value)
        } else {
          allQueryParams[key] = [allQueryParams[key] as string, value]
        }
      } else {
        allQueryParams[key] = value
      }
    })
    const queryStringParams = allQueryParams || {}
    const skipParams = ['slug', 'search']
    return Object.keys(queryStringParams).length
      ? [
          `search=${searchStr}`,
          ...Object.keys(queryStringParams)
            .filter((key) => !skipParams.includes(key))
            .map((key) => `${key}=${encodeURIComponent(queryStringParams[key] as string)}`)
        ].join('&')
      : ''
  }
  const isShowCompanyFeature = isFeatureEnabled(PLAN_FEATURE_KEYS.company) && isUnLockFeature(PLAN_FEATURE_KEYS.company)
  const referralPortal = dataReferral?.values?.referral_portal
  const renderLocations = useCallback((locations: JobDetailType['jobLocations']) => {
    const locationFormatted = (locations || [])?.map(
      (location) =>
        `${location.state || ''}${!!location.state && !!location.country ? ', ' : ''}${location.country || ''}`
    )
    const locationFormattedUnique = locationFormatted?.filter(
      (location, index) => locationFormatted.indexOf(location) === index
    )
    return (
      <JobInfoWrapper
        iconMenu="MapPin"
        tooltip={
          <>
            {locationFormattedUnique?.map((location) => (
              <div key={`open-job-location-${location}`}>{location}</div>
            ))}
          </>
        }
        text={
          locationFormattedUnique.length > 1
            ? `${locationFormattedUnique.length} ${t('label:locations')}`
            : truncateTextWithDot(String(locationFormattedUnique?.[0]))
        }
      />
    )
  }, [])
  const handleOpenReferralModal = useCallback(
    (job: JobDetailType) => {
      setReferralJob({
        jobId: [
          {
            value: String(job?.id),
            supportingObj: {
              name: job?.title
            }
          }
        ]
      })
      setOpenReferralModal(true)
    },
    [setOpenReferralModal]
  )

  const { setToast } = useToastStore()
  const { trigger, isLoading } = useSubmitCommon(CreateInternalApplicationMutation)
  const { employeeProfile, fetchEmployeeProfile } = useProfileEmployeeHook()
  const onFinish = useCallback(
    async (data: ApplicationFormType, formAction: IFormAction) => {
      if (isLoading) {
        return
      }

      trigger({
        jobId: Number(job.id),
        ...data
      }).then((result) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            formAction,
            setToast,
            callbackHandleStatusError422: (keys) => {
              keys.forEach((key) => {
                if (key.message.includes('terms_and_conditions')) {
                  setToast({
                    open: true,
                    type: 'error',
                    title: `${t('form:consent_data_processing')}`
                  })
                } else {
                  if (!key.field) {
                    setToast({
                      open: true,
                      type: 'error',
                      title: key.message
                    })
                  } else {
                    formAction.setError(key.field, {
                      type: 'custom',
                      message: key.message
                    })
                  }
                }
              })
            }
          })
        }
        const { internalApplicantsCreate } = result.data
        if (internalApplicantsCreate?.profile?.id) {
          //setToast
          setToast({
            open: true,
            type: 'success',
            title: `${t('careers:applied:applied_successfully')}`
          })

          setOpenApplicant(false)
          fetchEmployeeProfile()
          refetch()
        }
        return
      })
    },
    [isLoading, job?.id, job?.tenant?.slug]
  )
  const rank = job?.recommendationMatchedFields?.filter((r) => r.field === 'total')[0]
  const onCloseReferralModal = () => setOpenReferralModal(false)
  return (
    <div className="group mb-2 w-full rounded-xs border border-solid border-gray-100 bg-white px-4">
      <div className="flex justify-between pt-4 pb-2">
        <div>
          <div className="flex items-center">
            {job.recommendationMatchedFields && rank.total_rate > 0 && !isJobsPositionMode && (
              <div className="mr-1.5">
                <MatchedRankDetailWrapper
                  openMatchedRank={openMatchedRank}
                  currentTab={currentTab}
                  job={job}
                  isCareerHubJob={true}
                />
              </div>
            )}
            <div className="line-clamp-1">
              <Tooltip content={job?.title}>
                <a
                  target="_blank"
                  href={pathConfiguration?.careerHub?.jobDetail({
                    tenantSlug: job?.tenant?.slug,
                    jobId: job.id
                  })}>
                  <TypographyText className="text-base font-medium text-gray-900 hover:cursor-pointer hover:underline">
                    {job?.title}
                  </TypographyText>
                </a>
              </Tooltip>
            </div>
          </div>
        </div>
        <div className="ml-8 flex min-w-[300px] flex-row justify-end">
          {(referralPortal?.referral_only || referralPortal?.referral_job) && (
            <div className="flex">
              {job?.publicReferralUri ||
              checkConditionShareJob?.shareInternal ||
              checkConditionShareJob?.sharePublic ? (
                <div>
                  <Tooltip
                    content={`${t('careerHub:open_jobs:share:disabled_button_tooltip')}`}
                    classNameConfig={{
                      content: job?.status !== JOB_STATUS_ENUM.publish ? '' : 'hidden'
                    }}>
                    <TextButton
                      label={t('label:share')}
                      size="md"
                      isDisabled={job?.status !== JOB_STATUS_ENUM.publish}
                      underline={false}
                      onClick={() => {
                        setOpenShareModal(true)
                      }}
                    />
                  </Tooltip>
                </div>
              ) : null}

              <div className="ml-4">
                <TextButton
                  label={t('label:refer')}
                  size="md"
                  underline={false}
                  onClick={() => {
                    handleOpenReferralModal(job)
                  }}
                />
              </div>
            </div>
          )}
          {(referralPortal?.job_only || referralPortal?.referral_job) && (
            <div className="ml-4">
              <TooltipWithButton
                content={t('careerHub:tooltip:applied_tooltip_disable', {
                  date: `${defaultFormatDate(new Date(job.currentUserAppliedAt))}`
                })}
                isShowTooltip={!!job.currentUserAppliedAt}>
                <TextButton
                  isDisabled={!!job.currentUserAppliedAt}
                  label={t(!!job.currentUserAppliedAt ? 'careerHub:detail:btn_applied' : 'careerHub:detail:btn_apply')}
                  size="md"
                  underline={false}
                  onClick={() => onClickApplyNow()}
                />
              </TooltipWithButton>
            </div>
          )}
          <div
            onClick={() => {
              bookmarkAPI({
                id: Number(job.id),
                saveReferralJob: isBookMarked ? false : true
              }).then((result) => {
                refetch &&
                  refetch().then(() => {
                    setToast({
                      open: true,
                      type: 'success',
                      title: !isBookMarked
                        ? t('notification:jobs:jobCard:jobSaved')
                        : t('notification:jobs:jobCard:unJobSaved')
                    })
                  })
              })
            }}
            className="ml-4 cursor-pointer">
            {isBookMarked ? <BookMarkedSVG /> : <BookMarkSVG />}
          </div>
        </div>
      </div>
      <div className="mb-2 flex items-center space-x-2">
        <If condition={!configHideInfo?.publicId && !!job?.permittedFields?.publicId?.value}>
          <div className="flex max-w-[180px] items-center break-all">
            <div className="mr-1.5 flex-none">
              <ClipboardIdIcon />
            </div>
            <Tooltip content={`ID: ${job?.permittedFields?.publicId?.value}`}>
              <TypographyText className="line-clamp-1 text-sm text-gray-700">
                {job?.permittedFields?.publicId?.value}
              </TypographyText>
            </Tooltip>
          </div>
          <div className="mr-2 ml-2 h-[2px] w-[2px] flex-none rounded-full bg-gray-400" />
        </If>

        {isShowCompanyFeature && isShowSystemFieldWithCareerSite(SYSTEM_JOB_FIELDS.company) ? (
          <>
            <IconWrapper size={12} name="Building" className="flex-none text-gray-600" />
            <Tooltip
              content={
                job?.permittedFields?.['company']?.value?.name ||
                job?.company?.permittedFields?.name?.value ||
                user.currentTenant?.name
              }>
              <TypographyText className="line-clamp-1 max-w-[200px] text-sm font-medium text-gray-800 hover:underline">
                {job?.permittedFields?.['company']?.value?.name ||
                  job?.company?.permittedFields?.name?.value ||
                  user.currentTenant?.name}
              </TypographyText>
            </Tooltip>
          </>
        ) : null}

        {(job?.jobLocations || [])?.length > 0 &&
          !!job?.jobLocations?.[0]?.country &&
          renderLocations(job?.jobLocations)}
        {(job?.jobLocations || [])?.length > 0 && !!job?.jobLocations?.[0]?.country && !!job?.department?.name && (
          <div className="h-0.5 w-0.5 rounded-xs bg-gray-400" />
        )}
        {!!job?.department?.name && (
          <JobInfoWrapper
            iconMenu="Network"
            tooltip={<>{job?.department?.name}</>}
            text={truncateTextWithDot(job?.department?.name)}
          />
        )}
        <If condition={!configHideInfo?.tag && (job?.tags?.length || 0) > 0}>
          <div className="flex max-w-[200px] flex-none items-center">
            <div className="h-0.5 w-0.5 rounded-xs bg-gray-400" />
            <div className="mx-1.5">
              <IconWrapper size={12} name="Tag" className="flex-none text-gray-600" />
            </div>
            <div className="flex">
              <Tooltip content={job?.tags?.map((item) => item.name).join(', ')}>
                <TypographyText className="line-clamp-1 max-w-[200px] text-sm break-all text-gray-700">
                  {job?.tags
                    ?.map((item) => item.name)
                    .slice(0, 2)
                    .join(', ')}
                </TypographyText>
              </Tooltip>
              <div className="flex flex-none text-sm">
                {(job?.tags?.length || 0) > 2 && `, + ${(job?.tags?.length || 0) - 2}`}
              </div>
            </div>
          </div>
        </If>
        <If condition={job?.enablingReward && (referralPortal?.referral_only || referralPortal?.referral_job)}>
          <div className="h-0.5 w-0.5 rounded-xs bg-gray-400" />
          <Tooltip
            content={
              <>
                <TypographyText className="text-sm">
                  {convertMoneyNotRounded(job?.rewardAmount)}{' '}
                  {job?.referralRewardType === REFERRAL_REWARD_MONEY_VALUE ? job?.rewardCurrency : job?.rewardGift}
                </TypographyText>
              </>
            }>
            <div className="flex items-center text-blue-500">
              <IconWrapper size={12} name="Gift" className="mr-1.5 text-blue-500" />
            </div>
          </Tooltip>
        </If>
      </div>
      <div className="flex items-center justify-between pb-4">
        <div>
          {job?.skills?.length > 0 ? (
            <WithComputedMaxItemsChips
              totalCount={job?.skills?.length}
              className="relative -mt-2 flex w-[574px] items-center">
              {({ maxItems }) => {
                return (
                  <SuggestionInlineChips
                    size="md"
                    source={
                      job.skills?.map((skill) => ({
                        label: skill,
                        maxLength: 30,
                        onClick: () => {
                          if (!isJobsPositionMode) {
                            router.push(
                              `${pathConfiguration.careerHub.jobs(
                                user?.currentTenant?.slug || ''
                              )}?${formatQueryStringUrl(skill)}`,
                              undefined
                            )
                          }
                          onHandleSelectSkill && onHandleSelectSkill(skill)
                        }
                      })) || []
                    }
                    type="default"
                    maxItems={maxItems}
                  />
                )
              }}
            </WithComputedMaxItemsChips>
          ) : null}
        </div>

        <div className="mt-1 flex items-center justify-end">
          {job?.referralsCount > 0 && !referralPortal?.job_only && (
            <>
              <TypographyText className="text-sm whitespace-nowrap text-gray-600">
                {job?.referralsCount} {job?.referralsCount > 1 ? `${t('label:referrals')}` : `${t('label:referral')}`}
              </TypographyText>
              <div className="mx-2 h-0.5 w-0.5 rounded-full bg-gray-400" />
            </>
          )}
          {job?.createdAt && (
            <TypographyText className="text-sm whitespace-nowrap text-gray-600">
              {defaultFormatDate(new Date(job.createdAt))}
            </TypographyText>
          )}
        </div>
      </div>
      <ShareJobModal
        shareInternal={checkConditionShareJob?.shareInternal}
        sharePublic={checkConditionShareJob?.sharePublic}
        open={openShareModal}
        setOpen={setOpenShareModal}
        urlReferral={`${PUBLIC_APP_URL}${pathConfiguration.careerHub.jobDetail({
          tenantSlug: job?.tenant?.slug,
          jobId: job?.id.toString()
        })}`}
        url={`${PUBLIC_APP_URL}${japaneseLanguageDefault ? '/ja' : ''}${CAREERS_LIST_URL}/${
          job?.publicReferralUri
        }&utm_medium=internal_social_share`}
      />
      {openApplicant && (
        <ApplicationFormModal
          job={job}
          view={
            isFeatureEnabled(PLAN_FEATURE_KEYS.application_form) && isUnLockFeature(PLAN_FEATURE_KEYS.application_form)
              ? 'custom'
              : 'default'
          }
          open={openApplicant}
          setOpen={onOpenApplicationChange}
          isSubmitting={isLoading}
          onSubmit={onFinish}
          disableFields={['email']}
          defaultValue={mappingProfileEmployeeToApplicationForm({
            profile: employeeProfile,
            user
          })}
          schema={schemaInternalApplicationForm(t)}
        />
      )}
      <ReferralModal
        openReferralModal={openReferralModal}
        onClose={onCloseReferralModal}
        defaultValue={referralJob}
        callbackOnFinish={refetch}
      />
    </div>
  )
}

export default OpenJobItem
