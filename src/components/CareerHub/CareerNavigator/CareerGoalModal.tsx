import type { FC } from 'react'
import { useCallback, useEffect, useState } from 'react'
import { Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import { AsyncMultipleSearchWithSelect } from '~/core/ui/AsyncMultipleSearchWithSelect'
import { AsyncSingleSearchWithSelect } from '~/core/ui/AsyncSingleSearchWithSelect'
import { Button } from '~/core/ui/Button'
import { Dialog } from '~/core/ui/Dialog'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import { FormControlItem } from '~/core/ui/FormControlItem'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import type { ISelectOption } from '~/core/ui/Select'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import useCareerNavigator from '~/lib/features/career-hub/hook/use-career-navigator'
import schemaCreateCareerGoal from '~/lib/features/career-hub/schema/create-career-goal-schema'
import { getMatchingPercentageColor } from '~/lib/features/career-hub/utils/matching-percentage'
import useToastStore from '~/lib/store/toast'

type ISelectOptionWithMatching = ISelectOption & {
  matchingData?: {
    totalRate?: number
  }
}

const CareerGoalModal: FC<{
  openCareerGoalModal: boolean
  onClose: () => void
  defaultValue: {
    position: ISelectOptionWithMatching | undefined
    skills: ISelectOption[]
  }
}> = ({ openCareerGoalModal, onClose, defaultValue }) => {
  const { t } = useTranslation()
  const { setToast } = useToastStore()

  const [currentPosition, setCurrentPosition] = useState<ISelectOptionWithMatching | undefined>(defaultValue.position)

  const [aiDescription, setAiDescription] = useState<string>('')
  const [aiMatchingPercentage, setAiMatchingPercentage] = useState<number | null>(null)
  const [isGeneratingAI, setIsGeneratingAI] = useState<boolean>(false)

  const {
    promisePositionsList,
    promiseSkillsOptions,
    triggerAddCareerGoal,
    triggerGetCareerGoal,
    triggerGenerateDescriptionByAI,
    isGeneratingDescription
  } = useCareerNavigator({
    t
  })

  const isGenerating = isGeneratingAI || isGeneratingDescription

  const generateAIDescription = useCallback(
    async (position: ISelectOptionWithMatching, skills: ISelectOption[]) => {
      if (!position) return

      setIsGeneratingAI(true)
      try {
        const result = await triggerGenerateDescriptionByAI({
          targetPosition: position.supportingObj?.name,
          skills: skills.map((skill: ISelectOption) => skill.value)
        })

        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
          return
        }

        const { positionShortDescription } = result.data.positionShortDescription
        if (positionShortDescription) {
          setAiDescription(positionShortDescription.description || '')
          setAiMatchingPercentage(
            typeof positionShortDescription.percentage === 'number' ? positionShortDescription.percentage : null
          )
        }
      } catch (error) {
        console.error('Error generating AI description:', error)
      } finally {
        setIsGeneratingAI(false)
      }
    },
    [triggerGenerateDescriptionByAI, setToast]
  )

  const handleClose = useCallback(() => {
    setAiDescription('')
    setAiMatchingPercentage(null)
    setIsGeneratingAI(false)
    onClose()
  }, [onClose])

  const onSubmit = async (data: { position: ISelectOptionWithMatching; skills: ISelectOption[] }) => {
    const response = await triggerAddCareerGoal({
      positionId: Number(data.position.value),
      skills: data.skills.map((item: ISelectOption) => item?.value)
    })

    if (response.error) {
      catchErrorFromGraphQL({
        error: response.error,
        setToast
      })
    } else {
      triggerGetCareerGoal()
      setToast({
        open: true,
        type: 'success',
        title: `${t('careerHub:careerNavigator:careerGoalSaved')}`
      })
      handleClose()
    }
  }

  useEffect(() => {
    setCurrentPosition(defaultValue.position)
  }, [defaultValue.position])

  return (
    <Dialog
      open={openCareerGoalModal}
      size="sm"
      onOpenChange={handleClose}
      isPreventAutoFocusDialog={true}
      label={`${t('careerHub:careerNavigator:careerGoalModalTitle')}`}>
      <DynamicImportForm
        isShowDebug={false}
        className="w-full"
        schema={schemaCreateCareerGoal(t)}
        defaultValue={defaultValue}
        onSubmit={onSubmit}>
        {({ formState, control, getValues }) => {
          const watchedSkills = getValues('skills') || []

          return (
            <>
              <div className="mb-4">
                <Controller
                  control={control}
                  name="position"
                  render={({ field: { onChange, value } }) => {
                    return (
                      <FormControlItem
                        label={`${t('careerHub:careerNavigator:nextCareerHub')}`}
                        labelRequired
                        destructive={formState.errors && !!formState.errors?.position}
                        destructiveText={formState.errors && (formState.errors?.position?.message as string)}>
                        <AsyncSingleSearchWithSelect
                          isClearable={false}
                          showGhostDropdownIndicator={false}
                          promiseOptions={async (params) => {
                            const result = await promisePositionsList({
                              ...params,
                              skills: watchedSkills.map((skill: ISelectOption) => skill.value)
                            })

                            const enhancedCollection = result.collection.map((item: any) => {
                              const totalRate = item.supportingObj?.matchingData?.totalRate
                              let percentageText = ''

                              if (typeof totalRate === 'number') {
                                const percentage = Math.round(totalRate)
                                const colorCode = getMatchingPercentageColor(percentage)
                                percentageText = `<span style="color: ${colorCode}; font-weight: 400;">${percentage}%</span>`

                                return {
                                  ...item,
                                  supportingObj: {
                                    ...item.supportingObj,
                                    helpName: percentageText
                                  }
                                }
                              }

                              return item
                            })

                            return {
                              ...result,
                              collection: enhancedCollection
                            }
                          }}
                          size="sm"
                          onChange={(data: any) => {
                            onChange(data)

                            setCurrentPosition({
                              ...data,
                              matchingData: data?.supportingObj?.matchingData
                            } as ISelectOptionWithMatching)
                            // Generate AI description when position changes
                          }}
                          value={value}
                          placeholder={`${t('label:placeholder:select')}`}
                          classNameOverride={{
                            input: 'text-gray-900',
                            loadingMessage: `${t('label:loading')}`,
                            noOptionsMessage: `${t('label:noOptions')}`
                          }}
                          configSelectOption={{
                            supportingText: ['name', 'helpName']
                          }}
                          destructive={formState.errors && !!formState.errors?.position}
                        />
                      </FormControlItem>
                    )
                  }}
                />
              </div>
              <div>
                <Controller
                  control={control}
                  name="skills"
                  render={({ field: { onChange, value } }) => {
                    const skillString = JSON.parse(JSON.stringify(value || []))
                    const formatSkills = skillString.map((item: ISelectOption) => {
                      return item
                    })

                    return (
                      <FormControlItem
                        label={`${t('careerHub:careerNavigator:skillsYouHave')}`}
                        destructive={formState.errors && !!formState.errors?.skills}
                        helpIcon={
                          <Tooltip
                            mode="icon"
                            align="center"
                            content={`${t('careerHub:careerNavigator:allSkillsYouHave')}`}>
                            <IconWrapper size={14} name="HelpCircle" className="ml-1 text-gray-400" />
                          </Tooltip>
                        }
                        destructiveText={formState.errors && (formState.errors?.skills?.message as string)}>
                        <AsyncMultipleSearchWithSelect
                          promiseOptions={promiseSkillsOptions}
                          size="sm"
                          onChange={(skillValue) => {
                            onChange(skillValue)
                            // Regenerate AI description when skills change
                            if (currentPosition && skillValue) {
                              generateAIDescription(currentPosition, skillValue as ISelectOption[])
                            }
                          }}
                          placeholder={`${t('label:placeholder:select')}`}
                          value={formatSkills}
                          menuPlacement="bottom"
                          configSelectOption={{
                            option: 'checkbox'
                          }}
                          classNameOverride={{
                            loadingMessage: `${t('label:loading')}`,
                            noOptionsMessage: `${t('label:noOptions')}`
                          }}
                        />
                      </FormControlItem>
                    )
                  }}
                />
              </div>

              {/* AI Analysis Section */}
              <If condition={!!currentPosition}>
                <div className="mt-4">
                  {/* Header: AI analysis + Generate link */}
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                      <circle cx="16" cy="16" r="16" fill="url(#paint0_linear_6619_8111)" />
                      <g clip-path="url(#clip0_6619_8111)">
                        <path
                          d="M21 8.66797C21.1947 8.668 21.3877 8.70628 21.5674 8.78125C21.7464 8.85597 21.9093 8.9649 22.0459 9.10254L22.0469 9.10449L22.8955 9.95215C23.0342 10.0881 23.1442 10.2508 23.2197 10.4297C23.2959 10.6102 23.335 10.8041 23.335 11C23.335 11.1959 23.2959 11.3898 23.2197 11.5703C23.1442 11.7492 23.0342 11.9119 22.8955 12.0479L12.0469 22.8955C11.911 23.034 11.7491 23.1442 11.5703 23.2197C11.3898 23.2959 11.1959 23.3349 11 23.335C10.8041 23.335 10.6102 23.2959 10.4297 23.2197C10.2508 23.1442 10.0881 23.0342 9.95215 22.8955L9.10449 22.0469L9.10254 22.0459C8.96487 21.9092 8.85597 21.7464 8.78125 21.5674C8.70626 21.3876 8.66699 21.1948 8.66699 21C8.667 20.8052 8.70626 20.6124 8.78125 20.4326C8.85598 20.2536 8.96484 20.0908 9.10254 19.9541L9.10449 19.9531L19.9521 9.10449L19.9541 9.10254C20.0908 8.9649 20.2536 8.85594 20.4326 8.78125C20.6123 8.70628 20.8053 8.66797 21 8.66797ZM10.0449 20.8984L10.042 20.9004C10.0288 20.9134 10.0188 20.9292 10.0117 20.9463C10.0046 20.9634 10.001 20.9815 10.001 21C10.001 21.0185 10.0046 21.0367 10.0117 21.0537C10.0188 21.0708 10.0288 21.0866 10.042 21.0996L10.0449 21.1016L10.9043 21.9619C10.9167 21.9746 10.9319 21.9843 10.9482 21.9912C10.9646 21.9981 10.9822 22.002 11 22.002C11.0177 22.0019 11.0354 21.9981 11.0518 21.9912C11.0681 21.9843 11.0833 21.9746 11.0957 21.9619L11.1016 21.9551L18.3896 14.666L17.333 13.6094L10.0449 20.8984ZM20.666 16.667C21.0341 16.667 21.3328 16.965 21.333 17.333V18H22C22.3681 18.0001 22.666 18.2989 22.666 18.667C22.6658 19.035 22.368 19.3329 22 19.333H21.333V20C21.333 20.3682 21.0342 20.667 20.666 20.667C20.298 20.6668 20 20.3681 20 20V19.333H19.333C18.965 19.333 18.6662 19.035 18.666 18.667C18.666 18.2988 18.9649 18 19.333 18H20V17.333C20.0002 16.9651 20.2981 16.6672 20.666 16.667ZM11.333 11.333C11.7012 11.333 12 11.6318 12 12V12.667H12.666C13.0341 12.667 13.3328 12.965 13.333 13.333C13.333 13.7012 13.0342 14 12.666 14H12V14.667C11.9998 15.035 11.7011 15.333 11.333 15.333C10.965 15.333 10.6662 15.035 10.666 14.667V14H10C9.63181 14 9.33301 13.7012 9.33301 13.333C9.33318 12.965 9.63192 12.667 10 12.667H10.666V12C10.666 11.6318 10.9649 11.3331 11.333 11.333ZM20.9463 10.0117C20.9293 10.0188 20.9134 10.0289 20.9004 10.042L20.8975 10.0449L18.2754 12.666L19.333 13.7236L21.9619 11.0957C21.9746 11.0833 21.9843 11.0681 21.9912 11.0518C21.9981 11.0354 22.002 11.0178 22.002 11C22.0019 10.9822 21.9981 10.9646 21.9912 10.9482C21.9843 10.9319 21.9746 10.9167 21.9619 10.9043L21.9551 10.8984L21.1016 10.0449L21.0996 10.042C21.0866 10.0289 21.0708 10.0188 21.0537 10.0117C21.0367 10.0046 21.0184 10.001 21 10.001C20.9816 10.001 20.9633 10.0047 20.9463 10.0117ZM14.666 8.66699C15.0341 8.66699 15.3328 8.96497 15.333 9.33301C15.7012 9.33301 16 9.63181 16 10C16 10.3221 15.7714 10.5911 15.4678 10.6533L15.333 10.667L15.3193 10.8008C15.2572 11.1047 14.9882 11.333 14.666 11.333C14.344 11.3328 14.0757 11.1045 14.0137 10.8008L14 10.667L13.8652 10.6533C13.5615 10.5911 13.333 10.3221 13.333 10C13.333 9.67788 13.5615 9.40889 13.8652 9.34668L14 9.33301C14.0002 8.9651 14.2981 8.66721 14.666 8.66699Z"
                          fill="white"
                        />
                      </g>
                      <defs>
                        <linearGradient
                          id="paint0_linear_6619_8111"
                          x1="0"
                          y1="16"
                          x2="32"
                          y2="16"
                          gradientUnits="userSpaceOnUse">
                          <stop stop-color="#A3CBF6" />
                          <stop offset="1" stop-color="#BFA6F6" />
                        </linearGradient>
                        <clipPath id="clip0_6619_8111">
                          <rect width="16" height="16" fill="white" transform="translate(8 8)" />
                        </clipPath>
                      </defs>
                    </svg>
                    <span className="mx-2 text-sm font-medium text-gray-800">
                      {t('careerHub:careerNavigator:aiAnalysis')}
                    </span>

                    <TextButton
                      underline={false}
                      type="primary"
                      size="md"
                      label={t('common:generate')}
                      onClick={() => {
                        if (!currentPosition || isGenerating) return
                        generateAIDescription(currentPosition, watchedSkills)
                      }}
                      isDisabled={isGenerating}
                    />
                  </div>
                  {isGenerating && (
                    <div className="mt-2 flex items-center text-sm text-gray-900">
                      <IconWrapper
                        size={16}
                        name="Loader2"
                        className="mr-2 animate-spin text-gray-700 dark:text-gray-400"
                      />
                      {t('form:generating_loading')}
                    </div>
                  )}

                  {(() => {
                    const colorCode = getMatchingPercentageColor(Math.round(aiMatchingPercentage || 0))
                    if (aiDescription && aiMatchingPercentage !== null) {
                      return (
                        <div className="mt-2 rounded-[8px] bg-gradient-to-b from-[#F4F1F9] to-[#EEF2F9] p-3">
                          {aiMatchingPercentage !== null && (
                            <div className="text-sm font-medium" style={{ color: colorCode }}>
                              {Math.round(aiMatchingPercentage)}% {t('common:match')}
                            </div>
                          )}

                          <div className="text-sm text-gray-900">
                            <p>{aiDescription}</p>
                          </div>
                        </div>
                      )
                    }
                    return null
                  })()}
                </div>
              </If>

              <div className="flex items-center justify-end pt-6">
                <Button
                  type="secondary"
                  className="mr-3"
                  size="sm"
                  onClick={handleClose}
                  label={`${t('button:cancel')}`}
                />
                <Button
                  isDisabled={false}
                  isLoading={false}
                  size="sm"
                  htmlType="submit"
                  label={`${t('button:save')}`}
                />
              </div>
            </>
          )
        }}
      </DynamicImportForm>
    </Dialog>
  )
}

export default CareerGoalModal
