import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { Container } from '~/core/ui/Container'
import { PageHeaderSimple } from '~/core/ui/PageHeaderSimple'

import QueryTenantPositionNameShow from '~/lib/features/career-hub/graphql/query-tenant-position-goal'
import useAIAnalysis from '~/lib/features/career-hub/hook/use-ai-analysis'
import { CAREERHUB_POSITION_TABS } from '~/lib/features/career-hub/utilities/enum'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useBrowserTab from '~/lib/hooks/use-browser-tab'
import useBoundStore from '~/lib/store'

import HeadMetaTags from '~/components/HeadMetaTags'
import type { FeatureName } from '~/components/Subscription/subscription'
import { withSubscriptionPlanLockFearture } from '~/components/Subscription/SubscriptionPlan'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'

import PositionAnalysis from './PositionAnalysis'
import PositionOverview from './PositionOverview'
import PositionViewJobs from './PositionViewJobs'

const PositionDetails: React.FC<{
  suspend?: boolean
}> = ({ suspend }) => {
  const { t } = useTranslation()
  const router = useRouter()
  const user = useBoundStore((state) => state.user)
  const { isFeatureEnabled } = useSubscriptionPlan()
  const { query } = useRouter()
  const positionID = (query.id as string) || ''
  const { enableAnalysis, disableAnalysis } = useAIAnalysis()
  const [tabs, setTabs] = useState<Array<{ value: string; label: string; index: number }>>([
    {
      value: CAREERHUB_POSITION_TABS.overview,
      label: `${t('candidates:tabs:overview')}`,
      index: 0
    },
    {
      value: CAREERHUB_POSITION_TABS.jobs,
      label: `${t('careerHub:careerNavigator:tabs:jobs')}`,
      index: 3
    }
  ])
  const isShowTabAnalysis = isFeatureEnabled(PLAN_FEATURE_KEYS.employee_profile)

  // Query position only get ID va NAME by query positionID lay URL
  const handleTabChange = (tabValue: string) => {
    const url = `${configuration.path.careerHub.careerNavigatorPositionOverview(
      user?.currentTenant?.slug || '',
      Number(positionID)
    )}?tab=${tabValue}`
    router.push(url)
  }
  const {
    trigger: getCourseDetails,
    isLoading,
    data: position
  } = useQueryGraphQL({
    query: QueryTenantPositionNameShow,
    variables: { id: Number(positionID) }
  })

  useEffect(() => {
    getCourseDetails()
  }, [positionID])

  // Enable analysis when entering position detail page
  useEffect(() => {
    enableAnalysis()

    // Disable analysis when leaving position detail page
    return () => {
      disableAnalysis()
    }
  }, [enableAnalysis, disableAnalysis])

  useEffect(() => {
    if (isShowTabAnalysis) {
      setTabs((prev) =>
        [
          ...prev,
          {
            value: CAREERHUB_POSITION_TABS.analysis,
            label: `${t('careerHub:careerNavigator:tabs:analysis')}`,
            index: 1
          }
        ].sort((a, b) => a.index - b.index)
      )
    }
  }, [isShowTabAnalysis])

  const renderTabContent = () => {
    return (
      <div className="relative h-full">
        {tabControl.value === CAREERHUB_POSITION_TABS.overview && (
          <PositionOverview suspend={suspend} positionID={positionID || ''} />
        )}
        {tabControl.value === CAREERHUB_POSITION_TABS.analysis && isShowTabAnalysis && (
          <PositionAnalysis
            positionName={position?.positionsShow?.name || ''}
            positionID={positionID}
            suspend={suspend}
          />
        )}
        {tabControl.value === CAREERHUB_POSITION_TABS.jobs && (
          <PositionViewJobs suspend={suspend} positionID={positionID || ''} />
        )}
      </div>
    )
  }

  const tabControl = useBrowserTab({
    defaultValue: CAREERHUB_POSITION_TABS.overview,
    queryKeyName: 'tab'
  })

  return (
    <>
      <HeadMetaTags
        title={`${t(`common:seo:careerNavigatorPosition`, {
          PUBLIC_APP_NAME
        })}`}
      />

      <Container overrideClass="max-w-[1216px] h-full pt-6">
        <PageHeaderSimple
          value={tabControl.value}
          classNameWrapper="space-y-4"
          fontSizeTitle="text-2xl"
          onValueChange={handleTabChange}
          title={isLoading ? `${t('settings:positions:title')}` : position?.positionsShow?.name}
          description=""
          tabs={tabs}
        />
        {renderTabContent()}
      </Container>
    </>
  )
}

export default withSubscriptionPlanLockFearture(PositionDetails, PLAN_FEATURE_KEYS.career_navigator as FeatureName)
