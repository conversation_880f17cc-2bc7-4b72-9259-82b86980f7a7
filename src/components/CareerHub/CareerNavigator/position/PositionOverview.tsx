import DOMPurify from 'dompurify'
import { useRouter } from 'next/router'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { Container } from '~/core/ui/Container'
import Empty from '~/core/ui/Empty'
import { IconButton } from '~/core/ui/IconButton'
import IconWrapper from '~/core/ui/IconWrapper'
import type { SuggestionChipsProps } from '~/core/ui/SuggestionChips'
import { SuggestionChips } from '~/core/ui/SuggestionChips'
import { TypographyText } from '~/core/ui/Text'
import { convertLinkFromHTML } from '~/core/utilities/common'

import QueryTenantPositionOverviewShow from '~/lib/features/career-hub/graphql/query-tenant-position-goal'
import { CAREERHUB_POSITION_TABS } from '~/lib/features/career-hub/utilities/enum'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useBoundStore from '~/lib/store'

import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'

export type SkillInProfile = {
  id: number
  name: string
  inProfile: boolean
}

const PositionOverview: React.FC<{
  suspend?: boolean
  positionID: string
}> = ({ suspend, positionID }) => {
  const { t } = useTranslation()
  const user = useBoundStore((state) => state.user)
  const router = useRouter()
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()

  const enableViewCourses =
    isFeatureEnabled(PLAN_FEATURE_KEYS.learning_management_system) &&
    isUnLockFeature(PLAN_FEATURE_KEYS.learning_management_system)

  const { trigger: getCourseDetails, data: position } = useQueryGraphQL({
    query: QueryTenantPositionOverviewShow,
    variables: { id: Number(positionID) }
  })

  const handleTabChange = (tabValue: string) => {
    const url = `${configuration.path.careerHub.careerNavigatorPositionOverview(
      user?.currentTenant?.slug || '',
      Number(positionID)
    )}?tab=${tabValue}`
    router.push(url)
  }
  useEffect(() => {
    getCourseDetails()
  }, [positionID])

  const HtmlDisplay = ({ children, className }: { children?: string; className: string }) => {
    return children ? (
      <div
        dangerouslySetInnerHTML={{
          __html: DOMPurify.sanitize(convertLinkFromHTML(children))
        }}
        className={className}
      />
    ) : (
      <div className={className}>-</div>
    )
  }
  return (
    <>
      <Container>
        <div className="mt-3 flex justify-between space-x-12">
          <div className="w-[804px]">
            {position?.positionsShow?.description ? (
              <div>
                <HtmlDisplay className="prose prose-sm max-w-full text-[15px]">
                  {position?.positionsShow?.description}
                </HtmlDisplay>
              </div>
            ) : (
              <div className="flex h-[calc(100vh_-_350px)] items-center">
                <Empty
                  type="empty-data"
                  title={`${t('label:uSearch:empty:title')}`}
                  description={`${t('careerHub:careerNavigator:noOverviewAvailabel')}`}
                />
              </div>
            )}
          </div>
          <div className="w-[309px]">
            <div className="tablet:p-6 mb-6 rounded border border-gray-300 p-4">
              <TypographyText className="mb-1.5 text-xs text-gray-700 uppercase">
                {t('careerHub:learningLibrary:skills')}
              </TypographyText>
              {(() => {
                const skillsYouHave =
                  position?.positionsShow?.skillRecords?.map((skill: SkillInProfile): SuggestionChipsProps => {
                    let chip = {
                      label: skill.name,
                      maxLength: 25
                    } as SuggestionChipsProps
                    if (skill.inProfile) {
                      chip = {
                        ...chip,
                        startIconMenus: 'CheckCircle2'
                      }
                    }
                    return chip
                  }) || []

                return skillsYouHave.length > 0 ? (
                  <div className="mb-6">
                    <TypographyText tagName="div" className="items-center text-sm text-gray-900">
                      <SuggestionChips
                        size="md"
                        source={skillsYouHave}
                        type="default"
                        startIconMenusClassName="fill-green-500 text-white"
                        classNameChip="line-clamp-none h-auto flex items-center text-left"
                      />
                    </TypographyText>
                  </div>
                ) : (
                  <TypographyText className="mb-6 text-sm text-gray-600">
                    {t('candidates:tabs:candidateOverview:notAvailable')}
                  </TypographyText>
                )
              })()}

              <div className="mb-0">
                <TypographyText className="mb-0.5 text-xs text-gray-700 uppercase">
                  {t('careerHub:careerNavigator:jobOpenings')}
                </TypographyText>
                <span
                  role="button"
                  onClick={() => handleTabChange(CAREERHUB_POSITION_TABS.jobs)}
                  className="cursor-pointer text-blue-600">
                  <span className="text-primary-400 hover:text-primary-600 flex inline-block items-center pl-1 text-sm font-medium">
                    {t('button:viewJobs')}
                    <IconWrapper
                      className="text-primary-400 hover:text-primary-600 ml-1.5"
                      size={16}
                      name="ArrowRight"
                    />
                  </span>
                </span>
              </div>
              {(() => {
                const skillsYouNeedID =
                  position?.positionsShow?.skillRecords
                    ?.filter((skill: SkillInProfile) => !skill.inProfile)
                    .map((skill: SkillInProfile) => skill.id) || []

                const skillsYouNeedName =
                  position?.positionsShow?.skillRecords
                    ?.filter((skill: SkillInProfile) => !skill.inProfile)
                    .map((skill: SkillInProfile) => skill.name) || [].join(' ')

                return (
                  <div className="mt-6">
                    <TypographyText className="mb-0.5 text-xs text-gray-700 uppercase">
                      {t('careerHub:careerNavigator:recommendedCourses')}
                    </TypographyText>

                    {!enableViewCourses || skillsYouNeedID.length === 0 ? (
                      <TypographyText className="mb-0 text-sm text-gray-600">
                        {t('candidates:tabs:candidateOverview:notAvailable')}
                      </TypographyText>
                    ) : (
                      <a
                        href={configuration.path.careerHub.library(
                          user.currentTenant?.slug as string,
                          skillsYouNeedID,
                          skillsYouNeedName.join(' ')
                        )}
                        target="_blank"
                        rel="noopener noreferrer">
                        <span className="text-primary-400 hover:text-primary-600 flex inline-flex items-center pl-1 text-sm font-medium">
                          {t('button:viewCourses')}
                          <IconWrapper
                            className="text-primary-400 hover:text-primary-600 ml-1.5"
                            size={16}
                            name="ArrowRight"
                          />
                        </span>
                      </a>
                    )}
                  </div>
                )
              })()}
            </div>
          </div>
        </div>
      </Container>
    </>
  )
}

export default PositionOverview
