import { useRouter } from 'next/router'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { Button } from '~/core/ui/Button'
import Empty from '~/core/ui/Empty'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import { TypographyText } from '~/core/ui/Text'
import { cn } from '~/core/ui/utils'

import useAIAnalysis from '~/lib/features/career-hub/hook/use-ai-analysis'
import { useAnalysisStore } from '~/lib/features/career-hub/store/analysis-store'
import { ANALYSIS_STATUS } from '~/lib/features/career-hub/utilities/enum'

import ErrorState from './ErrorState'
import MarkdownRenderer from './MarkdownRenderer'
import TypingAnimation from './TypingAnimation'

interface PositionAnalysisProps {
  positionID: string
  suspend?: boolean
  positionName: string
}

const PositionAnalysis: React.FC<PositionAnalysisProps> = ({ positionID, positionName, suspend = false }) => {
  const { t } = useTranslation()
  const router = useRouter()
  const [isTypingComplete, setIsTypingComplete] = useState(false)
  const [isRegenerating, setIsRegenerating] = useState(false)
  const [isButtonClicked, setIsButtonClicked] = useState(false)
  const { analysis, isStreaming, generateAnalysis, regenerateAnalysis, shouldAnimate, resetTypingAnimation } =
    useAIAnalysis()
  const { setData, clearDataAnalysis } = useAnalysisStore()

  // Generate analysis when component mounts
  useEffect(() => {
    // Call generateAnalysis when component is mounted (tab Analysis is active)
    if (!suspend && positionID) {
      generateAnalysis(positionID)
    }
  }, [suspend, positionID, generateAnalysis])

  // Update displayed content when analysis changes (for non-streaming data)
  useEffect(() => {
    if (isButtonClicked && (!isStreaming || analysis?.status !== ANALYSIS_STATUS.loading)) {
      setIsButtonClicked(false)
    }
  }, [analysis?.status, isStreaming, isButtonClicked])

  useEffect(() => {
    if (!isStreaming && analysis?.status === ANALYSIS_STATUS.loading && !analysis?.content) {
      if (analysis) {
        setData({ ...analysis, status: ANALYSIS_STATUS.no_content })
      }
    }
  }, [isStreaming, analysis?.status, analysis?.content, setData])

  // Reset typing state when shouldAnimate changes
  useEffect(() => {
    if (shouldAnimate) {
      setIsTypingComplete(false)
    } else {
      // When not animating, mark as complete
      setIsTypingComplete(true)
    }
  }, [shouldAnimate])

  useEffect(() => {
    return () => {
      clearDataAnalysis()
    }
  }, [])

  // Handle typing animation completion
  const handleTypingComplete = () => {
    setIsTypingComplete(true)
    // Only reset animation and update status when streaming is complete
    if (!isStreaming) {
      resetTypingAnimation()
      if (analysis) {
        setData({
          ...analysis,
          status: ANALYSIS_STATUS.completed
        })
      }
    }
  }

  const handleRegenerate = async () => {
    if (positionID) {
      setIsButtonClicked(true)
      setIsRegenerating(true)
      setIsTypingComplete(false) // Reset typing state
      await regenerateAnalysis(positionID) // Use dedicated regenerate function
      setIsRegenerating(false)
    }
  }

  const LoadingState = () => (
    <div className="flex items-center justify-center p-8">
      <div className="flex flex-col items-center gap-2">
        <IconWrapper name="Loader2" className="text-primary animate-spin" size={24} />
        <TypographyText className="text-gray-500">
          {t('careerHub:analysis:generating') || 'Generating analysis...'}
        </TypographyText>
      </div>
    </div>
  )

  // Initial state or regenerating
  if (analysis === null || (isStreaming && !analysis.content && !isButtonClicked) || isButtonClicked) {
    return <LoadingState />
  }

  // Error state
  if (analysis?.status === ANALYSIS_STATUS.error) {
    return (
      <ErrorState
        icon="AlertCircle"
        title={t('careerHub:analysis.errorTitle') || 'Analysis Error'}
        description={t('careerHub:analysis.errorAnalysis')}
        actionLabel={t('careerHub:analysis.retry') || 'Retry'}
        onAction={handleRegenerate}
      />
    )
  }

  if (analysis?.status === ANALYSIS_STATUS.no_content) {
    const tenant = (router.query?.tenant as string) || ''
    return (
      <div className={cn('flex flex-1 items-center justify-center px-6 pt-4 pb-6', 'h-[calc(100vh_-_300px)]')}>
        <Empty
          type="empty-data"
          title={`${t('careerHub:analysis:empty:title')}`}
          description={`${t('careerHub:analysis:empty:description')}`}
          buttonTitle={`${t('careerHub:analysis:empty:updateProfile')}`}
          onClick={() => router.push(`/careerhub/${tenant}/my-career/profile`)}
        />
      </div>
    )
  }

  return (
    <div className="relative">
      {/* Main Content */}
      <div className="mx-auto w-[800px] bg-white">
        {/* Header */}
        <div className="mb-6 border-b border-gray-200 bg-white pt-3 pb-6">
          <div className="flex items-center">
            <TypographyText className="text-base text-gray-700">
              {t('careerHub:analysis:header', {
                position: positionName || ''
              })}
            </TypographyText>
          </div>
        </div>

        {/* Content */}
        <div className="prose prose-lg max-w-none text-base [&>*:first-child]:mt-0 [&>*:first-child]:mb-2">
          {analysis?.status === 'loading' && analysis?.content ? (
            // Show typing animation for streaming data
            <TypingAnimation content={analysis.content} speed={3} onComplete={handleTypingComplete}>
              {(animatedContent) => <MarkdownRenderer content={animatedContent} />}
            </TypingAnimation>
          ) : analysis?.content ? (
            // Show completed or cached content without animation
            <MarkdownRenderer content={analysis.content} />
          ) : (
            // Empty content
            <div className="flex h-32 items-center justify-center">
              <TypographyText className="text-gray-500">
                {t('careerHub:analysis:emptyContent') || 'Analysis completed but no content available'}
              </TypographyText>
            </div>
          )}
        </div>
      </div>

      {/* Regenerate Button */}
      <If condition={analysis !== null && analysis.status === 'completed' && !isStreaming && isTypingComplete}>
        <div className="pointer-events-none absolute inset-y-0 left-[calc(50%+416px)] z-20">
          <div className="pointer-events-auto sticky top-[calc(100%-55px)]">
            <Button
              onClick={handleRegenerate}
              iconMenus={'RotateCcw'}
              type="secondary"
              size="sm"
              label={t('careerHub:analysis:regenerate') || 'Regenerate'}
              isDisabled={isRegenerating}
              className="inline-flex w-auto min-w-max break-keep whitespace-nowrap"
            />
          </div>
        </div>
      </If>
    </div>
  )
}

export default PositionAnalysis
