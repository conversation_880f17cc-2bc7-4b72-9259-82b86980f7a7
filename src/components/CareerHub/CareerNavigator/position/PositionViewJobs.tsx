import { useTranslation } from 'react-i18next'

import useOpenJobsPosition from '~/lib/features/career-hub/graphql/use-open-jobs-postion'
import useReferralSetting from '~/lib/features/settings/referrals/hooks/useReferralSetting'

import FilterJobsPosition from './jobs/FilterJobsPosition'
import OpenJobsPositionsItem from './jobs/OpenJobsPositionsItem'

const PositionViewJobs: React.FC<{
  suspend?: boolean
  setCount?: (param: number) => void
  positionID: string
}> = ({ suspend, setCount, positionID, ...props }) => {
  const { t } = useTranslation()
  const { openJobsPaging, filterControl, refetch } = useOpenJobsPosition({
    ...props,
    positionRecommendId: positionID
  })
  const { dataReferral } = useReferralSetting()
  const referralPortal = dataReferral?.values?.referral_portal
  return (
    <>
      <div className="mx-auto max-w-[1216px] pt-2">
        <FilterJobsPosition
          filterControl={filterControl}
          positionID={positionID || ''}
          referral_only={referralPortal?.referral_only}
          extras={openJobsPaging.data?.pages?.[0]?.jobsReferableList?.metadata?.extras}
          viewList={
            <OpenJobsPositionsItem filterControl={filterControl} openJobsPaging={openJobsPaging} refetch={refetch} />
          }
        />
      </div>
    </>
  )
}

export default PositionViewJobs
