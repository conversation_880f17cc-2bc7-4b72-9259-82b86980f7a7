import type { Di<PERSON><PERSON>, FC, SetStateAction } from 'react'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { DEFAULT_MOUNT_PAGE_SIZE } from '~/core/constants/enum'
import { But<PERSON> } from '~/core/ui/Button'
import Empty from '~/core/ui/Empty'
import { Skeleton } from '~/core/ui/Skeleton'
import { cn } from '~/core/ui/utils'

import type { IOpenJobsPositionFilter } from '~/lib/features/career-hub/types'
import type { InfinityGraphPageReturnDataType } from '~/lib/features/jobs/hooks/use-infinity-graph-page'
import { SYSTEM_JOB_FIELDS } from '~/lib/features/jobs/utilities/enum'
import useReferralStore from '~/lib/features/referrals/store'
import type { JobDetailType } from '~/lib/features/referrals/types'
import useTenantSettingJobFieldsHook from '~/lib/features/settings/profile-fields/hooks/use-tenant-setting-job-field-hook'

import OpenJobItem from '~/components/CareerHub/[id]/OpenJobItem'

const OpenJobSkeleton = () => (
  <div className={`p-4`}>
    <Skeleton className="mb-4 h-5 w-full rounded" />
    {[1, 2, 3, 4].map((item) => (
      <div key={`task-skeleton-${item}`} className="mt-4">
        <Skeleton className="mb-1.5 h-5 w-full rounded" />
        <Skeleton className="h-[18px] w-full rounded" />
      </div>
    ))}
  </div>
)

type JobReferralPageType = {
  jobsReferableList: {
    collection?: JobDetailType[]
    metadata: {
      totalCount: number
    }
  }
}

const OpenJobsPositionsItem: FC<{
  filterControl: {
    value: IOpenJobsPositionFilter | undefined
    onChange: Dispatch<SetStateAction<IOpenJobsPositionFilter | undefined>>
  }
  openJobsPaging: InfinityGraphPageReturnDataType<JobReferralPageType>
  refetch: InfinityGraphPageReturnDataType<JobReferralPageType>['refetch']
}> = ({ filterControl, openJobsPaging, refetch }) => {
  const { t } = useTranslation()
  const { isShowSystemFieldWithCareerSite } = useTenantSettingJobFieldsHook()
  const setOpenJobsCount = useReferralStore((state) => state.setOpenJobCount)
  const [currentTab, setCurrentTab] = useState<string>()

  const onHandleSelectSkill = (skill: string) => {
    filterControl.onChange({
      ...filterControl.value,
      search: skill
    } as IOpenJobsPositionFilter)
  }

  useEffect(() => {
    setOpenJobsCount(openJobsPaging?.data?.pages?.[0]?.jobsReferableList?.metadata?.totalCount || 0)
  }, [filterControl?.value?.search, openJobsPaging?.data?.pages])

  return (
    <>
      {!openJobsPaging?.data && openJobsPaging?.isFetching ? (
        <OpenJobSkeleton />
      ) : (
        <>
          {openJobsPaging?.data?.pages?.[0]?.jobsReferableList?.metadata.totalCount === 0 ? (
            <div className={cn(`flex h-full min-h-[calc(100vh_-_214px)] items-center`)}>
              {!filterControl?.value?.isFilterTouched ? (
                <Empty
                  size="sm"
                  isShowIcon={false}
                  type="empty-data"
                  title={`${t('careerHub:open_jobs:empty_data:title')}`}
                  description={`${t('careerHub:open_jobs:empty_data:description')}`}
                />
              ) : (
                <Empty
                  size="sm"
                  isShowIcon={false}
                  type="empty-search"
                  title={`${t('careerHub:open_jobs:empty_search:title')}`}
                  description={`${t('careerHub:open_jobs:empty_search:description')}`}
                />
              )}
            </div>
          ) : (
            <div className="mx-auto w-full">
              {(() => {
                const totalCount = openJobsPaging?.data?.pages?.[0]?.jobsReferableList?.metadata?.totalCount || 0

                return (
                  <>
                    {totalCount > 0 && (
                      <div className="align-center my-4 flex flex-wrap justify-between gap-x-2">
                        <div className="text-sm text-gray-600">
                          {totalCount} {t('label:jobs')}
                        </div>
                      </div>
                    )}

                    {totalCount > 0 &&
                      openJobsPaging?.data?.pages?.map((jobsPage: JobReferralPageType) =>
                        jobsPage.jobsReferableList.collection?.map((job: JobDetailType) => (
                          <OpenJobItem
                            key={`open-job-position-${job?.id}`}
                            job={job}
                            isJobsPositionMode={true}
                            onHandleSelectSkill={onHandleSelectSkill}
                            currentTab={currentTab}
                            refetch={refetch}
                            configHideInfo={{
                              tag: !isShowSystemFieldWithCareerSite(SYSTEM_JOB_FIELDS['tags']),
                              publicId: !isShowSystemFieldWithCareerSite(SYSTEM_JOB_FIELDS['public_id'])
                            }}
                          />
                        ))
                      )}
                  </>
                )
              })()}

              {openJobsPaging.hasNextPage ? (
                <div className="mt-4 flex justify-center">
                  <Button
                    size="sm"
                    type="secondary"
                    onClick={() => openJobsPaging.fetchNextPage()}
                    label={t('button:showMore') || ''}
                  />
                </div>
              ) : (
                (openJobsPaging?.data?.pages?.[0]?.jobsReferableList?.metadata?.totalCount || 0) >
                  DEFAULT_MOUNT_PAGE_SIZE && (
                  <p className="mt-4 text-center text-sm text-gray-600 dark:text-gray-300">
                    {t('label:paging:labelEndOfList')}
                  </p>
                )
              )}
            </div>
          )}
        </>
      )}
    </>
  )
}

export default OpenJobsPositionsItem
