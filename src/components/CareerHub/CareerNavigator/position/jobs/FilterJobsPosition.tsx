import debounce from 'lodash/debounce'
import { useRouter } from 'next/router'
import type { Dispatch, FC, SetStateAction } from 'react'
import React, { useEffect, useRef } from 'react'
import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import type { ISelectOption } from '~/core/ui/Select'
import { buildURL } from '~/core/utilities/common'

import type { CountryState, CountryStateExtras, IOpenJobsPositionFilter } from '~/lib/features/career-hub/types'
import { CAREERHUB_POSITION_TABS } from '~/lib/features/career-hub/utilities/enum'
import { renderCombineText } from '~/lib/features/jobs/utilities/common'
import QueryJobLocsByCountryStateList from '~/lib/features/settings/locations/graphql/query-jobs-loc-by-country-state-list'
import useBoundStore from '~/lib/store'

import Filter from '~/components/Filter/Filter'

const FilterJobsPosition: FC<{
  filterControl: {
    value: IOpenJobsPositionFilter | undefined
    onChange: Dispatch<SetStateAction<IOpenJobsPositionFilter | undefined>>
  }
  referral_only?: boolean
  positionID?: string
  viewList?: React.ReactNode
  extras?: CountryStateExtras
}> = ({ filterControl, viewList, extras, positionID }) => {
  const firstLoadRef = useRef(false)
  const router = useRouter()
  const { t, i18n } = useTranslation()

  const { user } = useBoundStore()

  useEffect(() => {
    const debouncedPush = debounce(() => {
      const values = filterControl?.value ?? {}

      const params = Object.keys(values).map((key) => {
        const filterItem = values[key]
        if (filterItem && typeof filterItem === 'object' && 'value' in filterItem) {
          return { [key]: (filterItem as unknown as ISelectOption).value }
        }
        return { [key]: filterItem }
      })

      const url = buildURL(
        configuration.path.careerHub.careerNavigatorPositionOverview(
          user.currentTenant?.slug as string,
          positionID || ''
        ),
        { tab: CAREERHUB_POSITION_TABS.jobs, ...Object.assign({}, ...params) }
      )

      router.push(url, undefined, { shallow: true })
    }, 300)

    debouncedPush()
    return () => debouncedPush.cancel()
  }, [filterControl?.value, user])

  useEffect(() => {
    let valueUpdate: Partial<IOpenJobsPositionFilter> = {}
    if (filterControl.value && extras?.countryStateList && filterControl.value.countryStateIds?.length) {
      const updatedLocations = extras?.countryStateList?.map((item: CountryState) => ({
        id: item.countryStateId,
        value: String(item.countryStateId),
        label: item.country,
        supportingObj: {
          name: renderCombineText([item?.state, item?.country] as string[])
        }
      }))
      valueUpdate['countryStateIds'] = updatedLocations as ISelectOption[]
    }

    if (Object.keys(valueUpdate).length) {
      if (firstLoadRef.current) return
      firstLoadRef.current = true
      filterControl.onChange({
        ...filterControl.value,
        ...valueUpdate
      } as IOpenJobsPositionFilter)
    }
  }, [extras, filterControl])

  return (
    <div className="flex">
      <div className="mx-auto w-full max-w-[1216px]">
        <Filter
          value={filterControl.value}
          onChange={(filterVariable, name) => {
            filterControl.onChange(filterVariable)
          }}>
          <div className="flex w-full items-center">
            <div className="mr-3 w-full">
              <Filter.SearchText
                typingDebounceSubmit={500}
                maxLength={50}
                size="sm"
                placeholder={`${t('label:placeholder:search_by_job_title_skill')}`}
                name="search"
              />
            </div>
            <div>
              <Filter.Combobox
                menuOptionAlign="end"
                menuOptionSide="bottom"
                isSearchable={true}
                isMulti
                buttonClassName="w-[264px] text-gray-900"
                buttonFontWeightClassName="font-normal"
                dropdownMenuClassName="!w-[320px]"
                size="md"
                tooltipOption={{
                  position: 'bottom',
                  content: `${t('tooltip:filter_by_location')}`
                }}
                countName={`${t('label:locations')}`}
                optionsFromDocumentNode={{
                  documentNode: QueryJobLocsByCountryStateList,
                  variable: (searchParams) => ({
                    ...searchParams,
                    tenantSlug: user.currentTenant?.slug
                  }),
                  mapping: (data: {
                    jobLocsByCountryStateList: {
                      collection: {
                        address: string
                        country: string
                        state: string
                        countryStateId: string
                      }[]
                      metadata: {
                        totalCount: number
                      }
                    }
                  }) => {
                    return {
                      metadata: data?.jobLocsByCountryStateList?.metadata,
                      collection: data?.jobLocsByCountryStateList.collection.map((location) => ({
                        id: location.countryStateId,
                        value: String(location.countryStateId),
                        label: location.country,
                        supportingObj: {
                          name: renderCombineText([location?.state, location?.country])
                        }
                      }))
                    }
                  }
                }}
                placeholder={t('job:filter:placeholderSelectLocation') || ''}
                searchPlaceholder={`${t('label:placeholder:search')}`}
                loadingMessage={`${t('label:loading')}`}
                noOptionsMessage={`${t('label:noOptions')}`}
                name="countryStateIds"
              />
            </div>
          </div>
        </Filter>
        <div className="h-full">{viewList}</div>
      </div>
    </div>
  )
}

export default FilterJobsPosition
