'use client'

import { useTranslation } from 'react-i18next'
import type { Disp<PERSON>, FC, SetStateAction } from 'react'
import { useEffect, useState } from 'react'

import { ASC_SORTING, DEFAULT_MOUNT_PAGE_SIZE, DESC_SORTING } from '~/core/constants/enum'
import { Button } from '~/core/ui/Button'
import { Container } from '~/core/ui/Container'
import { DropdownMenu } from '~/core/ui/DropdownMenu'
import Empty from '~/core/ui/Empty'
import If from '~/core/ui/If'
import type { ISelectOption } from '~/core/ui/Select'
import { Skeleton } from '~/core/ui/Skeleton'
import type { SuggestionChipsProps } from '~/core/ui/SuggestionChips'
import { SuggestionInlineChips } from '~/core/ui/SuggestionChips'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { <PERSON><PERSON><PERSON> } from '~/core/ui/Tooltip'
import { cn } from '~/core/ui/utils'
import { convertDecimalHoursToHM, getNameFromEnum, hasAtLeastOneValue, removeHTMLTags } from '~/core/utilities/common'

import type { CourseFilter, CourseType, SkillRecords } from '~/lib/features/career-hub/types'
import type { InfinityGraphPageReturnDataType } from '~/lib/features/jobs/hooks/use-infinity-graph-page'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'

import CourseDetailModal from '~/components/Course/CourseDetailModal'
import DurationDisplay from '~/components/DurationDisplay'
import type { FeatureName } from '~/components/Subscription/subscription'
import { withSubscriptionPlanLockFearture } from '~/components/Subscription/SubscriptionPlan'
import WithComputedMaxItemsChips from '~/components/WithComputedMaxItemsChips'

const LoadingSkeleton = () => (
  <div className={`p-4`}>
    <Skeleton className="mb-4 h-5 w-full rounded" />
    {[1, 2, 3, 4].map((item) => (
      <div key={`task-skeleton-${item}`} className="mt-4">
        <Skeleton className="mb-1.5 h-5 w-full rounded" />
        <Skeleton className="h-[18px] w-full rounded" />
      </div>
    ))}
  </div>
)

type CourseReferralPageType = {
  tenantCoursesList: {
    collections?: CourseType[]
    metadata: {
      totalCount: number
    }
  }
}

const THUMBNAIL_DEFAULT = '/img/feature/careers/default_course.png'

const LearningLibrary: FC<{
  filterControl: {
    value: CourseFilter | undefined
    onChange: Dispatch<SetStateAction<CourseFilter | undefined>>
  }
  getCourse: InfinityGraphPageReturnDataType<CourseReferralPageType>
  refetch: InfinityGraphPageReturnDataType<CourseReferralPageType>['refetch']
  setSorting?: (params: { createdAt?: string; lastActivity?: string; relevant?: string }) => void
  sorting?: { createdAt?: string; lastActivity?: string; relevant?: string }
  filtersOptions?: {
    providerOptions: ISelectOption[]
    levelOptions: ISelectOption[]
    typeOptions: ISelectOption[]
    languagesOptions: ISelectOption[]
    priceOptions: ISelectOption[]
  }
  isResetting?: boolean
}> = ({ filterControl, getCourse, filtersOptions, isResetting, sorting, setSorting }) => {
  const { t } = useTranslation()
  const [totalCount, setTotalCount] = useState<number>(0)
  const [openModal, setOpenModal] = useState<boolean>(false)
  const [courseId, setCourseId] = useState<number | undefined>()

  // ==== totals & states ====
  const total = getCourse?.data?.pages?.[0]?.tenantCoursesList?.metadata?.totalCount ?? 0

  const isInitialLoading =
    (!getCourse.data && getCourse.isLoading) || (!getCourse.data && (isResetting || getCourse.isFetching))

  const isUpdating = !!getCourse.data && (isResetting || getCourse.isFetching)

  useEffect(() => {
    const numberOfCourse = getCourse.data?.pages[0]?.tenantCoursesList.metadata.totalCount || 0
    setTotalCount(numberOfCourse)
  }, [getCourse])

  // RENDER
  if (isInitialLoading) {
    return <LoadingSkeleton />
  }

  if (!isUpdating && total === 0) {
    return (
      <Container overrideClass="flex h-full min-h-[calc(100vh_-_214px)] items-center">
        <Empty
          size="sm"
          isShowIcon={false}
          type="empty-data"
          title={
            hasAtLeastOneValue(filterControl?.value, ['isFilterTouched'])
              ? `${t('careerHub:learningLibrary:emptySearch:title')}`
              : `${t('careerHub:learningLibrary:emptyData:title')}`
          }
          description={
            hasAtLeastOneValue(filterControl?.value, ['isFilterTouched'])
              ? `${t('careerHub:learningLibrary:emptySearch:description')}`
              : `${t('careerHub:learningLibrary:emptyData:description')}`
          }
          className="max-w-full"
        />
      </Container>
    )
  }

  return (
    <>
      <Container overrideClass={cn('h-full')}>
        {Number(total) > 0 ? (
          <div className="align-center mt-4 mb-6 flex flex-wrap justify-between gap-x-2">
            <div className="text-sm text-gray-600">{`${totalCount} ${
              Number(totalCount) > 1 ? t('label:countName:courses') : t('label:countName:course')
            } `}</div>
            <DropdownMenu
              side="bottom"
              sideOffset={8}
              menuClassName="w-[161px] z-[9999]"
              menuOptionClass="pr-0"
              trigger={() => (
                <TextButton
                  type="secondary"
                  size="md"
                  underline={false}
                  iconMenus="ChevronDown"
                  icon="trailing"
                  label={
                    sorting?.createdAt === undefined
                      ? `${t(`careerHub:mostRelevant`)}`
                      : sorting?.createdAt === DESC_SORTING
                        ? `${t('careerHub:newestToOldest')}`
                        : `${t('careerHub:oldestToNewest')}`
                  }
                  className={'text-gray-600 [&>svg]:text-gray-500'}
                  classNameText={'font-normal'}
                />
              )}
              menu={[
                {
                  label: `${t('careerHub:mostRelevant')}`,
                  onClick: () => {
                    setSorting?.({ relevant: DESC_SORTING })
                  }
                },
                {
                  label: `${t('careerHub:newestToOldest')}`,
                  onClick: () => {
                    setSorting?.({ createdAt: DESC_SORTING })
                  }
                },
                {
                  label: `${t('careerHub:oldestToNewest')}`,
                  onClick: () => {
                    setSorting?.({ createdAt: ASC_SORTING })
                  }
                }
              ]}
            />
          </div>
        ) : (
          <div className="mt-4 mb-6 h-[20px] gap-x-2"></div>
        )}
        {getCourse?.data?.pages?.map((coursesOfPage: any) =>
          coursesOfPage.tenantCoursesList.collection.map((course: any) => {
            const duration = convertDecimalHoursToHM(course?.duration)
            return (
              <div
                key={`learning-library-${course.id}`}
                className="item-center mb-5 flex h-[125px] w-full cursor-pointer justify-around bg-white"
                onClick={() => {
                  setCourseId(course.id)
                  setOpenModal(true)
                }}>
                <div
                  className="aspect-[16/9]"
                  style={{
                    minHeight: 125,
                    minWidth: 220
                  }}>
                  <img
                    className="h-full w-full rounded object-cover"
                    src={course.thumbnailVariants?.url || THUMBNAIL_DEFAULT}
                    alt={`${course.title} preview`}
                    onError={(e) => {
                      e.currentTarget.src = THUMBNAIL_DEFAULT
                    }}
                  />
                </div>
                <div className="flex grow flex-col pl-5">
                  <If condition={course.provider}>
                    <div className="mb-2 flex items-center">
                      {course.provider && course.provider !== 'other' && (
                        <img
                          className="mr-1 rounded-full"
                          src={`/img/feature/careers/logo_${course.provider}.svg`}
                          alt={`${course.title}`}
                          style={{
                            height: 16,
                            width: 16
                          }}
                        />
                      )}
                      <TypographyText className="line-clamp-1 text-xs break-all text-gray-700">
                        {getNameFromEnum(filtersOptions?.providerOptions, course?.provider)}
                      </TypographyText>
                    </div>
                  </If>

                  <Tooltip content={course?.title} align="start" sideOffset={0}>
                    <TypographyText className="line-clamp-1 text-base font-medium break-all text-gray-900">
                      {course?.title}
                    </TypographyText>
                  </Tooltip>

                  <If condition={course.description}>
                    <TypographyText className="line-clamp-1 text-sm break-all text-gray-700">
                      {removeHTMLTags(course?.description)}
                    </TypographyText>
                  </If>

                  <If condition={course?.skills?.length > 0}>
                    <div className="flex items-center justify-between py-2">
                      <WithComputedMaxItemsChips
                        totalCount={Object.keys(course?.skillRecords || {}).length || 0}
                        className="relative -mt-2 flex w-[574px] items-center">
                        {({ maxItems }) => {
                          return (
                            <SuggestionInlineChips
                              size="sm"
                              source={
                                (course?.skillRecords || []).map((skill: SkillRecords) => {
                                  let chip: SuggestionChipsProps = {
                                    label: skill.name,
                                    maxLength: 30,
                                    onClick: (e: React.MouseEvent<HTMLDivElement>) => {
                                      {
                                        e && e.stopPropagation()
                                        filterControl.onChange({
                                          skills: [
                                            {
                                              value: skill?.id || '',
                                              supportingObj: {
                                                name: skill.name as string
                                              }
                                            }
                                          ],
                                          isFilterTouched: true
                                        })
                                      }
                                    }
                                  }
                                  if (skill.inProfile) {
                                    chip = {
                                      ...chip,
                                      startIconMenus: 'CheckCircle2'
                                    }
                                  }

                                  return chip
                                }) || []
                              }
                              startIconMenusClassName="fill-green-500 text-white"
                              type="default"
                              maxItems={maxItems}
                            />
                          )
                        }}
                      </WithComputedMaxItemsChips>
                    </div>
                  </If>
                  <div className="flex items-center">
                    <If condition={duration}>
                      <DurationDisplay duration={duration} className="text-xs text-gray-600" />
                      <div className="mx-2 h-0.5 w-0.5 rounded-full bg-gray-400" />
                    </If>

                    <TypographyText className="text-xs text-gray-600">
                      {getNameFromEnum(filtersOptions?.typeOptions, course?.courseType)}
                    </TypographyText>
                    <div className="mx-2 h-0.5 w-0.5 rounded-full bg-gray-400" />
                    <TypographyText className="text-xs text-gray-600">
                      {getNameFromEnum(filtersOptions?.levelOptions, course?.courseLevel)}
                    </TypographyText>
                  </div>
                </div>
              </div>
            )
          })
        )}
        {getCourse.hasNextPage ? (
          <div className="mt-4 flex justify-center">
            <Button
              size="sm"
              type="secondary"
              onClick={() => getCourse.fetchNextPage()}
              label={t('button:showMore') || ''}
            />
          </div>
        ) : (
          (getCourse?.data?.pages?.[0]?.tenantCoursesList?.metadata?.totalCount || 0) > DEFAULT_MOUNT_PAGE_SIZE && (
            <p className="mt-4 text-center text-sm text-gray-600 dark:text-gray-300">
              {t('label:paging:labelEndOfList')}
            </p>
          )
        )}
      </Container>
      <If condition={openModal && courseId}>
        <CourseDetailModal
          open={openModal}
          setOpen={setOpenModal}
          courseId={courseId}
          filterControl={filterControl}
          enumsData={filtersOptions}
        />
      </If>
    </>
  )
}

export default withSubscriptionPlanLockFearture(
  LearningLibrary,
  PLAN_FEATURE_KEYS.learning_management_system as FeatureName
)
