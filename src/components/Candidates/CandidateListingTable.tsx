'use client'

import type { VisibilityState } from '@tanstack/react-table'
import Link from 'next/link'
import type { Dispatch, FC, SetStateAction } from 'react'
import { useEffect, useMemo, useState } from 'react'
import { Trans, useTranslation } from 'react-i18next'

import useEnumsData from 'src/hooks/data/use-enums-data'
import useStaticData from 'src/hooks/data/use-static-data'
import configuration from '~/configuration'
import { AGENCY_TENANT } from '~/core/constants/enum'
import { Avatar } from '~/core/ui/Avatar'
import type { IColorBadgeType } from '~/core/ui/Badge'
import { Badge } from '~/core/ui/Badge'
import { Checkbox } from '~/core/ui/Checkbox'
import type { IDotColorProps } from '~/core/ui/Dot'
import { Dot } from '~/core/ui/Dot'
import { IconButton } from '~/core/ui/IconButton'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import { Popover, PopoverContent, PopoverPortal, PopoverTrigger } from '~/core/ui/Popover'
import { SelectOption } from '~/core/ui/Select'
import { formatDatePickerToDate } from '~/core/ui/SingleDateWithYearOnlyPicker'
import { SuggestionInlineChips } from '~/core/ui/SuggestionChips'
import type { TableInfinityOrderingProps } from '~/core/ui/TableInfinityOrdering'
import type { IPagePagination, IPagePaginationFetcher } from '~/core/ui/TablePagination'
import { TablePagination } from '~/core/ui/TablePagination'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn } from '~/core/ui/utils'
import { removeHTMLTags } from '~/core/utilities/common'
import { defaultFormatDate } from '~/core/utilities/format-date'
import { pushStateBrowser } from '~/core/utilities/is-browser'

import type {
  ICandidateProfile,
  ICandidatesFilter,
  IProfileListType,
  PreferredWorkStatesType
} from '~/lib/features/candidates/types'
import { TalentPoolType } from '~/lib/features/candidates/types'
import { useConvertSalary } from '~/lib/features/candidates/utilities'
import { ASC_SORTING, DESC_SORTING, OPTION_SORT_CREATE_AT_CANDIDATE } from '~/lib/features/candidates/utilities/enum'
import { transformYearsOfExperience } from '~/lib/features/candidates/utilities/index'
import type { IStageType } from '~/lib/features/jobs/types'
import { JOB_COLOR_STAGE_NAME, JOB_DOT_STATUS, MATCHED_RANK_BADGE_COLOR } from '~/lib/features/jobs/utilities/enum'
import usePermissionCandidate from '~/lib/features/permissions/hooks/use-permission-candidate'
import usePermissionJob from '~/lib/features/permissions/hooks/use-permission-job'
import { mappingColorByStageType } from '~/lib/features/settings/hiring-pipelines/utilities/common'
import type { CustomFieldViewType } from '~/lib/features/settings/profile-fields/types/custom-field'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import useBoundStore from '~/lib/store'

import type { FieldSettingType } from '../DisplayConfig'
import type { ISwitchLayoutView } from '../SwitchLayout/SwitchLayoutView'
import WithComputedMaxItemsChips from '../WithComputedMaxItemsChips'
import type { ModalMatchedRankType } from './Profile/components/Recommendation/MatchedRankDetail'
import SocialLinks from './SocialLinks'
import WithComputedMaxColumnJob from './WithComputedMaxColumnJob'

const CandidateListingTable: FC<{
  tableType: string
  calcHeightScroll: string | number
  isTalentPool?: boolean
  hiddenColumns?: string[]
  setOpenCreateCandidate: (value: boolean) => void
  data?: IPagePagination
  fetcher?: IPagePaginationFetcher
  isFetching: boolean
  filter?: ICandidatesFilter | undefined
  clearFilter: () => void
  sorting?: {
    [key: string]: string | null
  }
  setSorting?: (params: { [key: string]: string | null }) => void
  configUserDisplay?: FieldSettingType[]
  classNameTable?: string
  classNameEmpty?: string
  emptyConfig?: TableInfinityOrderingProps['emptyConfig']
  columnSize?: { [key: string]: number }
  actions?: {
    configSwitchLayout?: {
      path: Array<string>
      redirectUrls: Array<string>
    }
    setConfigSwitchLayout?: (param: { path: Array<string>; redirectUrls: Array<string> }) => void
    switchView?: ISwitchLayoutView
    setSwitchView?: (param: ISwitchLayoutView) => void
    setModalMatchedRank?: Dispatch<SetStateAction<ModalMatchedRankType>>
  }
  talentPoolId?: string
  enableRowSelection?: boolean
  tableRef?: (tableEditor: any) => void
  mappingsFilterProfileField?: CustomFieldViewType[]
}> = ({
  tableType = 'candidate',
  calcHeightScroll,
  isTalentPool = false,
  hiddenColumns = [],
  setOpenCreateCandidate,
  data,
  fetcher,
  isFetching,
  filter,
  clearFilter,
  sorting,
  setSorting,
  configUserDisplay,
  classNameTable = '',
  classNameEmpty = '',
  emptyConfig,
  columnSize,
  actions,
  talentPoolId,
  enableRowSelection = false,
  tableRef,
  mappingsFilterProfileField
}) => {
  const { setBulkValues, setBulkSelectedAll, bulkSelectedAll, bulkValues, resetBulkValues, user } = useBoundStore()
  const { numberWithCommas } = useConvertSalary()
  const { t, i18n } = useTranslation()
  const [showTable, setShowTable] = useState(false)
  const stageTypes = useStaticData({
    keyName: 'agency_stageTypes',
    locale: i18n.language
  })
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })

  const { actionProfile } = usePermissionCandidate({})
  const { actionTalentPool } = usePermissionJob()
  const profileLevel = useEnumsData({
    enumType: 'ProfileProfileLevel',
    locale: i18n.language
  })
  const action = isTalentPool ? actionTalentPool : tableType === 'recommendation' ? { create: false } : actionProfile

  const visibleColumns = useMemo(() => {
    const obj: VisibilityState = {}
    configUserDisplay &&
      configUserDisplay.map((field: FieldSettingType) => {
        if (!field?.filter) {
          obj[field?.accessorKey] = !!field?.visibleValue
        }
      })

    if (enableRowSelection) {
      obj.rowSelection = true
    }

    return obj
  }, [configUserDisplay, enableRowSelection])

  const filterConfigUserDisplay = useMemo(() => {
    let results: FieldSettingType[] = []
    if (configUserDisplay) {
      results = configUserDisplay.filter((item) => !item.isDefaultField)
    }

    return results
  }, [configUserDisplay])

  const columns = useMemo(() => {
    const getCustomFieldSize = (fieldKind: string) => {
      if (['string', 'array'].includes(fieldKind)) {
        return 240
      } else if (['number', 'boolean'].includes(fieldKind)) {
        return 160
      } else if (fieldKind === 'date') {
        return 180
      } else return 320 // fieldKind === text || multiple
    }

    const defaultColumns = [
      {
        accessorKey: 'rowSelection',
        header: () => (
          <Checkbox
            isChecked={bulkSelectedAll}
            onCheckedChange={(e) => {
              const { checked } = e.target
              setBulkSelectedAll(checked)
              if (checked) {
                let ids: string[] = []
                data?.data?.map((candidate: ICandidateProfile) => {
                  ids = [...ids, String(candidate.id)]
                })
                setBulkValues(ids)
              } else resetBulkValues()
            }}
            size="sm"
            className="mt-0 mr-1.5 flex items-center"
          />
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => (
          <Checkbox
            isChecked={bulkValues?.includes(info.row.original.id?.toString() || '')}
            onCheckedChange={(e) => {
              const { checked } = e.target
              const id = info?.row?.original?.id?.toString() || ''
              let newList = [...(bulkValues || [])]
              if (checked) {
                newList = [...newList, id]
              } else {
                newList = newList.filter((i) => i !== id)
              }
              setBulkValues(newList)
              setBulkSelectedAll(false)
            }}
            size="sm"
            className="mt-0 mr-1.5 flex items-center"
          />
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: 28,
        maxSize: 28
      },
      {
        accessorKey: 'publicId',
        header: () => {
          if (tableType === 'candidate') {
            // eslint-disable-next-line react-hooks/rules-of-hooks
            const [openSort, setOpenSort] = useState<boolean>(false)
            const iconHeader =
              sorting?.publicId === DESC_SORTING
                ? 'ArrowDown'
                : sorting?.publicId === ASC_SORTING
                  ? 'ArrowUp'
                  : 'MoreHorizontal'

            return (
              <Popover open={openSort} onOpenChange={(value) => setOpenSort(value)}>
                <PopoverTrigger asChild>
                  <div
                    className={`-mx-3 -my-[9px] flex items-center px-3 py-[9px] hover:cursor-pointer ${
                      openSort ? 'bg-gray-50' : ''
                    }`}>
                    <span className="mr-2 text-left text-xs font-normal text-gray-600">
                      <Trans i18nKey={'candidates:candidateTable:publicId'} />
                    </span>
                    <IconWrapper name={openSort ? 'MoreHorizontal' : iconHeader} size={14} className="text-gray-400" />
                  </div>
                </PopoverTrigger>

                <PopoverPortal>
                  <PopoverContent align="end" sideOffset={10} className="w-[158px] p-1">
                    {OPTION_SORT_CREATE_AT_CANDIDATE.map((item, index) => (
                      <div
                        className="hover:bg-gray-50"
                        key={index}
                        onClick={(e) => {
                          if (setSorting) {
                            setSorting({
                              publicId: item.value
                            })
                          }
                        }}>
                        <SelectOption
                          option="radio"
                          icon={true}
                          size="sm"
                          data={{
                            ...item,
                            supportingObj: {
                              name: `${t(`candidates:publicIdOrder:${item.value}`)}`
                            }
                          }}
                          isSelected={sorting?.publicId === item.value}
                          isFocused={sorting?.publicId === item.value}
                          isOption={true}
                          isHeading={false}
                        />
                      </div>
                    ))}
                  </PopoverContent>
                </PopoverPortal>
              </Popover>
            )
          }

          return (
            <span className="line-clamp-1">
              <Trans i18nKey={'candidates:candidateTable:publicId'} />
            </span>
          )
        },
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => (
          <div className="flex">
            <TypographyText className="text-sm text-gray-900">
              {info.row.original.permittedFields?.publicId.value || '-'}
            </TypographyText>
          </div>
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['publicId'] || 100
      },
      {
        accessorKey: 'fullName',
        header: () => {
          if (tableType === 'candidate') {
            // eslint-disable-next-line react-hooks/rules-of-hooks
            const [openSort, setOpenSort] = useState<boolean>(false)
            const iconHeader =
              sorting?.fullName === DESC_SORTING
                ? 'ArrowDown'
                : sorting?.fullName === ASC_SORTING
                  ? 'ArrowUp'
                  : 'MoreHorizontal'

            return (
              <Popover open={openSort} onOpenChange={(value) => setOpenSort(value)}>
                <PopoverTrigger asChild>
                  <div
                    className={`-mx-3 -my-[9px] flex items-center px-3 py-[9px] hover:cursor-pointer ${
                      openSort ? 'bg-gray-50' : ''
                    }`}>
                    <span className="mr-2 text-left text-xs font-normal text-gray-600">
                      <Trans i18nKey={'candidates:candidateTable:fullName'} />
                    </span>
                    <IconWrapper name={openSort ? 'MoreHorizontal' : iconHeader} size={14} className="text-gray-400" />
                  </div>
                </PopoverTrigger>

                <PopoverPortal>
                  <PopoverContent align="end" sideOffset={10} className="w-[158px] p-1">
                    {OPTION_SORT_CREATE_AT_CANDIDATE.map((item, index) => (
                      <div
                        className="hover:bg-gray-50"
                        key={index}
                        onClick={(e) => {
                          if (setSorting) {
                            setSorting({
                              fullName: item.value
                            })
                          }
                        }}>
                        <SelectOption
                          option="radio"
                          icon={true}
                          size="sm"
                          data={{
                            ...item,
                            supportingObj: {
                              name: `${t(`candidates:fullNameOrder:${item.value}`)}`
                            }
                          }}
                          isSelected={sorting?.fullName === item.value}
                          isFocused={sorting?.fullName === item.value}
                          isOption={true}
                          isHeading={false}
                        />
                      </div>
                    ))}
                  </PopoverContent>
                </PopoverPortal>
              </Popover>
            )
          }

          return (
            <span className="flex items-center text-left text-xs font-normal text-gray-600">
              <Tooltip align="start" content={t('candidates:candidateTable:fullName')}>
                <Trans i18nKey={'candidates:candidateTable:fullName'} />
              </Tooltip>
            </span>
          )
        },
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => (
          <div>
            <div className="flex items-center">
              <div className="relative mr-2">
                <Tooltip
                  classNameConfig={{
                    content: info?.row?.original?.employeeId ? '' : 'hidden'
                  }}
                  content={`${t('candidates:candidateTable:tooltips:internal')}`}>
                  <Avatar
                    src={info.row.original.avatarVariants?.thumb?.url}
                    alt={info.row.original.fullName}
                    size="sm"
                    color="#FFFFFF"
                  />
                  {info?.row?.original?.employeeId ? (
                    <div className="absolute right-0 bottom-0">
                      <svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clipPath="url(#clip0_28686_13473)">
                          <path
                            d="M4 0C6.21053 0 8 1.79257 8 4.00929C8 6.21362 6.20124 8.0031 3.99071 8C1.78328 7.9969 0 6.20124 0 3.98142C0 1.78947 1.79876 0 4 0ZM5.85758 4.12384C5.91641 4.12384 5.96594 4.12384 6.01548 4.12384C6.16409 4.11765 6.28173 4.03406 6.32508 3.90402C6.37152 3.7709 6.33127 3.64087 6.21362 3.5418C5.6161 3.04334 5.02167 2.5418 4.42415 2.04334C4.16409 1.82663 3.82972 1.82972 3.56966 2.04644C2.97833 2.5418 2.3839 3.04025 1.79257 3.5356C1.66873 3.63777 1.62848 3.76471 1.67183 3.90093C1.71827 4.03715 1.83591 4.11765 1.9969 4.12074C2.04334 4.12074 2.08978 4.12074 2.13932 4.12074V4.22291C2.13932 4.73375 2.13932 5.24458 2.13932 5.75232C2.13932 5.98452 2.26935 6.11455 2.50155 6.11455C2.7678 6.11455 3.03406 6.11455 3.29721 6.11455C3.32198 6.11455 3.34675 6.11455 3.37771 6.10836C3.37771 6.06502 3.37771 6.03406 3.37771 6C3.37771 5.65015 3.37152 5.30341 3.39009 4.95356C3.40557 4.64706 3.68421 4.41176 4 4.41486C4.31579 4.41486 4.59133 4.65016 4.60681 4.95975C4.62229 5.3096 4.6161 5.66254 4.6161 6.01238C4.6161 6.04334 4.6161 6.0743 4.6161 6.10836C4.64706 6.10836 4.67183 6.11455 4.6935 6.11455C4.96285 6.11455 5.2291 6.11455 5.49845 6.11455C5.72446 6.11455 5.85449 5.98142 5.85449 5.75232C5.85449 5.36842 5.85449 4.98142 5.85449 4.59752C5.85449 4.44272 5.85449 4.28483 5.85449 4.11765L5.85758 4.12384Z"
                            fill="#8B5CF6"
                          />
                          <path
                            d="M5.85758 4.12385C5.85758 4.29104 5.85758 4.44893 5.85758 4.60373C5.85758 4.98763 5.85758 5.37463 5.85758 5.75853C5.85758 5.98763 5.72755 6.11766 5.50154 6.12076C5.23219 6.12076 4.96594 6.12076 4.69659 6.12076C4.67492 6.12076 4.65015 6.12076 4.61919 6.11457C4.61919 6.08051 4.61919 6.04955 4.61919 6.01859C4.61919 5.66875 4.62538 5.31581 4.6099 4.96596C4.59442 4.65636 4.31888 4.42107 4.00309 4.42107C3.6873 4.42107 3.40866 4.65327 3.39318 4.95977C3.3777 5.30652 3.3839 5.65636 3.3808 6.00621C3.3808 6.04026 3.3808 6.07432 3.3808 6.11457C3.34984 6.11457 3.32507 6.12076 3.3003 6.12076C3.03405 6.12076 2.7678 6.12076 2.50464 6.12076C2.27244 6.12076 2.14241 5.99073 2.14241 5.75853C2.14241 5.24769 2.14241 4.73686 2.14241 4.22912V4.12695C2.08978 4.12695 2.04643 4.12695 2 4.12695C1.839 4.12385 1.72136 4.04336 1.67492 3.90714C1.62848 3.77091 1.67182 3.64398 1.79566 3.54181C2.38699 3.04646 2.98142 2.5511 3.57275 2.05265C3.83281 1.83283 4.16718 1.82974 4.42724 2.04955C5.02476 2.548 5.61919 3.04955 6.21671 3.548C6.33436 3.64707 6.37461 3.77711 6.32817 3.91023C6.28482 4.04026 6.16718 4.12386 6.01857 4.13005C5.96904 4.13005 5.9195 4.13005 5.86068 4.13005L5.85758 4.12385Z"
                            fill="white"
                          />
                        </g>
                        <defs>
                          <clipPath id="clip0_28686_13473">
                            <rect width="8" height="8" fill="white" />
                          </clipPath>
                        </defs>
                      </svg>
                    </div>
                  ) : null}
                </Tooltip>
              </div>
              <div className="line-clamp-1">
                <Tooltip content={info.row.original.fullName}>
                  <TypographyText
                    className="mr-2 cursor-pointer truncate text-sm font-medium text-gray-900 hover:underline"
                    onClick={() => {
                      if (actions?.setSwitchView) {
                        actions?.setSwitchView({
                          id: info.row.original?.id,
                          view: 'candidates'
                        })
                        pushStateBrowser({
                          state: {
                            id: info.row.original?.id,
                            talentPoolId: talentPoolId
                          },
                          unused: '',
                          url: configuration.path.candidates.detail(info.row.original?.id)
                        })
                      } else {
                        window.open(configuration.path.candidates.detail(info.row.original?.id), '_blank')
                      }
                    }}>
                    {info.row.original.fullName}
                  </TypographyText>
                </Tooltip>
              </div>
              {info.row.original.applicants?.find((item) => item.flagNew) && (
                <Badge color="green" radius="circular" size="sm" classNameText="whitespace-nowrap">
                  {t('label:new')}
                </Badge>
              )}
            </div>
          </div>
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configUserDisplay
          ? Object.values(visibleColumns || [])?.filter((item) => !!item).length > 4
            ? 240
            : window.innerWidth - 900
          : columnSize?.['fullName'] || 240
      },
      {
        accessorKey: 'matchRank',
        header: () => (
          <span className="line-clamp-1">
            <Trans i18nKey={'candidates:candidateTable:matchRank'} />
          </span>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const rank = info.row.original.recommendationMatchedFields?.filter((r) => r.field === 'total')[0]
          if (rank)
            return (
              <div
                className="inline-flex flex-none cursor-pointer flex-col items-start"
                onClick={() => {
                  actions?.setModalMatchedRank &&
                    actions?.setModalMatchedRank({
                      open: true,
                      listRecommend: info.row.original.recommendationMatchedFields || [],
                      modalDetail: {
                        title: `${t('candidates:tabs:candidateRecommendation:detailModal:title')}`,
                        description: (
                          <Trans
                            i18nKey={'job:detail:recommendation:detailModal:description'}
                            values={{
                              fullName: info.row.original.fullName
                            }}>
                            <span className="font-medium text-gray-900" />
                          </Trans>
                        )
                      }
                    })
                }}>
                <Badge
                  color={MATCHED_RANK_BADGE_COLOR[rank.total_rate_string]}
                  size="md"
                  classNameText="text-white font-medium">
                  {rank?.total_rate_string} ({rank.total_rate}%)
                </Badge>
              </div>
            )
          return <div />
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['matchRank'] || 220
      },
      {
        accessorKey: 'headline',
        header: () => (
          <span className="line-clamp-1">
            <Tooltip align="start" content={t('candidates:candidateTable:headline')}>
              <Trans i18nKey={'candidates:candidateTable:headline'} />
            </Tooltip>
          </span>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => (
          <div className="flex">
            <Tooltip
              position="bottom"
              content={info.row.original.headline}
              classNameConfig={{
                content: info.row.original.headline ? '' : 'hidden'
              }}>
              <TypographyText className="line-clamp-1 text-sm text-gray-900">
                {info.row.original.headline || '-'}
              </TypographyText>
            </Tooltip>
          </div>
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['headline'] || 240
      },

      {
        accessorKey: 'departments',
        header: () => (
          <Tooltip align="start" content={t('candidates:candidateTable:departments')}>
            <Trans i18nKey={'candidates:candidateTable:departments'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const departments: string[] = info?.row?.original?.permittedFields?.departments?.value?.departments || []

          const allDepartments = info?.row?.original?.permittedFields?.departments?.value?.all_departments

          if (allDepartments) {
            return (
              <TypographyText className="truncate text-sm text-gray-900 dark:text-gray-300">
                {isCompanyKind ? `${t('settings:team:allTeams')}` : `${t('settings:departments:allDepartments')}`}
              </TypographyText>
            )
          }
          const suggestionChipDepartments = departments.map((dep) => ({
            label: dep,
            maxLength: 30
          }))

          return (
            <>
              {suggestionChipDepartments.length > 0 ? (
                <WithComputedMaxItemsChips
                  totalCount={suggestionChipDepartments.length}
                  className="relative -mt-2 flex w-[calc(100%-40px)] items-center">
                  {({ maxItems }) => (
                    <SuggestionInlineChips
                      size="sm"
                      source={suggestionChipDepartments}
                      type="default"
                      maxItems={maxItems}
                    />
                  )}
                </WithComputedMaxItemsChips>
              ) : (
                <TypographyText className="text-sm text-gray-900">-</TypographyText>
              )}
            </>
          )
        },

        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['departments'] || 320
      },
      {
        accessorKey: 'email',
        header: () => (
          <span className="line-clamp-1">
            <Tooltip align="start" content={t('candidates:candidateTable:email')}>
              <Trans i18nKey={'candidates:candidateTable:email'} />
            </Tooltip>
          </span>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => (
          <div className="flex items-center">
            <Tooltip classNameAsChild="truncate text-sm text-gray-900" content={info.row.original.email}>
              {info.row.original.email?.[0] || '-'}
            </Tooltip>
          </div>
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['email'] || 240
      },
      {
        accessorKey: 'phoneNumber',
        header: () => (
          <Tooltip align="start" content={t('candidates:candidateTable:phoneNumber')}>
            <Trans i18nKey={'candidates:candidateTable:phoneNumber'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => (
          <div>
            <div className="flex items-center">
              <TypographyText className="text-sm text-gray-900">{info.row.original.phoneNumber || '-'}</TypographyText>
            </div>
          </div>
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['phoneNumber'] || 160
      },
      {
        accessorKey: 'location',
        header: () => (
          <Tooltip align="start" content={t('candidates:candidateTable:location')}>
            <Trans i18nKey={'candidates:candidateTable:location'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => (
          <div className="flex">
            <Tooltip
              content={info.row.original.permittedFields?.location?.value}
              classNameConfig={{
                content: info.row.original.permittedFields?.location?.value ? '' : 'hidden'
              }}>
              <TypographyText className="line-clamp-1 text-sm text-gray-900">
                {info.row.original.permittedFields?.location?.value || '-'}
              </TypographyText>
            </Tooltip>
          </div>
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['location'] || 240
      },
      {
        accessorKey: 'links',
        header: () => (
          <Tooltip align="start" content={t('candidates:candidateTable:links')}>
            <Trans i18nKey={'candidates:candidateTable:links'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          return (
            <>
              {Object.keys(info?.row?.original?.links || {}).length > 0 ? (
                <SocialLinks source={info?.row?.original?.links || {}} maxItems={5} />
              ) : (
                <TypographyText className="text-sm text-gray-900">-</TypographyText>
              )}
            </>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['links'] || 194
      },
      {
        accessorKey: 'resume',
        header: () => (
          <Tooltip align="start" content={t('candidates:candidateTable:resume')}>
            <Trans i18nKey={'candidates:candidateTable:resume'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const profile = info?.row?.original?.profileCvs
          return (
            <div className="flex">
              {profile?.length > 0 ? (
                <Tooltip content={profile?.[0]?.attachments?.[0]?.blobs?.filename}>
                  <IconButton
                    size="xs"
                    type="secondary"
                    iconMenus="Paperclip"
                    onClick={() => {
                      window.open(profile?.[0]?.attachments?.[0]?.file, '_blank')
                    }}
                  />
                </Tooltip>
              ) : (
                <TypographyText className="text-sm text-gray-900">-</TypographyText>
              )}
            </div>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['resume'] || 100
      },
      {
        accessorKey: 'tag',
        header: () => (
          <Tooltip align="start" content={t('candidates:candidateTable:tag')}>
            <Trans i18nKey={'candidates:candidateTable:tag'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          return (
            <>
              {info?.row?.original?.tags?.length > 0 ? (
                <WithComputedMaxItemsChips
                  totalCount={info?.row?.original?.tags?.length}
                  className="relative -mt-2 flex w-[calc(100%-40px)] items-center">
                  {({ maxItems }) => (
                    <SuggestionInlineChips
                      size="sm"
                      source={info?.row?.original?.tags?.map((item) => ({
                        label: item?.name,
                        maxLength: 30
                      }))}
                      type="default"
                      maxItems={maxItems}
                    />
                  )}
                </WithComputedMaxItemsChips>
              ) : (
                <TypographyText className="text-sm text-gray-900">-</TypographyText>
              )}
            </>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['tag'] || 320
      },
      {
        accessorKey: 'talentPool',
        header: () => (
          <Tooltip align="start" content={t('candidates:candidateTable:talentPools')}>
            <Trans i18nKey={'candidates:candidateTable:talentPools'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const talentPools = info?.row?.original?.permittedFields?.talentPools?.value || []
          return (
            <>
              {talentPools?.length > 0 ? (
                <WithComputedMaxItemsChips
                  totalCount={talentPools?.length}
                  className="relative -mt-2 flex w-[calc(100%-40px)] items-center">
                  {({ maxItems }) => (
                    <SuggestionInlineChips
                      size="sm"
                      source={talentPools?.map((item, index) => ({
                        label: item?.name,
                        maxLength: 25,
                        onClick: () => {
                          if (index !== undefined && index !== 9999) {
                            window.open(`${configuration.path.talentPool.detail(Number(item?.id))}`, '_blank')
                          }
                        }
                      }))}
                      type="default"
                      maxItems={maxItems}
                    />
                  )}
                </WithComputedMaxItemsChips>
              ) : (
                <TypographyText className="text-sm text-gray-900">-</TypographyText>
              )}
            </>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['talentPool'] || 320
      },
      {
        accessorKey: 'jobs',
        header: () => (
          <Tooltip align="start" content={t('candidates:candidateTable:jobs')}>
            <Trans i18nKey={'candidates:candidateTable:jobs'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          return (
            <div className="flex items-center">
              {(info.row.original.applicants || [])?.length > 1 ? (
                <Popover>
                  <PopoverTrigger asChild>
                    <TextButton
                      label={`${t('candidates:candidateTable:countJobs', {
                        count: info.row.original.applicants?.length
                      })}`}
                    />
                  </PopoverTrigger>

                  <PopoverPortal>
                    <PopoverContent
                      align="start"
                      sideOffset={10}
                      className="w-[320px] p-1"
                      onOpenAutoFocus={(e) => e.preventDefault()}>
                      {(info.row.original.applicants || []).map((item, index) => {
                        return (
                          <div key={index} className="px-2 py-1.5">
                            <div className="flex space-x-1.5">
                              <Tooltip
                                classNameAsChild="flex pt-1.5 hover:cursor-pointer"
                                content={item?.job?.statusDescription}>
                                {item && (
                                  <Dot
                                    size="xl"
                                    color={JOB_DOT_STATUS(item?.job?.status || 'gray') as IDotColorProps}
                                  />
                                )}
                              </Tooltip>
                              <div>
                                {item?.job?.currentUserAccessible ? (
                                  <WithComputedMaxColumnJob
                                    className="flex flex-1"
                                    maxWidth={284}
                                    leftContent={
                                      <Link href={configuration.path.jobs.detail(Number(item.job?.id))}>
                                        <div>
                                          <Tooltip content={item?.job?.title}>
                                            <TypographyText className="max-w-max truncate text-sm text-gray-900 hover:underline">
                                              {item?.job?.title}
                                            </TypographyText>
                                          </Tooltip>
                                        </div>
                                      </Link>
                                    }
                                    rightContent={
                                      <If condition={item?.job?.permittedFields?.company?.value?.name}>
                                        <Tooltip content={item?.job?.permittedFields?.company?.value?.name}>
                                          <TypographyText className="ml-1 max-w-max truncate text-sm text-gray-900">
                                            ({item?.job?.permittedFields?.company?.value?.name})
                                          </TypographyText>
                                        </Tooltip>
                                      </If>
                                    }
                                  />
                                ) : (
                                  <WithComputedMaxColumnJob
                                    className="flex flex-1"
                                    maxWidth={284}
                                    leftContent={
                                      <Tooltip
                                        classNameConfig={{
                                          content: 'max-w-[367px]'
                                        }}
                                        content={
                                          <>
                                            <div>{item?.job?.title}</div>
                                            <div>{t('common:you_not_in_hiring_team')}</div>
                                          </>
                                        }>
                                        <TypographyText className="max-w-max truncate text-sm text-gray-600">
                                          {item?.job?.title}
                                        </TypographyText>
                                      </Tooltip>
                                    }
                                    rightContent={
                                      item?.job?.permittedFields?.company?.value?.name && (
                                        <Tooltip content={item?.job?.permittedFields?.company?.value?.name}>
                                          <TypographyText className="ml-1 max-w-max truncate text-sm text-gray-900">
                                            ({item?.job?.permittedFields?.company?.value?.name})
                                          </TypographyText>
                                        </Tooltip>
                                      )
                                    }
                                  />
                                )}

                                <div>
                                  {item?.status === 'rejected' ? (
                                    <div className="flex items-center space-x-1.5">
                                      <IconWrapper name="Ban" size={14} className="flex-none text-red-500" />
                                      <Tooltip content={item?.rejectedReasonLabel}>
                                        <TypographyText className="line-clamp-1 text-xs text-gray-700">
                                          {item?.rejectedReasonLabel}
                                        </TypographyText>
                                      </Tooltip>
                                    </div>
                                  ) : (
                                    <TypographyText className="flex items-center text-xs text-gray-700">
                                      {item?.jobStage?.stageLabel}
                                    </TypographyText>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        )
                      })}
                    </PopoverContent>
                  </PopoverPortal>
                </Popover>
              ) : (
                <div className="flex w-full items-center space-x-1.5">
                  <Tooltip
                    classNameAsChild="flex items-center hover:cursor-pointer"
                    content={info.row.original.applicants?.[0]?.job?.statusDescription}>
                    {info.row.original?.applicants?.[0] && (
                      <Dot
                        size="sm"
                        color={
                          JOB_DOT_STATUS(info.row.original.applicants?.[0]?.job?.status || 'gray') as IDotColorProps
                        }
                      />
                    )}
                  </Tooltip>

                  {info.row.original?.applicants && info.row.original?.applicants?.length > 0 ? (
                    <>
                      {info.row.original?.applicants?.[0]?.job?.currentUserAccessible ? (
                        <WithComputedMaxColumnJob
                          className="flex w-[284px] flex-1"
                          leftContent={
                            <Tooltip content={info.row.original?.applicants?.[0]?.job?.title}>
                              <Link
                                href={configuration.path.jobs.detail(
                                  Number(info.row.original?.applicants && info.row.original?.applicants[0]?.job?.id)
                                )}>
                                <TypographyText className="max-w-max truncate text-sm text-gray-900 hover:underline">
                                  {info.row.original?.applicants[0]?.job?.title}
                                </TypographyText>
                              </Link>
                            </Tooltip>
                          }
                          rightContent={
                            info.row.original?.applicants?.[0]?.job?.company?.permittedFields?.name?.value && (
                              <Tooltip
                                content={
                                  info.row.original?.applicants?.[0]?.job?.company?.permittedFields?.name?.value
                                }>
                                <TypographyText className="ml-1 max-w-max truncate text-sm text-gray-600">
                                  ({info.row.original?.applicants[0]?.job?.company?.permittedFields?.name?.value})
                                </TypographyText>
                              </Tooltip>
                            )
                          }
                        />
                      ) : (
                        <WithComputedMaxColumnJob
                          className="flex w-[284px] flex-1"
                          maxWidth={284}
                          leftContent={
                            <Tooltip
                              classNameConfig={{
                                content: 'max-w-[367px]'
                              }}
                              content={
                                <>
                                  <div>{info.row.original?.applicants?.[0]?.job?.title}</div>
                                  <div>{t('common:you_not_in_hiring_team')}</div>
                                </>
                              }>
                              <TypographyText className="max-w-max truncate text-sm text-gray-600">
                                {info.row.original?.applicants?.[0]?.job?.title}
                              </TypographyText>
                            </Tooltip>
                          }
                          rightContent={
                            info.row.original?.applicants?.[0]?.job?.company?.permittedFields?.name?.value && (
                              <Tooltip
                                content={
                                  <>
                                    <div>
                                      {info.row.original?.applicants?.[0]?.job?.company?.permittedFields?.name?.value}
                                    </div>
                                  </>
                                }>
                                <TypographyText className="ml-1 truncate text-sm text-gray-600">
                                  {info.row.original?.applicants?.[0]?.job?.company?.permittedFields?.name?.value}
                                </TypographyText>
                              </Tooltip>
                            )
                          }
                        />
                      )}
                    </>
                  ) : (
                    <TypographyText className="line-clamp-1 text-sm text-gray-900">-</TypographyText>
                  )}
                </div>
              )}
            </div>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['jobs'] || 320
      },
      {
        accessorKey: 'stage',
        header: () => (
          <Tooltip align="start" content={t('candidates:candidateTable:stage')}>
            <Trans i18nKey={'candidates:candidateTable:stage'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => (
          <div className="flex items-center">
            {(info.row.original.applicants || [])?.length > 1 ? (
              <TypographyText className="text-sm text-gray-900">
                <Trans i18nKey="candidates:candidateTable:multipleStage" />
              </TypographyText>
            ) : info.row.original?.applicants?.[0] ? (
              info.row.original?.applicants?.[0]?.status === 'rejected' ? (
                <div className="flex items-center space-x-1.5">
                  <IconWrapper name="Ban" size={16} className="flex-none text-red-500" />
                  <Tooltip content={info.row.original?.applicants[0]?.rejectedReasonLabel}>
                    <TypographyText className="line-clamp-1 text-sm text-gray-900">
                      {info.row.original?.applicants[0]?.rejectedReasonLabel}
                    </TypographyText>
                  </Tooltip>
                </div>
              ) : (
                <Badge
                  type="dotLeading"
                  color={
                    JOB_COLOR_STAGE_NAME(
                      String(
                        (stageTypes || []).filter(
                          (s: IStageType) =>
                            String(s.id) === String(info.row.original?.applicants?.[0]?.jobStage?.stageTypeId)
                        )?.[0]?.colorClassName
                      )
                    ) as IColorBadgeType
                  }
                  dotColor={
                    mappingColorByStageType(
                      (stageTypes || []).filter(
                        (s: IStageType) =>
                          String(s.id) === String(info.row.original?.applicants?.[0]?.jobStage?.stageTypeId)
                      )?.[0]?.colorClassName
                    ) as IDotColorProps
                  }
                  size="md"
                  classNameText="truncate"
                  className="max-w-full">
                  {info.row.original?.applicants[0]?.jobStage?.stageLabel}
                </Badge>
              )
            ) : (
              <TypographyText className="text-sm text-gray-900">-</TypographyText>
            )}
          </div>
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['stage'] || 180
      },
      {
        accessorKey: 'owner',
        header: () => (
          <Tooltip align="start" content={t('candidates:candidateTable:owner')}>
            <Trans i18nKey={'candidates:candidateTable:owner'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const owner = info.row.original.owner
          return (
            <div className="flex">
              {!!owner ? (
                <Tooltip content={owner.fullName}>
                  <Avatar
                    size="sm"
                    src={owner?.avatarVariants?.thumb?.url}
                    color={owner?.defaultColour}
                    alt={owner?.fullName}
                  />
                </Tooltip>
              ) : (
                <TypographyText className="text-sm text-gray-900">-</TypographyText>
              )}
            </div>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['owner'] || 100
      },
      {
        accessorKey: 'summary',
        header: () => (
          <span className="flex items-center text-left text-xs">
            <Tooltip align="start" content={t('candidates:candidateTable:summary')}>
              <Trans i18nKey={'candidates:candidateTable:summary'} />
            </Tooltip>
          </span>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => (
          <div>
            <div className="flex items-center">
              <div className="line-clamp-1">
                <TypographyText className="mr-2 truncate text-sm font-normal text-gray-900">
                  {removeHTMLTags(info?.row?.original?.permittedFields?.summary?.value || '') || '-'}
                </TypographyText>
              </div>
            </div>
          </div>
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['summary'] || 320
      },
      {
        accessorKey: 'skills',
        header: () => (
          <Tooltip align="start" content={t('candidates:candidateTable:skills')}>
            <Trans i18nKey={'candidates:candidateTable:skills'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const skills = info?.row?.original?.permittedFields?.skills?.value || []
          return (
            <>
              {skills?.length > 0 ? (
                <WithComputedMaxItemsChips
                  totalCount={skills?.length}
                  className="relative -mt-2 flex w-[calc(100%-40px)] items-center">
                  {({ maxItems }) => (
                    <SuggestionInlineChips
                      size="sm"
                      source={skills?.map((item, index) => ({
                        label: item,
                        maxLength: 25
                      }))}
                      type="default"
                      maxItems={maxItems}
                    />
                  )}
                </WithComputedMaxItemsChips>
              ) : (
                <TypographyText className="text-sm text-gray-900">-</TypographyText>
              )}
            </>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['skills'] || 320
      },
      {
        accessorKey: 'openToWork',
        header: () => (
          <Tooltip align="start" content={t('candidates:tabs:candidateOverview:profileInformation:openToWork')}>
            <Trans i18nKey={'candidates:tabs:candidateOverview:profileInformation:openToWork'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          return (
            <TypographyText className="line-clamp-1 text-sm font-normal text-gray-900">
              {info?.row?.original?.permittedFields?.openToWork?.value ? 'Yes' : 'No'}
            </TypographyText>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['openToWork'] || 140
      },
      {
        accessorKey: 'languages',
        header: () => (
          <Tooltip align="start" content={t('candidates:tabs:candidateOverview:profileInformation:languages')}>
            <Trans i18nKey={'candidates:tabs:candidateOverview:profileInformation:languages'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const languages = info?.row?.original?.permittedFields?.languages?.value || []
          const languageString = languages.map((lang) => lang.languageDescription).join(',')
          return (
            <div className="line-clamp-1">
              {languageString ? (
                <Tooltip align="start" content={languageString}>
                  <TypographyText className="truncate text-sm font-normal text-gray-900 capitalize">
                    {languageString}
                  </TypographyText>
                </Tooltip>
              ) : (
                <TypographyText className="text-sm font-normal text-gray-900 capitalize">-</TypographyText>
              )}
            </div>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['languages'] || 180
      },
      {
        accessorKey: 'nationality',
        header: () => (
          <Tooltip align="start" content={t('candidates:tabs:candidateOverview:profileInformation:nationality')}>
            <Trans i18nKey={'candidates:tabs:candidateOverview:profileInformation:nationality'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const nationality = info?.row?.original?.permittedFields?.nationality?.value
          return (
            <div className="line-clamp-1">
              {nationality ? (
                <Tooltip align="start" content={nationality}>
                  <TypographyText className="truncate text-sm font-normal text-gray-900">{nationality}</TypographyText>
                </Tooltip>
              ) : (
                <TypographyText className="text-sm font-normal text-gray-900">-</TypographyText>
              )}
            </div>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['nationality'] || 160
      },
      {
        accessorKey: 'birthday',
        header: () => {
          if (tableType === 'candidate') {
            // eslint-disable-next-line react-hooks/rules-of-hooks
            const [openSort, setOpenSort] = useState<boolean>(false)
            const iconHeader =
              sorting?.birthday === DESC_SORTING
                ? 'ArrowDown'
                : sorting?.birthday === ASC_SORTING
                  ? 'ArrowUp'
                  : 'MoreHorizontal'

            return (
              <Popover open={openSort} onOpenChange={(value) => setOpenSort(value)}>
                <PopoverTrigger asChild>
                  <div
                    className={`-mx-3 -my-[9px] flex items-center px-3 py-[9px] hover:cursor-pointer ${
                      openSort ? 'bg-gray-50' : ''
                    }`}>
                    <span className="mr-2 text-left text-xs font-normal text-gray-600">
                      <Trans i18nKey={'candidates:tabs:candidateOverview:profileInformation:birthday'} />
                    </span>
                    <IconWrapper name={openSort ? 'MoreHorizontal' : iconHeader} size={14} className="text-gray-400" />
                  </div>
                </PopoverTrigger>

                <PopoverPortal>
                  <PopoverContent align="end" sideOffset={10} className="w-[158px] p-1">
                    {OPTION_SORT_CREATE_AT_CANDIDATE.map((item, index) => (
                      <div
                        className="hover:bg-gray-50"
                        key={index}
                        onClick={(e) => {
                          if (setSorting) {
                            setSorting({
                              birthday: item.value
                            })
                          }
                        }}>
                        <SelectOption
                          option="radio"
                          icon={true}
                          size="sm"
                          data={{
                            ...item,
                            supportingObj: {
                              name: `${t(`candidates:publicIdOrder:${item.value}`)}`
                            }
                          }}
                          isSelected={sorting?.birthday === item.value}
                          isFocused={sorting?.birthday === item.value}
                          isOption={true}
                          isHeading={false}
                        />
                      </div>
                    ))}
                  </PopoverContent>
                </PopoverPortal>
              </Popover>
            )
          }

          return (
            <span className="line-clamp-1">
              <Trans i18nKey={'candidates:tabs:candidateOverview:profileInformation:birthday'} />
            </span>
          )
        },
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const birthdayFull = info.row.original.permittedFields?.birthday?.value
          const year = birthdayFull?.birth_year
          const month = birthdayFull?.birth_month
          const date = birthdayFull?.birth_date
          return (
            <div className="flex items-center">
              {info.row?.original?.createdAt ? (
                <TypographyText className="text-sm font-normal text-gray-900">
                  {year && typeof birthdayFull === 'object'
                    ? !!year && !month && !date
                      ? year
                      : defaultFormatDate(formatDatePickerToDate({ year, month, date }))
                    : '-'}
                </TypographyText>
              ) : null}
            </div>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['birthday'] || 180
      },
      {
        accessorKey: 'willingToRelocate',
        header: () => (
          <Tooltip align="start" content={t('candidates:tabs:candidateOverview:profileInformation:willingToRelocate')}>
            <Trans i18nKey={'candidates:tabs:candidateOverview:profileInformation:willingToRelocate'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          return (
            <TypographyText className="line-clamp-1 text-sm font-normal text-gray-900">
              {info?.row?.original?.permittedFields?.willingToRelocate?.value ? 'Yes' : 'No'}
            </TypographyText>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['willingToRelocate'] || 140
      },
      {
        accessorKey: 'preferredWorkStates',
        header: () => (
          <Tooltip align="start" content={t('candidates:candidateTable:preferredWorkStates')}>
            <Trans i18nKey={'candidates:candidateTable:preferredWorkStates'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const preferredWorkStates: PreferredWorkStatesType[] =
            info?.row?.original?.permittedFields?.preferredWorkStates?.value || []

          const suggestionChipPreferredWorkStates = preferredWorkStates.map((state) => ({
            label: state.full_name,
            maxLength: 30
          }))

          return (
            <>
              {preferredWorkStates.length > 0 ? (
                <WithComputedMaxItemsChips
                  totalCount={preferredWorkStates.length}
                  className="relative -mt-2 flex w-[calc(100%_-_40px)] items-center">
                  {({ maxItems }) => (
                    <SuggestionInlineChips
                      size="sm"
                      source={suggestionChipPreferredWorkStates}
                      type="default"
                      maxItems={maxItems}
                    />
                  )}
                </WithComputedMaxItemsChips>
              ) : (
                <TypographyText className="text-sm text-gray-900">-</TypographyText>
              )}
            </>
          )
        },

        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['preferredWorkState'] || 320
      },
      {
        accessorKey: 'noticeToPeriodDays',
        header: () => (
          <Tooltip align="start" content={t('candidates:tabs:candidateOverview:profileInformation:noticePeriod')}>
            <Trans i18nKey={'candidates:tabs:candidateOverview:profileInformation:noticePeriod'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          return (
            <TypographyText className="line-clamp-1 text-sm">
              {info?.row?.original?.permittedFields?.noticeToPeriodDays?.value || '-'}
            </TypographyText>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['noticeToPeriodDays'] || 140
      },
      {
        accessorKey: 'currentSalary',
        header: () => (
          <Tooltip align="start" content={t('candidates:tabs:candidateOverview:profileInformation:currentSalary')}>
            <Trans i18nKey={'candidates:tabs:candidateOverview:profileInformation:currentSalary'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const currentSalary = info?.row?.original?.permittedFields?.currentSalary?.value
          const currentSalaryCurrency =
            info?.row?.original?.permittedFields?.currentSalaryCurrency?.value || user?.currentTenant?.currency

          const salaryDisplay = Number(currentSalary)
            ? `${numberWithCommas(Number(currentSalary))} ${currentSalaryCurrency}`
            : '-'
          return (
            <TypographyText className="line-clamp-1 text-sm font-normal text-gray-900">{salaryDisplay}</TypographyText>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['currentSalary'] || 160
      },
      {
        accessorKey: 'expectedSalary',
        header: () => (
          <Tooltip align="start" content={t('candidates:tabs:candidateOverview:profileInformation:expectedSalary')}>
            <Trans i18nKey={'candidates:tabs:candidateOverview:profileInformation:expectedSalary'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const expectedSalary = info?.row?.original?.permittedFields?.expectedSalary?.value
          const expectedSalaryCurrency =
            info?.row?.original?.permittedFields?.expectedSalaryCurrency?.value || user?.currentTenant?.currency
          const salaryDisplay = Number(expectedSalary)
            ? `${numberWithCommas(Number(expectedSalary))} ${expectedSalaryCurrency}`
            : '-'
          return (
            <TypographyText className="line-clamp-1 text-sm font-normal text-gray-900">{salaryDisplay}</TypographyText>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['expectedSalary'] || 160
      },
      {
        accessorKey: 'profileLevel',
        header: () => (
          <Tooltip align="start" content={t('candidates:tabs:candidateOverview:profileInformation:experienceLevel')}>
            <Trans i18nKey={'candidates:tabs:candidateOverview:profileInformation:experienceLevel'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const level = profileLevel.find(
            (level: { value: string; supportingObj: { name: string } }) =>
              level.value == info?.row?.original?.permittedFields?.profileLevel?.value
          )
          return (
            <TypographyText className="line-clamp-1 text-sm font-normal text-gray-900">
              {level?.supportingObj?.name || '-'}
            </TypographyText>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['profileLevel'] || 160
      },
      {
        accessorKey: 'totalYearsOfExp',
        header: () => (
          <Tooltip align="start" content={t('candidates:candidateTable:yearsOfExperience')}>
            <Trans i18nKey={'candidates:candidateTable:yearsOfExperience'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const experience = info?.row?.original?.permittedFields?.totalYearsOfExp?.value

          return (
            <TypographyText className="line-clamp-1 text-sm font-normal text-gray-900">
              {experience !== null
                ? transformYearsOfExperience(Number(info?.row?.original?.permittedFields?.totalYearsOfExp?.value), t)
                : '-'}
            </TypographyText>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['totalYearsOfExp'] || 160
      },
      {
        accessorKey: 'educations',
        header: () => (
          <Tooltip align="start" content={t('candidates:candidateTable:educations')}>
            <Trans i18nKey={'candidates:candidateTable:educations'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const values = ((info?.row?.original?.permittedFields?.educations?.value as unknown as string[]) || []).map(
            (item: string, index: number) => ({
              id: index,
              schoolName: item,
              profileId: index
            })
          )

          return (
            <>
              {values?.length > 0 ? (
                <WithComputedMaxItemsChips
                  totalCount={values?.length}
                  className="relative -mt-2 flex w-[calc(100%-40px)] items-center">
                  {({ maxItems }) => (
                    <SuggestionInlineChips
                      size="sm"
                      source={values?.map((item) => ({
                        label: item?.schoolName,
                        maxLength: 25
                      }))}
                      type="default"
                      maxItems={maxItems}
                    />
                  )}
                </WithComputedMaxItemsChips>
              ) : (
                <TypographyText className="text-sm text-gray-900">-</TypographyText>
              )}
            </>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['educations'] || 320
      },
      {
        accessorKey: 'workExperiences',
        header: () => (
          <Tooltip align="start" content={t('candidates:candidateTable:workExperiences')}>
            <Trans i18nKey={'candidates:candidateTable:workExperiences'} />
          </Tooltip>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const values = (
            (info?.row?.original?.permittedFields?.workExperiences?.value as unknown as string[]) || []
          ).map((item: string, index: number) => ({
            id: index,
            company: item,
            profileId: index
          }))

          return (
            <>
              {values?.length > 0 ? (
                <WithComputedMaxItemsChips
                  totalCount={values?.length}
                  className="relative -mt-2 flex w-[calc(100%-40px)] items-center">
                  {({ maxItems }) => (
                    <SuggestionInlineChips
                      size="sm"
                      source={values?.map((item) => ({
                        label: item?.company,
                        maxLength: 25
                      }))}
                      type="default"
                      maxItems={maxItems}
                    />
                  )}
                </WithComputedMaxItemsChips>
              ) : (
                <TypographyText className="text-sm text-gray-900">-</TypographyText>
              )}
            </>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: columnSize?.['workExperiences'] || 320
      },
      {
        accessorKey: 'createdAt',
        header: () => {
          // eslint-disable-next-line react-hooks/rules-of-hooks
          const [openSort, setOpenSort] = useState<boolean>(false)
          const iconHeader =
            sorting?.createdAt === DESC_SORTING
              ? 'ArrowDown'
              : sorting?.createdAt === ASC_SORTING
                ? 'ArrowUp'
                : 'MoreHorizontal'
          if (tableType === 'recommendation')
            return (
              <div
                className={`-mx-3 -my-[9px] flex items-center px-3 py-[9px] hover:cursor-pointer ${
                  openSort ? 'bg-gray-50' : ''
                }`}>
                <span className="mr-2 text-left text-xs font-normal text-gray-600">
                  <Trans i18nKey={'candidates:candidateTable:createAt'} />
                </span>
              </div>
            )
          return (
            <Tooltip align="start" content={t('candidates:candidateTable:createAt')}>
              <Popover open={openSort} onOpenChange={(value) => setOpenSort(value)}>
                <PopoverTrigger asChild>
                  <div
                    className={`-mx-3 -my-[9px] flex items-center px-3 py-[9px] hover:cursor-pointer ${
                      openSort ? 'bg-gray-50' : ''
                    }`}>
                    <span className="mr-2 text-left text-xs font-normal text-gray-600">
                      <Trans i18nKey={'candidates:candidateTable:createAt'} />
                    </span>
                    <IconWrapper name={openSort ? 'MoreHorizontal' : iconHeader} size={14} className="text-gray-400" />
                  </div>
                </PopoverTrigger>

                <PopoverPortal>
                  <PopoverContent align="start" sideOffset={10} className="w-[158px] p-1">
                    {OPTION_SORT_CREATE_AT_CANDIDATE.map((item, index) => (
                      <div
                        className="hover:bg-gray-50"
                        key={index}
                        onClick={(e) => {
                          if (setSorting) {
                            setSorting({
                              createdAt: item.value
                            })
                          }
                        }}>
                        <SelectOption
                          option="radio"
                          icon={true}
                          size="sm"
                          data={{
                            ...item,
                            supportingObj: {
                              name: `${t(`candidates:lastActivity:${item.value}`)}`
                            }
                          }}
                          isSelected={sorting?.createdAt === item.value}
                          isFocused={sorting?.createdAt === item.value}
                          isOption={true}
                          isHeading={false}
                        />
                      </div>
                    ))}
                  </PopoverContent>
                </PopoverPortal>
              </Popover>
            </Tooltip>
          )
        },
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => (
          <div className="flex items-center">
            {info.row?.original?.createdAt ? (
              <TypographyText className="text-sm text-gray-900">
                {defaultFormatDate(new Date(info.row.original.createdAt))}
              </TypographyText>
            ) : null}
          </div>
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size:
          Object.values(visibleColumns || [])?.filter((item) => !!item)?.length > 4
            ? 180
            : columnSize?.['createdAt'] || 350
      },
      {
        accessorKey: 'lastActivity',
        header: () => {
          // eslint-disable-next-line react-hooks/rules-of-hooks
          const [openSort, setOpenSort] = useState<boolean>(false)
          const iconHeader =
            sorting?.lastActivity === DESC_SORTING
              ? 'ArrowDown'
              : sorting?.lastActivity === ASC_SORTING
                ? 'ArrowUp'
                : 'MoreHorizontal'
          if (tableType === 'recommendation')
            return (
              <div
                className={`-mx-3 -my-[9px] line-clamp-1 items-center px-3 py-[9px] hover:cursor-pointer ${
                  openSort ? 'bg-gray-50' : ''
                }`}>
                <TypographyText className="mr-2 truncate text-left text-xs font-normal text-gray-600">
                  <Trans i18nKey={'candidates:candidateTable:lastActivity'} />
                </TypographyText>
              </div>
            )
          return (
            <Tooltip align="start" content={t('candidates:candidateTable:lastActivity')}>
              <Popover open={openSort} onOpenChange={(value) => setOpenSort(value)}>
                <PopoverTrigger asChild>
                  <div
                    className={`-mx-3 -my-[9px] flex items-center px-3 py-[9px] hover:cursor-pointer ${
                      openSort ? 'bg-gray-50' : ''
                    }`}>
                    <TypographyText className="mr-2 truncate text-left text-xs font-normal text-gray-600">
                      <Trans i18nKey={'candidates:candidateTable:lastActivity'} />
                    </TypographyText>

                    <IconWrapper name={openSort ? 'MoreHorizontal' : iconHeader} size={14} className="text-gray-400" />
                  </div>
                </PopoverTrigger>

                <PopoverPortal>
                  <PopoverContent align="start" sideOffset={10} className="w-[158px] p-1">
                    {OPTION_SORT_CREATE_AT_CANDIDATE.map((item, index) => (
                      <div
                        className="hover:bg-gray-50"
                        key={index}
                        onClick={(e) => {
                          if (setSorting) {
                            setSorting({
                              lastActivity: item.value
                            })
                          }
                        }}>
                        <SelectOption
                          option="radio"
                          icon={true}
                          size="sm"
                          data={{
                            ...item,
                            supportingObj: {
                              name: `${t(`candidates:lastActivity:${item.value}`)}`
                            }
                          }}
                          isSelected={sorting?.lastActivity === item.value}
                          isFocused={sorting?.lastActivity === item.value}
                          isOption={true}
                          isHeading={false}
                        />
                      </div>
                    ))}
                  </PopoverContent>
                </PopoverPortal>
              </Popover>
            </Tooltip>
          )
        },
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => (
          <div className="flex items-center">
            {info.row?.original?.updatedAt ? (
              <TypographyText className="text-sm text-gray-900">
                {defaultFormatDate(new Date(info.row.original.updatedAt))}
              </TypographyText>
            ) : null}
          </div>
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size:
          Object.values(visibleColumns || [])?.filter((item) => !!item).length > 4
            ? 180
            : columnSize?.['lastActivity'] || 350
      },
      ...filterConfigUserDisplay.map((mapping) => ({
        accessorKey: mapping.accessorKey,
        header: () => (
          <div className="line-clamp-1">
            <Tooltip align="start" content={mapping.label}>
              <TypographyText className="truncate text-left text-xs">{mapping.label}</TypographyText>
            </Tooltip>
          </div>
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const fieldKind = String(mapping.fieldKind)
          const findCustomField = (info.row.original.customFields || [])?.find(
            (item) => String(item.customSettingId) === String(mapping.customSettingId)
          )
          const mergeViewData = [...(mappingsFilterProfileField || [])]
          const selectOptions = mergeViewData?.find(
            (item) => String(item.id) === String(mapping.customSettingId)
          )?.selectOptions
          const formatDate = findCustomField?.value as {
            year?: number
            month?: number
            date?: number
          }
          let fieldValue: {
            value: string | number | Array<string | undefined> | undefined
            showTooltip?: boolean
            className?: string
          } = { value: '' }

          if (['string', 'number'].includes(fieldKind)) {
            fieldValue = {
              value: findCustomField?.value,
              showTooltip: fieldKind === 'string'
            }
          } else if (fieldKind === 'array') {
            fieldValue = {
              value: (selectOptions || []).find(
                (item) => String(item.value) === String(findCustomField?.selectedOptionKeys?.[0])
              )?.supportingObj?.name,
              showTooltip: true
            }
          } else if (fieldKind === 'multiple') {
            fieldValue = {
              value: (selectOptions || [])
                .filter((item) => findCustomField?.selectedOptionKeys?.includes(item.value))
                .map((val) => val.supportingObj?.name),
              showTooltip: true
            }
          } else if (fieldKind === 'boolean') {
            fieldValue = {
              value: !!findCustomField?.value ? 'Yes' : 'No'
            }
          } else if (fieldKind === 'date') {
            fieldValue = {
              value:
                formatDate?.year && typeof formatDate === 'object'
                  ? !!formatDate?.year && !formatDate.month && !formatDate.date
                    ? formatDate.year
                    : defaultFormatDate(formatDatePickerToDate(formatDate))
                  : '-'
            }
          }

          if (fieldKind === 'multiple' && Array.isArray(fieldValue.value)) {
            return fieldValue?.value?.length > 0 ? (
              <WithComputedMaxItemsChips
                totalCount={fieldValue.value?.length}
                className="relative -mt-2 flex w-[calc(100%-40px)] items-center">
                {({ maxItems }) => (
                  <SuggestionInlineChips
                    size="sm"
                    source={
                      Array.isArray(fieldValue.value)
                        ? fieldValue?.value?.map((item: any, index: number) => ({
                            label: item,
                            maxLength: 25
                          }))
                        : []
                    }
                    type="default"
                    maxItems={maxItems}
                  />
                )}
              </WithComputedMaxItemsChips>
            ) : (
              <TypographyText className="text-sm text-gray-900">-</TypographyText>
            )
          }
          return (
            <div className="flex">
              {fieldKind === 'text' ? (
                <TypographyText className="mr-2 truncate text-sm font-normal text-gray-900">
                  {removeHTMLTags(findCustomField?.value || '') || '-'}
                </TypographyText>
              ) : (
                <Tooltip
                  position="bottom"
                  content={fieldValue?.value}
                  classNameConfig={{
                    content: fieldValue?.value && fieldValue.showTooltip ? '' : 'hidden'
                  }}>
                  <TypographyText className="line-clamp-1 text-sm text-gray-900">
                    {fieldValue?.value || '-'}
                  </TypographyText>
                </Tooltip>
              )}
            </div>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: getCustomFieldSize(String(mapping.fieldKind))
      }))
    ]

    return defaultColumns
  }, [data, configUserDisplay, columnSize, filterConfigUserDisplay, bulkValues])

  useEffect(() => {
    if (configUserDisplay?.length || tableType === 'recommendation') {
      setShowTable(true)
    }
  }, [configUserDisplay])

  useEffect(() => {
    setTimeout(() => {
      if (showTable === false) {
        setShowTable(true)
      }
    }, 5000)
  }, [showTable])

  useEffect(() => {
    prepareDataForSwitchLayout()
  }, [data])

  const prepareDataForSwitchLayout = () => {
    const mappingRedirectUrls = [] as Array<string>
    const collections = data?.data || []

    collections.forEach((s) => {
      const item = s as { id: string }
      if (item?.id) {
        mappingRedirectUrls.push(configuration.path.candidates.detail(item.id))
      }
    })

    if (actions?.setConfigSwitchLayout && actions?.configSwitchLayout?.path) {
      actions?.setConfigSwitchLayout({
        path: actions?.configSwitchLayout?.path,
        redirectUrls: mappingRedirectUrls
      })
    }
  }

  const LAST_KEYS = ['createdAt', 'lastActivity']
  const columnOrder = configUserDisplay
    ?.slice()
    .sort((a, b) => {
      return (LAST_KEYS.includes(a.accessorKey) ? 1 : 0) - (LAST_KEYS.includes(b.accessorKey) ? 1 : 0)
    })
    .map((item) => item.accessorKey)

  const visibleTableColumns: {
    [key: string]: boolean
  } = {
    ...((configUserDisplay
      ? visibleColumns
      : columns.map((col) => ({
          [col.accessorKey]: true
        }))) as VisibilityState),
    rowSelection: enableRowSelection
  }

  const stickyConfig = () => {
    if (enableRowSelection) {
      if (visibleTableColumns?.publicId) {
        return [
          { index: 0, position: 'left', value: 0, useShadow: true },
          { index: 1, position: 'left', value: 28, useShadow: true },
          { index: 2, position: 'left', value: 128, useShadow: true }
        ]
      }

      return [
        { index: 0, position: 'left', value: 0, useShadow: true },
        { index: 1, position: 'left', value: 28, useShadow: true }
      ]
    }

    if (visibleTableColumns?.publicId) {
      return [
        { index: 0, position: 'left', value: 0, useShadow: true },
        { index: 1, position: 'left', value: 100, useShadow: true }
      ]
    }

    return [{ index: 0, position: 'left', value: 0, useShadow: true }]
  }

  return showTable ? (
    <TablePagination
      tableRef={tableRef}
      search={{
        globalFilter: [
          ...(!!filter?.search ? [filter?.search] : []),
          ...(filter?.job_id?.value ? [filter?.job_id?.value] : []),
          ...(filter?.stage_type_id?.value ? [filter?.stage_type_id?.value] : []),
          ...(filter?.country_state_id?.value ? [filter?.country_state_id?.value] : []),
          ...((filter?.tagged_ids || [])?.length > 0 ? (filter?.tagged_ids || []).map((tag) => tag.value) : []),
          ...((filter?.owner_id || [])?.length > 0 ? (filter?.owner_id || []).map((owner) => owner.value) : []),
          ...((filter?.applicant_statuses || [])?.length > 0
            ? (filter?.applicant_statuses || []).map((applicantStatus) => applicantStatus.value)
            : []),
          ...((filter?.profile_talent_pool_ids || [])?.length > 0
            ? (filter?.profile_talent_pool_ids || []).map((item) => item.value)
            : []),
          ...(filter?.fieldsFilter?.length ? (filter?.fieldsFilter || []).map((item) => item) : [])
        ].join(' '),
        filter
      }}
      textOverride={{
        of: `${t('label:of')}`,
        page: `${t('label:page')}`,
        placeholder: `${t('label:placeholder:select')}`,
        search: `${t('label:placeholder:search')}`,
        loading: `${t('label:loading')}`,
        noOptions: `${t('label:noOptions')}`,
        rowsPerPage: `${t('label:rowsPerPage')}`
      }}
      emptyConfig={{
        classNameEmpty: cn('flex h-full items-center justify-center', calcHeightScroll, classNameEmpty),
        title: `${t('candidates:candidateTable:emptyData:title')}`,
        description: `${t('candidates:candidateTable:emptyData:description')}`,
        buttonTitle: action?.create ? `${t('candidates:candidateTable:emptyData:buttonAddCandidate')}` : '',
        buttonTitleOnClick: () => setOpenCreateCandidate(true),
        titleSearch: `${t('candidates:candidateTable:emptySearch:title')}`,
        descriptionSearch: `${t('candidates:candidateTable:emptySearch:description')}`,
        buttonTitleSearch: `${t('candidates:candidateTable:emptySearch:buttonClear')}`,
        buttonTitleSearchOnClick: () => clearFilter(),
        ...(emptyConfig || {})
      }}
      tableConfig={{
        defaultPageSize: configuration.defaultPageSize,
        showRowsPerPage: true,
        paginationInsideScroll: true
      }}
      dataQuery={{
        isFetching,
        fetcher,
        data
      }}
      columnOrder={columnOrder?.length ? ['rowSelection', ...columnOrder] : columnOrder}
      columnVisibility={visibleTableColumns}
      columns={columns.filter((col) =>
        configUserDisplay
          ? !hiddenColumns.includes(col.accessorKey) && visibleColumns[col.accessorKey] === true
          : !hiddenColumns.includes(col.accessorKey)
      )}
      isHeaderSticky
      stickyConfig={stickyConfig()}
      classNameTable={cn(
        'max-w-full border-t border-solid border-t-gray-100 bg-white px-px',
        classNameTable,
        calcHeightScroll
      )}
    />
  ) : null
}

export default CandidateListingTable
