import { Trans, useTranslation } from 'react-i18next'

import type { IRouterWithID } from '~/core/@types/global'
import { openAlert } from '~/core/ui/AlertDialog'
import { IconButton } from '~/core/ui/IconButton'
import { TypographyText } from '~/core/ui/Text'
import { monthYearFormatDate } from '~/core/utilities/format-date'

import type { CertificatesType } from '~/lib/features/candidates/types'
import {
  checkCertificateFieldFormatDate,
  getCertificateFieldFormatDate
} from '~/lib/features/candidates/utilities/format'

import DisableControlByPermission from '../../DisableControlByPermission'

export const checkFieldFormatDate = ({
  attributes
}: {
  type?: string
  attributes?: CertificatesType & {
    from?: {
      month?: string
      year?: string
    }
  }
}) => {
  if (!attributes || Object.keys(attributes).length === 0) return false

  return attributes?.issueMonth || attributes?.issueYear ? true : false
}

export const getFieldFormatDate = ({ attributes = {} as any, locale }: { attributes?: any; locale?: string }) => {
  if (Object.keys(attributes).length === 0) return false
  const formatDate = `${attributes.issueYear}-${attributes.issueMonth >= 10 ? attributes.issueMonth : `0${attributes.issueMonth}`}`
  if (attributes?.issueMonth) {
    if (attributes?.issueYear) {
      return `${monthYearFormatDate(new Date(formatDate), locale)} `
    }

    return attributes.issueMonth
  } else {
    return attributes.issueYear
  }
}

const CertificatesView = ({
  source = [],
  onClick,
  onDelete,
  isLoadingUpdateProfile = false,
  applicantId
}: {
  source?: Array<CertificatesType>
  onClick?: (item: CertificatesType, index: number) => void
  onDelete?: (item: CertificatesType, index: number) => void
  isLoadingUpdateProfile?: boolean
  applicantId?: IRouterWithID
}) => {
  const { t, i18n } = useTranslation()

  return (
    <div className="space-y-1.5">
      {source.map((item, index) => (
        <div className="relative flex flex-col" key={`item-${index}`}>
          <div className="group flex justify-between">
            <div>
              <div className="flex items-center">
                <span className="mr-2.5 h-1 w-1 rounded-xs bg-gray-600" />
                <TypographyText className="text-sm font-medium text-gray-900">
                  {item.certificateName || `${t('label:undefined')}`}
                  {item.certificateName && item.institution ? (
                    <span className="mx-2 my-1 inline-block h-0.5 min-h-[2px] w-0.5 min-w-[2px] rounded-xs bg-gray-400" />
                  ) : null}
                  {item.institution ? <>{item.institution}</> : null}
                </TypographyText>
              </div>

              {checkCertificateFieldFormatDate({ attributes: item }) && (
                <div className="mt-2-px ml-[14px] text-sm text-gray-600">
                  {getCertificateFieldFormatDate({
                    attributes: item,
                    locale: i18n.language
                  })}
                </div>
              )}
            </div>
            <DisableControlByPermission applicantId={applicantId}>
              <div className="shadow-actions absolute top-0 right-0 hidden items-center space-x-1 rounded-xs border border-solid border-gray-100 bg-white p-0.5 group-hover:flex">
                <IconButton
                  size="xs"
                  isDisabled={isLoadingUpdateProfile}
                  type="secondary"
                  iconMenus="Edit3"
                  className="border-none"
                  onClick={() => onClick && onClick(item, index)}
                />
                <IconButton
                  size="xs"
                  isLoading={isLoadingUpdateProfile}
                  isDisabled={isLoadingUpdateProfile}
                  type="secondary-destructive"
                  iconMenus="Trash2"
                  className="border-none"
                  onClick={() => {
                    openAlert({
                      isPreventAutoFocusDialog: false,
                      className: 'w-[480px]',
                      title: `${t('candidates:tabs:candidateOverview:certificates:deleteTitle')}`,
                      description: (
                        <Trans
                          i18nKey="candidates:tabs:candidateOverview:certificates:deleteDescription"
                          values={{
                            title: item.certificateName
                          }}>
                          <span className="font-medium text-gray-900" />
                        </Trans>
                      ),
                      actions: [
                        {
                          label: `${t('button:cancel')}`,
                          type: 'secondary',
                          size: 'sm'
                        },
                        {
                          isCallAPI: true,
                          label: `${t('button:remove')}`,
                          type: 'destructive',
                          size: 'sm',
                          onClick: async () => {
                            if (onDelete) {
                              await onDelete(item, index)
                            }
                          }
                        }
                      ]
                    })
                  }}
                />
              </div>
            </DisableControlByPermission>
          </div>
        </div>
      ))}
    </div>
  )
}

export default CertificatesView
