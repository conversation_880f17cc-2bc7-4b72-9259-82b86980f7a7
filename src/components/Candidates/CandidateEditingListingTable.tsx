'use client'

import type { VisibilityState } from '@tanstack/react-table'
import Link from 'next/link'
import type { Dispatch, FC, SetStateAction } from 'react'
import { useCallback, useEffect, useMemo, useRef } from 'react'
import { Trans, useTranslation } from 'react-i18next'

import useEnumsData from 'src/hooks/data/use-enums-data'
import useStaticData from 'src/hooks/data/use-static-data'
import configuration from '~/configuration'
import { AGENCY_TENANT } from '~/core/constants/enum'
import { openAlert } from '~/core/ui/AlertDialog'
import { Avatar } from '~/core/ui/Avatar'
import type { IColorBadgeType } from '~/core/ui/Badge'
import { Badge } from '~/core/ui/Badge'
import { Checkbox } from '~/core/ui/Checkbox'
import type { IDotColorProps } from '~/core/ui/Dot'
import { Dot } from '~/core/ui/Dot'
import IconWrapper from '~/core/ui/IconWrapper'
import { Popover, PopoverContent, PopoverPortal, PopoverTrigger } from '~/core/ui/Popover'
import type { ISelectOption } from '~/core/ui/Select'
import { formatDatePickerToDate } from '~/core/ui/SingleDateWithYearOnlyPicker'
import { SuggestionInlineChips } from '~/core/ui/SuggestionChips'
import type { IPageEditingPagination, IPageEditingPaginationFetcher } from '~/core/ui/TableEditingPagination'
import { TableEditingPagination } from '~/core/ui/TableEditingPagination'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn } from '~/core/ui/utils'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { convertValueToHTMLFromSearch, removeHTMLTags } from '~/core/utilities/common'
import { defaultFormatDate } from '~/core/utilities/format-date'

import MutationAgencyProfileUpdate from '~/lib/features/candidates/graphql/mutation-agency-update-profile'
import MutationProfileUpdate from '~/lib/features/candidates/graphql/mutation-update-profile'
import useCandidateProfile from '~/lib/features/candidates/hooks/use-query-candidate'
import useTagsProfile from '~/lib/features/candidates/hooks/use-query-tags'
import {
  schemaCandidateProfileNoticeToPeriodDays,
  schemaCandidateProfileNumberDynamic,
  schemaCandidateProfilePhoneNumber,
  schemaCandidateProfilePreferredWorkStateIds,
  schemaCandidateProfileRichEditorDynamic,
  schemaCandidateProfileSkills,
  schemaCandidateProfileTalentPools,
  schemaCandidateProfileTextDynamic,
  schemaUpdateProfile
} from '~/lib/features/candidates/schema/validation-update-profile'
import type {
  CandidateProfileInputType,
  ICandidateProfile,
  ICandidatesFilter,
  IProfileListType,
  LanguagesType,
  PreferredWorkStatesType,
  TalentPoolType
} from '~/lib/features/candidates/types'
import { useConvertSalary } from '~/lib/features/candidates/utilities'
import { ListSuggestNoticeOfPeriod, totalYoeOptions } from '~/lib/features/candidates/utilities/enum'
import { transformYearsOfExperience } from '~/lib/features/candidates/utilities/index'
import { mappingDataUpdateProfile, mappingFormatFieldUpdateProfile } from '~/lib/features/candidates/utilities/mapping'
import type { IStageType } from '~/lib/features/jobs/types'
import { JOB_COLOR_STAGE_NAME, JOB_DOT_STATUS } from '~/lib/features/jobs/utilities/enum'
import usePermissionCandidate from '~/lib/features/permissions/hooks/use-permission-candidate'
import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'
import { mappingColorByStageType } from '~/lib/features/settings/hiring-pipelines/utilities/common'
import usePromiseOwnerOptionsFetchAll from '~/lib/features/settings/members/hooks/use-promise-owner-fetch-all'
import { formatInitialValueCustomField } from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'
import type { CustomFieldViewType } from '~/lib/features/settings/profile-fields/types/custom-field'
import useSkillSettingsStore from '~/lib/features/settings/skills/store'
import type { ITags } from '~/lib/features/settings/tags/types'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import type { FieldSettingType } from '../DisplayConfig'
import EditingInlineTableBody, {
  ENUMS_EDITING_INLINE_MODE,
  ENUMS_EDITING_INLINE_MODE_COMPONENT
} from '../EditingInlineTable/Body'
import EditingInlineTableHeader from '../EditingInlineTable/Header'
import type { ISwitchLayoutView } from '../SwitchLayout/SwitchLayoutView'
import WithComputedMaxItemsChips from '../WithComputedMaxItemsChips'
import type { ModalMatchedRankType } from './Profile/components/Recommendation/MatchedRankDetail'
import SocialLinksWithAction from './SocialLinksWithAction'
import WithComputedMaxColumnJob from './WithComputedMaxColumnJob'

const CandidateEditingListingTable: FC<{
  tableRef: (tableEditor: any) => void
  calcHeightScroll: string | number
  hiddenColumns?: string[]
  setOpenCreateCandidate: (value: boolean) => void
  data?: IPageEditingPagination
  fetcher?: IPageEditingPaginationFetcher
  isFetching: boolean
  filter?: ICandidatesFilter | undefined
  clearFilter: () => void
  sorting?: {
    [key: string]: string | null
  }
  setSorting?: (params: { [key: string]: string | null }) => void
  configUserDisplay?: FieldSettingType[]
  classNameTable?: string
  classNameEmpty?: string
  actions?: {
    configSwitchLayout?: {
      path: Array<string>
      redirectUrls: Array<string>
    }
    setConfigSwitchLayout?: (param: { path: Array<string>; redirectUrls: Array<string> }) => void
    switchView?: ISwitchLayoutView
    setSwitchView?: (param: ISwitchLayoutView) => void
    setModalMatchedRank?: Dispatch<SetStateAction<ModalMatchedRankType>>
  }
  enableRowSelection?: boolean

  mappingsFilterProfileField?: CustomFieldViewType[]
}> = ({
  tableRef: callbackTableRef,
  calcHeightScroll,
  hiddenColumns = [],
  setOpenCreateCandidate,
  data,
  fetcher,
  isFetching,
  filter,
  clearFilter,
  sorting,
  setSorting,
  configUserDisplay,
  classNameTable = '',
  classNameEmpty = '',
  actions,
  enableRowSelection = false,
  mappingsFilterProfileField
}) => {
  const { t, i18n } = useTranslation()
  const { setBulkValues, setBulkSelectedAll, bulkSelectedAll, bulkValues, resetBulkValues, user } = useBoundStore()
  const { setToast } = useToastStore()
  const { numberWithCommas } = useConvertSalary()
  const { actionProfile } = usePermissionCandidate({})
  const {
    promiseCountryStateOptions,
    languageList,
    promiseTalentPoolOptions,
    promiseSkillsOptions,
    promiseTagsOptions,
    currencySalary
  } = useCandidateProfile({})
  const promiseMembersOwnerOptionsFetchAll = usePromiseOwnerOptionsFetchAll()
  const { actionTag } = usePermissionSetting()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })
  const { onAddTags, onDeleteTags, onCreateTags, validationTag } = useTagsProfile({
    setToast
  })
  const { allowAddNewSkill } = useSkillSettingsStore()
  const { trigger: triggerUpdateProfile } = useSubmitCommon(
    isCompanyKind ? MutationAgencyProfileUpdate : MutationProfileUpdate
  )

  const stageTypes = useStaticData({
    keyName: 'agency_stageTypes',
    locale: i18n.language
  })
  const profileLevel = useEnumsData({
    enumType: 'ProfileProfileLevel',
    locale: i18n.language
  })

  const tableRef = useRef<any>(null)

  useEffect(() => {
    if (tableRef.current && isFetching) {
      tableRef.current.toggleAllRowsSelected(false)
    }
  }, [isFetching])

  // API UPDATE PROFILE
  const onUpdateProfile = useCallback(
    async (
      param: CandidateProfileInputType & {
        paramType: string
        row: { original: IProfileListType; index: number }
      }
    ) => {
      setTimeout(() => {
        if (tableRef.current && !['links', 'phoneNumber', 'email'].includes(param.paramType)) {
          const { fieldUpdate, newValue } = mappingFormatFieldUpdateProfile({
            currentOriginalValue: param.row.original,
            currentField: param.paramType,
            value: param[param.paramType as keyof unknown]
          })

          if (tableRef.current.options?.meta?.updateData) {
            tableRef.current.options?.meta?.updateData(param.row.index, fieldUpdate, newValue)
          }
        }

        triggerUpdateProfile(
          mappingDataUpdateProfile({
            ...param,
            id: Number(param.id),
            profileType: 'candidateEditingTable'
          })
        ).then((result) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              setToast
            })

            return
          }

          const { profilesUpdate } = result.data
          if (profilesUpdate.profile) {
            if (tableRef.current && ['links', 'phoneNumber', 'email'].includes(param.paramType)) {
              const { fieldUpdate, newValue } = mappingFormatFieldUpdateProfile({
                currentOriginalValue: param.row.original,
                currentField: param.paramType,
                value: profilesUpdate.profile?.permittedFields?.[param.paramType]?.value
              })

              if (tableRef.current.options?.meta?.updateData) {
                tableRef.current.options?.meta?.updateData(param.row.index, fieldUpdate, newValue)
              }
            }
          }
        })
      }, 0)
    },
    [triggerUpdateProfile, setToast]
  )
  // END API UPDATE PROFILE

  const visibleColumns = useMemo(() => {
    const obj: VisibilityState = {}
    configUserDisplay &&
      configUserDisplay.map((field: FieldSettingType) => {
        if (!field?.filter) {
          obj[field?.accessorKey] = !!field?.visibleValue
        }
      })

    if (enableRowSelection) {
      obj.rowSelection = true
    }

    return obj
  }, [configUserDisplay, enableRowSelection])

  const filterConfigUserDisplay = useMemo(() => {
    let results: FieldSettingType[] = []
    if (configUserDisplay) {
      results = configUserDisplay.filter((item) => !item.isDefaultField)
    }

    return results
  }, [configUserDisplay])

  const columns = useMemo(() => {
    const searchValue = filter?.search
    const getCustomFieldSize = (fieldKind: string) => {
      if (['string', 'array', 'multiple'].includes(fieldKind)) {
        return configuration.tableColumnSize.mapping.text
      } else if (['boolean'].includes(fieldKind)) {
        return configuration.tableColumnSize.mapping.boolean
      } else if (fieldKind === 'number') {
        return configuration.tableColumnSize.mapping.qualitativeNumber
      } else if (fieldKind === 'date') {
        return configuration.tableColumnSize.mapping.text
      } else return configuration.tableColumnSize.mapping.paragraph
    }

    const defaultColumns = [
      {
        accessorKey: 'rowSelection',
        header: () => (
          <div className="flex items-center py-[7px]">
            <Checkbox
              isChecked={bulkSelectedAll}
              onCheckedChange={(e) => {
                const { checked } = e.target

                setBulkSelectedAll(checked)
                if (tableRef.current) {
                  tableRef.current.toggleAllRowsSelected(checked)
                }

                if (checked) {
                  let ids: string[] = []
                  data?.data?.map((candidate: ICandidateProfile) => {
                    ids = [...ids, String(candidate.id)]
                  })
                  setBulkValues(ids)
                } else resetBulkValues()
              }}
              size="sm"
              className="mt-0 mr-1.5 flex items-center pl-px"
            />
          </div>
        ),
        cell: (info: {
          row: {
            original: IProfileListType
            index: number
            toggleSelected: (checked: boolean) => void
          }
          getValue: Function
        }) => {
          // eslint-disable-next-line react-hooks/rules-of-hooks
          useEffect(() => {
            const filterActiveRecord = bulkValues?.filter((item) => String(item) === String(info.row.original?.id))
            if (filterActiveRecord?.length) {
              info.row.toggleSelected(true)
            }
          }, [])

          return (
            <>
              <div className="checkedSystemId flex h-8 items-center pl-px">
                <Checkbox
                  isChecked={bulkValues?.includes(info.row.original.id?.toString() || '')}
                  onCheckedChange={(e) => {
                    const { checked } = e.target
                    const id = info?.row?.original?.id?.toString() || ''
                    let newList = [...(bulkValues || [])]

                    info.row.toggleSelected(checked)

                    if (checked) {
                      newList = [...newList, id]
                    } else {
                      newList = newList.filter((i) => i !== id)
                    }
                    setBulkValues(newList)
                    setBulkSelectedAll(false)
                  }}
                  size="sm"
                  className="mt-0 mr-1.5 flex items-center"
                />
              </div>
            </>
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.system,
        minSize: configuration.tableColumnSize.mapping.system,
        maxSize: configuration.tableColumnSize.mapping.system * 2
      },
      {
        accessorKey: 'publicId',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              sortingShow: true,
              sortingDescTitle: `${t('candidates:publicIdOrder:desc')}`,
              sortingAscTitle: `${t('candidates:publicIdOrder:asc')}`,
              title: t('candidates:candidateTable:publicId'),
              sorting,
              setSorting
            }}
            accessorKey="publicId"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => (
          <EditingInlineTableBody
            bodyConfig={{
              mode: ENUMS_EDITING_INLINE_MODE.onlyView,
              content: convertValueToHTMLFromSearch({
                value: info.row.original.permittedFields?.publicId?.value || '-',
                searchValue
              })
            }}
            accessorKey="publicId"
          />
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.id,
        minSize: configuration.tableColumnSize.mapping.id,
        maxSize:
          configuration.tableColumnSize.maxFreeze -
          configuration.tableColumnSize.mapping.system -
          configuration.tableColumnSize.mapping.text
      },
      {
        accessorKey: 'fullName',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              sortingShow: true,
              sortingDescTitle: `${t('candidates:fullNameOrder:desc')}`,
              sortingAscTitle: `${t('candidates:fullNameOrder:asc')}`,
              title: t('candidates:candidateTable:fullName'),
              sorting,
              setSorting
            }}
            accessorKey="fullName"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => (
          <EditingInlineTableBody
            bodyConfig={{
              mode: ENUMS_EDITING_INLINE_MODE.editing,
              content: (
                <div className="flex items-center">
                  <div className="relative mr-2">
                    <Tooltip
                      classNameConfig={{
                        content: info?.row?.original?.employeeId ? '' : 'hidden'
                      }}
                      content={`${t('candidates:candidateTable:tooltips:internal')}`}>
                      <Avatar
                        src={info.row.original.avatarVariants?.thumb?.url}
                        alt={info.row.original.permittedFields?.fullName?.value}
                        size="xs"
                        color="#FFFFFF"
                      />
                      {info?.row?.original?.employeeId ? (
                        <div className="absolute right-0 bottom-0">
                          <svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clipPath="url(#clip0_28686_13473)">
                              <path
                                d="M4 0C6.21053 0 8 1.79257 8 4.00929C8 6.21362 6.20124 8.0031 3.99071 8C1.78328 7.9969 0 6.20124 0 3.98142C0 1.78947 1.79876 0 4 0ZM5.85758 4.12384C5.91641 4.12384 5.96594 4.12384 6.01548 4.12384C6.16409 4.11765 6.28173 4.03406 6.32508 3.90402C6.37152 3.7709 6.33127 3.64087 6.21362 3.5418C5.6161 3.04334 5.02167 2.5418 4.42415 2.04334C4.16409 1.82663 3.82972 1.82972 3.56966 2.04644C2.97833 2.5418 2.3839 3.04025 1.79257 3.5356C1.66873 3.63777 1.62848 3.76471 1.67183 3.90093C1.71827 4.03715 1.83591 4.11765 1.9969 4.12074C2.04334 4.12074 2.08978 4.12074 2.13932 4.12074V4.22291C2.13932 4.73375 2.13932 5.24458 2.13932 5.75232C2.13932 5.98452 2.26935 6.11455 2.50155 6.11455C2.7678 6.11455 3.03406 6.11455 3.29721 6.11455C3.32198 6.11455 3.34675 6.11455 3.37771 6.10836C3.37771 6.06502 3.37771 6.03406 3.37771 6C3.37771 5.65015 3.37152 5.30341 3.39009 4.95356C3.40557 4.64706 3.68421 4.41176 4 4.41486C4.31579 4.41486 4.59133 4.65016 4.60681 4.95975C4.62229 5.3096 4.6161 5.66254 4.6161 6.01238C4.6161 6.04334 4.6161 6.0743 4.6161 6.10836C4.64706 6.10836 4.67183 6.11455 4.6935 6.11455C4.96285 6.11455 5.2291 6.11455 5.49845 6.11455C5.72446 6.11455 5.85449 5.98142 5.85449 5.75232C5.85449 5.36842 5.85449 4.98142 5.85449 4.59752C5.85449 4.44272 5.85449 4.28483 5.85449 4.11765L5.85758 4.12384Z"
                                fill="#8B5CF6"
                              />
                              <path
                                d="M5.85758 4.12385C5.85758 4.29104 5.85758 4.44893 5.85758 4.60373C5.85758 4.98763 5.85758 5.37463 5.85758 5.75853C5.85758 5.98763 5.72755 6.11766 5.50154 6.12076C5.23219 6.12076 4.96594 6.12076 4.69659 6.12076C4.67492 6.12076 4.65015 6.12076 4.61919 6.11457C4.61919 6.08051 4.61919 6.04955 4.61919 6.01859C4.61919 5.66875 4.62538 5.31581 4.6099 4.96596C4.59442 4.65636 4.31888 4.42107 4.00309 4.42107C3.6873 4.42107 3.40866 4.65327 3.39318 4.95977C3.3777 5.30652 3.3839 5.65636 3.3808 6.00621C3.3808 6.04026 3.3808 6.07432 3.3808 6.11457C3.34984 6.11457 3.32507 6.12076 3.3003 6.12076C3.03405 6.12076 2.7678 6.12076 2.50464 6.12076C2.27244 6.12076 2.14241 5.99073 2.14241 5.75853C2.14241 5.24769 2.14241 4.73686 2.14241 4.22912V4.12695C2.08978 4.12695 2.04643 4.12695 2 4.12695C1.839 4.12385 1.72136 4.04336 1.67492 3.90714C1.62848 3.77091 1.67182 3.64398 1.79566 3.54181C2.38699 3.04646 2.98142 2.5511 3.57275 2.05265C3.83281 1.83283 4.16718 1.82974 4.42724 2.04955C5.02476 2.548 5.61919 3.04955 6.21671 3.548C6.33436 3.64707 6.37461 3.77711 6.32817 3.91023C6.28482 4.04026 6.16718 4.12386 6.01857 4.13005C5.96904 4.13005 5.9195 4.13005 5.86068 4.13005L5.85758 4.12385Z"
                                fill="white"
                              />
                            </g>
                            <defs>
                              <clipPath id="clip0_28686_13473">
                                <rect width="8" height="8" fill="white" />
                              </clipPath>
                            </defs>
                          </svg>
                        </div>
                      ) : null}
                    </Tooltip>
                  </div>

                  <span
                    className="mr-2 cursor-pointer text-sm font-medium text-gray-900"
                    dangerouslySetInnerHTML={{
                      __html: convertValueToHTMLFromSearch({
                        value: info.row.original.permittedFields?.fullName?.value || '-',
                        searchValue
                      })
                    }}
                  />

                  {info.row.original.applicants?.find((item) => item.flagNew) && (
                    <Badge color="green" radius="circular" size="sm" classNameText="whitespace-nowrap">
                      {t('label:new')}
                    </Badge>
                  )}
                </div>
              )
            }}
            editingConfig={{
              id: String(info.row.original?.id),
              mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.input,
              placeholder: `${t('candidates:placeholder:clickToEnterValue')}`,
              value: info.row.original.permittedFields?.fullName?.value,
              onChange: (value, accessorKey) => {
                onUpdateProfile({
                  [accessorKey]: value,
                  row: info.row,
                  id: Number(info.row.original?.id),
                  paramType: accessorKey
                })
              }
            }}
            accessorKey="fullName"
            schema={schemaUpdateProfile(t)}
            actions={[
              {
                iconMenus: 'ExternalLink',
                onClick: async () => {
                  window.open(configuration.path.candidates.detail(info.row.original?.id), '_blank')
                }
              }
            ]}
          />
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.text,
        minSize: configuration.tableColumnSize.mapping.text,
        maxSize:
          configuration.tableColumnSize.maxFreeze -
          configuration.tableColumnSize.mapping.system -
          (visibleColumns?.publicId ? configuration.tableColumnSize.mapping.id : 0)
      },
      {
        accessorKey: 'headline',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:headline')
            }}
            accessorKey="headline"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => (
          <EditingInlineTableBody
            bodyConfig={{
              mode: ENUMS_EDITING_INLINE_MODE.editing,
              content: convertValueToHTMLFromSearch({
                value: info.row.original.permittedFields?.headline?.value || '-',
                searchValue
              })
            }}
            editingConfig={{
              id: String(info.row.original?.id),
              mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.input,
              placeholder: `${t('candidates:tabs:candidateOverview:profileInformation:add_position')}`,
              value: info.row.original.permittedFields?.headline?.value,
              onChange: (value, accessorKey) =>
                onUpdateProfile({
                  [accessorKey]: value,
                  row: info.row,
                  id: Number(info.row.original?.id),
                  paramType: accessorKey
                })
            }}
            schema={schemaUpdateProfile(t)}
            accessorKey="headline"
          />
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.text
      },
      {
        accessorKey: 'departments',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:departments')
            }}
            accessorKey="departmentIds"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          const departments: string[] = info?.row?.original?.permittedFields?.departments.value?.departments || []

          const allDepartments = info?.row?.original?.permittedFields?.departments?.value?.all_departments

          const suggestionChipDepartments = departments.map((dep) => ({
            label: dep,
            maxLength: 30
          }))
          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.onlyView,
                content: (() => {
                  if (allDepartments) {
                    return (
                      <TypographyText className="truncate text-sm text-gray-900 dark:text-gray-300">
                        {isCompanyKind ? t('settings:team:allTeams') : t('settings:departments:allDepartments')}
                      </TypographyText>
                    )
                  }

                  return suggestionChipDepartments.length > 0 ? (
                    <WithComputedMaxItemsChips
                      totalCount={suggestionChipDepartments.length}
                      className="relative -mt-2 flex items-center">
                      {({ maxItems }) => (
                        <SuggestionInlineChips
                          size="sm"
                          source={suggestionChipDepartments}
                          type="default"
                          maxItems={maxItems}
                        />
                      )}
                    </WithComputedMaxItemsChips>
                  ) : (
                    <TypographyText className="text-sm text-gray-900">-</TypographyText>
                  )
                })()
              }}
              accessorKey="departmentIds"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.tags
      },
      {
        accessorKey: 'email',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:email')
            }}
            accessorKey="email"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => (
          <EditingInlineTableBody
            bodyConfig={{
              mode: info.row.original.employeeId
                ? ENUMS_EDITING_INLINE_MODE.onlyView
                : ENUMS_EDITING_INLINE_MODE.editing,
              content: info.row.original.employeeId ? (
                <Tooltip
                  classNameConfig={{
                    content: `${info.row.original?.employeeId ? '' : 'hidden'}`
                  }}
                  content={`${t(`tooltip:thisFieldIsBlocked`)}`}>
                  <div
                    className="text-gray-900"
                    dangerouslySetInnerHTML={{
                      __html: convertValueToHTMLFromSearch({
                        value: info.row.original.permittedFields?.email?.value?.[0] || '-',
                        searchValue
                      })
                    }}
                  />
                </Tooltip>
              ) : (
                convertValueToHTMLFromSearch({
                  value: info.row.original.permittedFields?.email?.value?.[0] || '-',
                  searchValue
                })
              )
            }}
            editingConfig={{
              id: String(info.row.original?.id),
              mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.inputArray,
              placeholder: `${t('candidates:tabs:candidateOverview:contactDetails:addEmail')}`,
              value: info.row.original.permittedFields?.email?.value || [],
              onChange: (value, accessorKey) =>
                onUpdateProfile({
                  [accessorKey]: value,
                  row: info.row,
                  id: Number(info.row.original?.id),
                  paramType: accessorKey
                })
            }}
            schema={schemaUpdateProfile(t)}
            accessorKey="email"
          />
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.text
      },
      {
        accessorKey: 'phoneNumber',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:phoneNumber')
            }}
            accessorKey="phoneNumber"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => (
          <EditingInlineTableBody
            bodyConfig={{
              mode: ENUMS_EDITING_INLINE_MODE.editing,
              content: convertValueToHTMLFromSearch({
                value: info.row.original.permittedFields?.phoneNumber?.value?.[0] || '-',
                searchValue
              })
            }}
            editingConfig={{
              id: String(info.row.original?.id),
              mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.phone,
              placeholder: `${t('candidates:tabs:candidateOverview:contactDetails:addPhoneNumber')}`,
              value: info.row.original.permittedFields?.phoneNumber?.value || [],
              onChange: (value, accessorKey) =>
                onUpdateProfile({
                  [accessorKey]: value,
                  row: info.row,
                  id: Number(info.row.original?.id),
                  paramType: accessorKey
                })
            }}
            schema={schemaCandidateProfilePhoneNumber(t)}
            accessorKey="phoneNumber"
          />
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.qualitativeNumber
      },
      {
        accessorKey: 'location',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:location')
            }}
            accessorKey="locationWithStateID"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => (
          <EditingInlineTableBody
            bodyConfig={{
              mode: ENUMS_EDITING_INLINE_MODE.editing,
              content: convertValueToHTMLFromSearch({
                value: info.row.original.permittedFields?.location?.value || '-',
                searchValue
              })
            }}
            editingConfig={{
              id: String(info.row.original?.id),
              mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.select,
              select: {
                options: promiseCountryStateOptions,
                size: 'md',
                configSelectOption: {
                  supportingText: ['name']
                },
                searchPlaceholder: `${t('label:placeholder:search')}`,
                loadingMessage: `${t('label:loading')}`,
                noOptionsMessage: `${t('label:noOptions')}`,
                onChange: () => ({})
              },
              placeholder: `${t('candidates:tabs:candidateOverview:contactDetails:addLocation')}`,
              value: info.row.original.permittedFields?.location?.value
                ? {
                    value: info.row.original.permittedFields?.location?.value,
                    supportingObj: {
                      name: info.row.original.permittedFields?.location?.value
                    }
                  }
                : undefined,
              onChange: (value, accessorKey) =>
                onUpdateProfile({
                  [accessorKey]: value,
                  row: info.row,
                  id: Number(info.row.original?.id),
                  paramType: accessorKey
                })
            }}
            schema={schemaUpdateProfile(t)}
            accessorKey="locationWithStateID"
          />
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.text
      },
      {
        accessorKey: 'links',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:links')
            }}
            accessorKey="links"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          const getDisabledLinks = ({
            links = undefined
          }: {
            links?: {
              [key: string]: Array<string>
            }
          }) => {
            if (!links || Object.keys(links).length === 0) return false

            let count = 0
            Object.keys(links).forEach((item) => {
              count += links?.[item]?.length || 0
            })

            if (count >= 15) return true
            return false
          }

          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.editing,
                content: (
                  <>
                    {Object.keys(info?.row?.original?.permittedFields?.links?.value || {}).length > 0 ? (
                      <SocialLinksWithAction
                        className="!space-x-1"
                        size="xs"
                        source={info?.row?.original?.permittedFields?.links?.value || {}}
                        maxItems={5}
                        onDeleteLink={(value) => {
                          openAlert({
                            isPreventAutoFocusDialog: false,
                            className: 'w-[480px]',
                            title: `${t('candidates:tabs:candidateOverview:link:deleteTitle')}`,
                            description: (
                              <Trans
                                i18nKey="candidates:tabs:candidateOverview:link:deleteDescription"
                                values={{
                                  link: value
                                }}>
                                <span className="font-medium text-gray-900" />
                              </Trans>
                            ),
                            actions: [
                              {
                                label: `${t('button:cancel')}`,
                                type: 'secondary',
                                size: 'sm'
                              },
                              {
                                isCallAPI: true,
                                label: `${t('button:remove')}`,
                                size: 'sm',
                                onClick: async () => {
                                  onUpdateProfile({
                                    links: {
                                      _destroy: true,
                                      links: [value]
                                    } as unknown as {
                                      [key: string]: Array<string>
                                    },
                                    row: info.row,
                                    id: Number(info.row.original?.id),
                                    paramType: 'links'
                                  })
                                }
                              }
                            ]
                          })
                        }}
                      />
                    ) : null}
                  </>
                )
              }}
              editingConfig={{
                id: String(info.row.original?.id),
                mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.link,
                placeholder: `${t('candidates:tabs:candidateOverview:contactDetails:addLinks')}`,
                isDisabled: getDisabledLinks({
                  links: info?.row?.original?.permittedFields?.links?.value
                }),
                isDisabledTooltip: `${t('candidates:tabs:candidateOverview:contactDetails:maximumLinks', { number: 15 })}`,
                value: '',
                configShow: {
                  left: 12,
                  top: 32
                },
                onChange: (value, accessorKey) =>
                  onUpdateProfile({
                    [accessorKey]: {
                      _destroy: false,
                      links: [value]
                    },
                    row: info.row,
                    id: Number(info.row.original?.id),
                    paramType: accessorKey
                  })
              }}
              accessorKey="links"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.links
      },
      {
        accessorKey: 'resume',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:resume')
            }}
            accessorKey="resume"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          const profile = info?.row?.original?.profileCvs
          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.onlyView,
                content: (
                  <div className="flex h-[20px] items-center">
                    {profile?.length > 0 ? (
                      <div
                        className="cursor-pointer"
                        onClick={() => {
                          window.open(profile?.[0]?.attachments?.[0]?.file, '_blank')
                        }}>
                        <IconWrapper name="Paperclip" size={16} />
                      </div>
                    ) : (
                      <TypographyText className="text-sm text-gray-900">-</TypographyText>
                    )}
                  </div>
                )
              }}
              accessorKey="resume"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.file
      },
      {
        accessorKey: 'tag',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:tag')
            }}
            accessorKey="tags"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          const tags = info?.row?.original?.tags
          const formatTags = (tags || []).map((item: ITags) => {
            return {
              label: item.value,
              value: item.id,
              supportingObj: {
                name: item.name
              }
            }
          })

          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.editing,
                content: (
                  <>
                    {tags?.length > 0 ? (
                      <div className="-mt-2 flex items-center">
                        <SuggestionInlineChips
                          size="sm"
                          source={tags?.map((item) => ({
                            label: convertValueToHTMLFromSearch({
                              value: String(item?.name),
                              searchValue
                            }),
                            maxLength: 30
                          }))}
                          type="default"
                        />
                      </div>
                    ) : (
                      <TypographyText className="text-sm text-gray-900">-</TypographyText>
                    )}
                  </>
                )
              }}
              editingConfig={{
                id: String(info.row.original?.id),
                mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.select,
                select: {
                  creatable: !!actionTag,
                  isMulti: true,
                  size: 'md',
                  configSelectOption: {
                    option: 'checkbox',
                    supportingText: ['name']
                  },
                  searchPlaceholder: `${t('label:placeholder:search')}`,
                  loadingMessage: `${t('label:loading')}`,
                  noOptionsMessage: `${t('label:noOptions')}`,
                  options: promiseTagsOptions,
                  onChange: () => ({})
                },
                placeholder: `${t('candidates:tabs:candidateOverview:addTag')}`,
                value: formatTags,
                onCreate: async (value, callback) => {
                  const formatValue = (value as ISelectOption[]).filter((item) => item.__isNew__)
                  const originalTags = info?.row?.original?.tags || []
                  const mappingValueIds = formatValue.map((item) => item.value)
                  const mappingTagIds = originalTags.map((item: ITags) => Number(item.id))

                  const filterAddedOrCreated = mappingValueIds.filter((item) => !mappingTagIds.includes(Number(item)))
                  const filterCreateTags = filterAddedOrCreated.filter((item) => typeof item === 'string')

                  const arr = []
                  if (filterCreateTags.length) {
                    for (let i = 0; i < filterCreateTags.length; i++) {
                      const item = String(filterCreateTags[i])
                      if (
                        validationTag({ option: { value: item }, action: 'create-option' }, value as ISelectOption[])
                      ) {
                        const newTag = await onCreateTags(item, String(info.row.original?.id))
                        arr.push({
                          id: String(newTag?.id),
                          index: (value as ISelectOption[]).findIndex((i) => String(i.value) === item)
                        })
                      }
                    }
                  }

                  callback && callback(arr)
                },
                onChange: async (value, accessorKey) => {
                  const formatValue = value as ISelectOption[]
                  const originalTags = info?.row?.original?.tags || []
                  const mappingValueIds = formatValue.map((item: ISelectOption) =>
                    item.__isNew__ ? item.value : Number(item.value)
                  )
                  const mappingTagIds = originalTags.map((item: ITags) => Number(item.id))

                  // Tag should be removed
                  const filterRemoved = mappingTagIds.filter((item) => !mappingValueIds.includes(item))
                  if (filterRemoved.length) {
                    await onDeleteTags(filterRemoved, String(info.row.original?.id))
                  }

                  // Tag should be added or created
                  const filterAddedOrCreated = mappingValueIds.filter((item) => !mappingTagIds.includes(Number(item)))

                  const filterAddTags = filterAddedOrCreated.filter((item) => typeof item === 'number')
                  if (filterAddTags.length) {
                    await onAddTags(filterAddTags as number[], String(info.row.original?.id))
                  }

                  const filterCreateTags = filterAddedOrCreated.filter((item) => typeof item === 'string')
                  if (filterCreateTags.length) {
                    let formatValueCreateTags = JSON.parse(JSON.stringify(formatValue))
                    for (let i = 0; i < filterCreateTags.length; i++) {
                      const item = String(filterCreateTags[i])
                      if (
                        !validationTag({ option: { value: item }, action: 'create-option' }, value as ISelectOption[])
                      ) {
                        formatValueCreateTags = formatValue.filter(
                          (ii: ISelectOption) => String(ii.value) !== String(item)
                        )
                      }
                    }

                    if (tableRef.current.options?.meta?.updateData) {
                      tableRef.current.options?.meta?.updateData(
                        info.row.index,
                        'tags',
                        formatValueCreateTags.map((item: ISelectOption) => ({
                          value: item.label,
                          id: item.value,
                          name: item.supportingObj?.name
                        }))
                      )
                    }
                  } else {
                    if (tableRef.current.options?.meta?.updateData) {
                      tableRef.current.options?.meta?.updateData(
                        info.row.index,
                        'tags',
                        formatValue.map((item) => ({
                          value: item.label,
                          id: item.value,
                          name: item.supportingObj?.name
                        }))
                      )
                    }
                  }
                }
              }}
              schema={schemaUpdateProfile(t)}
              accessorKey="tags"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.tags
      },
      {
        accessorKey: 'talentPool',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:talentPools')
            }}
            accessorKey="profileTalentPoolIds"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          const talentPools = info?.row?.original?.permittedFields?.talentPools?.value || []
          const formatTalentPools = talentPools?.map((item: TalentPoolType) => {
            return {
              value: String(item.id),
              supportingObj: {
                name: item.name
              }
            }
          })

          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.editing,
                content: (
                  <>
                    {talentPools?.length > 0 ? (
                      <div className="-mt-2 flex items-center">
                        <SuggestionInlineChips
                          size="sm"
                          source={talentPools?.map((item, index) => ({
                            label: convertValueToHTMLFromSearch({
                              value: String(item?.name),
                              searchValue
                            }),
                            maxLength: 25,
                            onClick: () => {
                              if (index !== undefined && index !== 9999) {
                                window.open(`${configuration.path.talentPool.detail(Number(item?.id))}`, '_blank')
                              }
                            }
                          }))}
                          type="default"
                        />
                      </div>
                    ) : (
                      <TypographyText className="text-sm text-gray-900">-</TypographyText>
                    )}
                  </>
                )
              }}
              editingConfig={{
                id: String(info.row.original?.id),
                mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.select,
                select: {
                  isMulti: true,
                  size: 'md',
                  configSelectOption: {
                    option: 'checkbox',
                    supportingText: ['name']
                  },
                  searchPlaceholder: `${t('label:placeholder:search')}`,
                  loadingMessage: `${t('label:loading')}`,
                  noOptionsMessage: `${t('label:noOptions')}`,
                  options: promiseTalentPoolOptions,
                  onChange: () => ({})
                },
                placeholder: `${t('candidates:tabs:candidateOverview:profileInformation:addTalentPool')}`,
                value: formatTalentPools,
                onChange: (value, accessorKey) =>
                  onUpdateProfile({
                    [accessorKey]: value,
                    row: info.row,
                    id: Number(info.row.original?.id),
                    paramType: accessorKey
                  })
              }}
              schema={schemaCandidateProfileTalentPools(t)}
              accessorKey="profileTalentPoolIds"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.tags
      },
      {
        accessorKey: 'jobs',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:jobs')
            }}
            accessorKey="jobs"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.onlyView,
                content: (
                  <div className="flex items-center">
                    {(info.row.original.applicants || [])?.length > 1 ? (
                      <Popover>
                        <PopoverTrigger asChild>
                          <TextButton
                            label={`${t('candidates:candidateTable:countJobs', {
                              count: info.row.original.applicants?.length
                            })}`}
                          />
                        </PopoverTrigger>

                        <PopoverPortal>
                          <PopoverContent
                            align="start"
                            sideOffset={10}
                            className="w-[320px] p-1"
                            onOpenAutoFocus={(e) => e.preventDefault()}>
                            {(info.row.original.applicants || []).map((item, index) => {
                              return (
                                <div key={index} className="px-2 py-1.5">
                                  <div className="flex space-x-1.5">
                                    <Tooltip
                                      classNameAsChild="flex pt-1.5 hover:cursor-pointer"
                                      content={item?.job?.statusDescription}>
                                      {item && (
                                        <Dot
                                          size="xl"
                                          color={JOB_DOT_STATUS(item?.job?.status || 'gray') as IDotColorProps}
                                        />
                                      )}
                                    </Tooltip>
                                    <div>
                                      {item?.job?.currentUserAccessible ? (
                                        <div className="flex">
                                          <>
                                            <Link href={configuration.path.jobs.detail(Number(item.job?.id))}>
                                              <div className="flex">
                                                <Tooltip content={item?.job?.title}>
                                                  <TypographyText className="line-clamp-1 max-w-[192px] text-sm text-gray-900 hover:underline">
                                                    {item?.job?.title}
                                                  </TypographyText>
                                                </Tooltip>
                                              </div>
                                            </Link>
                                            {() => {
                                              const companyName = isCompanyKind
                                                ? item?.job?.company?.permittedFields?.name?.value
                                                : item?.job?.permittedFields?.company?.value?.name
                                              return (
                                                companyName && (
                                                  <Tooltip content={companyName}>
                                                    <TypographyText className="ml-1 line-clamp-1 text-sm text-gray-900">
                                                      ({companyName})
                                                    </TypographyText>
                                                  </Tooltip>
                                                )
                                              )
                                            }}
                                          </>
                                        </div>
                                      ) : (
                                        <div className="flex">
                                          <>
                                            <Tooltip
                                              classNameConfig={{
                                                content: 'max-w-[367px]'
                                              }}
                                              content={
                                                <>
                                                  <div>{item?.job?.title}</div>
                                                  <div>{t('common:you_not_in_hiring_team')}</div>
                                                </>
                                              }>
                                              <TypographyText className="line-clamp-1 max-w-[192px] text-sm text-gray-600">
                                                {item?.job?.title}
                                              </TypographyText>
                                            </Tooltip>
                                            {() => {
                                              const companyName = isCompanyKind
                                                ? item?.job?.company?.permittedFields?.name?.value
                                                : item?.job?.permittedFields?.company?.value?.name

                                              return (
                                                companyName && (
                                                  <Tooltip content={companyName}>
                                                    <TypographyText className="ml-1 line-clamp-1 text-sm text-gray-900">
                                                      ({companyName})
                                                    </TypographyText>
                                                  </Tooltip>
                                                )
                                              )
                                            }}
                                          </>
                                        </div>
                                      )}

                                      <div>
                                        {item?.status === 'rejected' ? (
                                          <div className="flex items-center space-x-1.5">
                                            <IconWrapper name="Ban" size={14} className="flex-none text-red-500" />
                                            <Tooltip content={item?.rejectedReasonLabel}>
                                              <TypographyText className="line-clamp-1 text-xs text-gray-700">
                                                {item?.rejectedReasonLabel}
                                              </TypographyText>
                                            </Tooltip>
                                          </div>
                                        ) : (
                                          <TypographyText className="flex items-center text-xs text-gray-700">
                                            {item?.jobStage?.stageLabel}
                                          </TypographyText>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              )
                            })}
                          </PopoverContent>
                        </PopoverPortal>
                      </Popover>
                    ) : (
                      <div className="flex w-full items-center space-x-1.5">
                        <Tooltip
                          classNameAsChild="flex items-center hover:cursor-pointer"
                          content={info.row.original.applicants?.[0]?.job?.statusDescription}>
                          {info.row.original?.applicants?.[0] && (
                            <Dot
                              size="sm"
                              color={
                                JOB_DOT_STATUS(
                                  info.row.original.applicants?.[0]?.job?.status || 'gray'
                                ) as IDotColorProps
                              }
                            />
                          )}
                        </Tooltip>

                        {info.row.original?.applicants && info.row.original?.applicants?.length > 0 ? (
                          <>
                            {info.row.original?.applicants?.[0]?.job?.currentUserAccessible ? (
                              <WithComputedMaxColumnJob
                                className="flex w-[284px] flex-1"
                                leftContent={
                                  <Tooltip content={info.row.original?.applicants?.[0]?.job?.title}>
                                    <Link
                                      href={configuration.path.jobs.detail(
                                        Number(
                                          info.row.original?.applicants && info.row.original?.applicants[0]?.job?.id
                                        )
                                      )}>
                                      <span
                                        className="max-w-max truncate text-sm text-gray-900 hover:underline"
                                        dangerouslySetInnerHTML={{
                                          __html: convertValueToHTMLFromSearch({
                                            value: String(info.row.original?.applicants[0]?.job?.title),
                                            searchValue
                                          })
                                        }}
                                      />
                                    </Link>
                                  </Tooltip>
                                }
                                rightContent={
                                  (isCompanyKind
                                    ? info.row.original?.applicants?.[0]?.job?.company?.permittedFields?.name?.value
                                    : info.row.original?.applicants?.[0]?.job?.permittedFields?.company?.value
                                        ?.name) && (
                                    <Tooltip
                                      content={
                                        isCompanyKind
                                          ? info.row.original?.applicants?.[0]?.job?.company?.permittedFields?.name
                                              ?.value
                                          : info.row.original?.applicants?.[0]?.job?.permittedFields?.company?.value
                                              ?.name
                                      }>
                                      <span
                                        className="ml-1 max-w-max truncate text-sm text-gray-600"
                                        dangerouslySetInnerHTML={{
                                          __html: convertValueToHTMLFromSearch({
                                            value: `(${
                                              isCompanyKind
                                                ? info.row.original?.applicants[0]?.job?.company?.permittedFields?.name
                                                    ?.value
                                                : info.row.original?.applicants?.[0]?.job?.permittedFields?.company
                                                    ?.value?.name
                                            })`,
                                            searchValue
                                          })
                                        }}
                                      />
                                    </Tooltip>
                                  )
                                }
                              />
                            ) : (
                              <WithComputedMaxColumnJob
                                className="flex w-[284px] flex-1"
                                maxWidth={284}
                                leftContent={
                                  <Tooltip
                                    classNameConfig={{
                                      content: 'max-w-[367px]'
                                    }}
                                    content={
                                      <>
                                        <div>{info.row.original?.applicants?.[0]?.job?.title}</div>
                                        <div>{t('common:you_not_in_hiring_team')}</div>
                                      </>
                                    }>
                                    <span
                                      className="max-w-max truncate text-sm text-gray-600"
                                      dangerouslySetInnerHTML={{
                                        __html: convertValueToHTMLFromSearch({
                                          value: String(info.row.original?.applicants?.[0]?.job?.title),
                                          searchValue
                                        })
                                      }}
                                    />
                                  </Tooltip>
                                }
                                rightContent={
                                  (isCompanyKind
                                    ? info.row.original?.applicants?.[0]?.job?.company?.permittedFields?.name?.value
                                    : info.row.original?.applicants?.[0]?.job?.permittedFields?.company?.value
                                        ?.name) && (
                                    <Tooltip
                                      content={
                                        <>
                                          <div>
                                            {isCompanyKind
                                              ? info.row.original?.applicants?.[0]?.job?.company?.permittedFields?.name
                                                  ?.value
                                              : info.row.original?.applicants?.[0]?.job?.permittedFields?.company?.value
                                                  ?.name}
                                          </div>
                                        </>
                                      }>
                                      <span
                                        className="ml-1 truncate text-sm text-gray-600"
                                        dangerouslySetInnerHTML={{
                                          __html: convertValueToHTMLFromSearch({
                                            value:
                                              (isCompanyKind
                                                ? info.row.original?.applicants?.[0]?.job?.company?.permittedFields
                                                    ?.name?.value
                                                : info.row.original?.applicants?.[0]?.job?.permittedFields?.company
                                                    ?.value?.name) || '',
                                            searchValue
                                          })
                                        }}
                                      />
                                    </Tooltip>
                                  )
                                }
                              />
                            )}
                          </>
                        ) : (
                          <TypographyText className="!ml-0 text-sm text-gray-900">-</TypographyText>
                        )}
                      </div>
                    )}
                  </div>
                )
              }}
              accessorKey="jobs"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.text
      },
      {
        accessorKey: 'stage',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:stage')
            }}
            accessorKey="stage"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => (
          <EditingInlineTableBody
            bodyConfig={{
              mode: ENUMS_EDITING_INLINE_MODE.onlyView,
              content: (
                <div className="flex items-center">
                  {(info.row.original.applicants || [])?.length > 1 ? (
                    <TypographyText className="text-sm text-gray-900">
                      <Trans i18nKey="candidates:candidateTable:multipleStage" />
                    </TypographyText>
                  ) : info.row.original?.applicants?.[0] ? (
                    info.row.original?.applicants?.[0]?.status === 'rejected' ? (
                      <div className="flex items-center space-x-1.5">
                        <IconWrapper name="Ban" size={16} className="flex-none text-red-500" />
                        <Tooltip content={info.row.original?.applicants[0]?.rejectedReasonLabel}>
                          <span
                            className="line-clamp-1 text-sm text-gray-900"
                            dangerouslySetInnerHTML={{
                              __html: convertValueToHTMLFromSearch({
                                value: info.row.original?.applicants[0]?.rejectedReasonLabel,
                                searchValue
                              })
                            }}
                          />
                        </Tooltip>
                      </div>
                    ) : (
                      <Badge
                        type="dotLeading"
                        color={
                          JOB_COLOR_STAGE_NAME(
                            String(
                              (stageTypes || []).filter(
                                (s: IStageType) =>
                                  String(s.id) === String(info.row.original?.applicants?.[0]?.jobStage?.stageTypeId)
                              )?.[0]?.colorClassName
                            )
                          ) as IColorBadgeType
                        }
                        dotColor={
                          mappingColorByStageType(
                            (stageTypes || []).filter(
                              (s: IStageType) =>
                                String(s.id) === String(info.row.original?.applicants?.[0]?.jobStage?.stageTypeId)
                            )?.[0]?.colorClassName
                          ) as IDotColorProps
                        }
                        size="md"
                        classNameText="truncate"
                        className="max-w-full">
                        {convertValueToHTMLFromSearch({
                          value: String(info.row.original?.applicants[0]?.jobStage?.stageLabel),
                          searchValue
                        })}
                      </Badge>
                    )
                  ) : (
                    <TypographyText className="text-sm text-gray-900">-</TypographyText>
                  )}
                </div>
              )
            }}
            accessorKey="stage"
          />
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.badge
      },
      {
        accessorKey: 'owner',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:owner')
            }}
            accessorKey="ownerId"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          const owner = info.row.original.owner
          const formatOwner = owner
            ? {
                value: owner.id,
                avatar: owner?.avatarVariants?.thumb?.url,
                avatarVariants: owner.avatarVariants,
                supportingObj: {
                  name: owner?.fullName,
                  defaultColour: owner?.defaultColour
                }
              }
            : undefined

          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.editing,
                content: (
                  <div className="flex">
                    {!!owner ? (
                      <div className="flex items-center">
                        <Avatar
                          size="xs"
                          src={owner?.avatarVariants?.thumb?.url}
                          color={owner?.defaultColour}
                          alt={owner?.fullName}
                        />
                        <div className="ml-2 overflow-hidden text-left">
                          <div className="flex items-center">
                            <p className="line-clamp-1 text-sm font-medium break-all text-gray-900 dark:text-gray-200">
                              {owner.fullName}
                            </p>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <TypographyText className="text-sm text-gray-900">-</TypographyText>
                    )}
                  </div>
                )
              }}
              editingConfig={{
                id: String(info.row.original?.id),
                mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.select,
                select: {
                  size: 'md',
                  configSelectOption: {
                    avatar: true,
                    supportingText: ['name']
                  },
                  searchPlaceholder: `${t('label:placeholder:search')}`,
                  loadingMessage: `${t('label:loading')}`,
                  noOptionsMessage: `${t('label:noOptions')}`,
                  options: promiseMembersOwnerOptionsFetchAll,
                  onChange: () => ({})
                },
                placeholder: `${t('candidates:tabs:candidateOverview:contactDetails:owner:search')}`,
                value: formatOwner,
                onChange: (value, accessorKey) =>
                  onUpdateProfile({
                    [accessorKey]: value,
                    row: info.row,
                    id: Number(info.row.original?.id),
                    paramType: accessorKey
                  })
              }}
              schema={schemaUpdateProfile(t)}
              accessorKey="ownerId"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.image
      },
      {
        accessorKey: 'summary',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:summary')
            }}
            accessorKey="summary"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => (
          <EditingInlineTableBody
            bodyConfig={{
              mode: ENUMS_EDITING_INLINE_MODE.editing,
              content: (
                <span
                  className="mr-2 truncate text-sm font-normal text-gray-900"
                  dangerouslySetInnerHTML={{
                    __html: convertValueToHTMLFromSearch({
                      value: removeHTMLTags(info?.row?.original?.permittedFields?.summary?.value || '') || '-',
                      searchValue
                    })
                  }}
                />
              )
            }}
            editingConfig={{
              id: String(info.row.original?.id),
              mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.richEditor,
              editor: {
                title: `${t('candidates:candidateTable:summary')}`,
                cancel: `${t('button:cancel')}`,
                save: `${t('button:save')}`,
                limit: 100000,
                showCount: true,
                size: 'sm'
              },
              placeholder: `${t('candidates:tabs:candidateOverview:summary:addSummary')}`,
              value: info?.row?.original?.permittedFields?.summary?.value,
              onChange: (value, accessorKey) =>
                onUpdateProfile({
                  [accessorKey]: value,
                  row: info.row,
                  id: Number(info.row.original?.id),
                  paramType: accessorKey
                })
            }}
            schema={schemaCandidateProfileRichEditorDynamic(t, 'summary', 100000)}
            accessorKey="summary"
          />
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.paragraph
      },
      {
        accessorKey: 'skills',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:skills')
            }}
            accessorKey="skills"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          const skills = info?.row?.original?.permittedFields?.skills?.value || []
          const formatSkills = skills?.map((item: string) => {
            return {
              value: String(item),
              supportingObj: {
                name: item
              }
            }
          })

          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.editing,
                content: (
                  <>
                    {skills?.length > 0 ? (
                      <div className="-mt-2 flex items-center">
                        <SuggestionInlineChips
                          size="sm"
                          source={skills?.map((item, index) => ({
                            label: convertValueToHTMLFromSearch({
                              value: String(item),
                              searchValue
                            }),
                            maxLength: 25
                          }))}
                          type="default"
                        />
                      </div>
                    ) : (
                      <TypographyText className="text-sm text-gray-900">-</TypographyText>
                    )}
                  </>
                )
              }}
              editingConfig={{
                id: String(info.row.original?.id),
                mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.select,
                select: {
                  creatable: !!allowAddNewSkill,
                  isMulti: true,
                  size: 'md',
                  configSelectOption: {
                    option: 'checkbox',
                    supportingText: ['name']
                  },
                  searchPlaceholder: `${t('label:placeholder:search')}`,
                  loadingMessage: `${t('label:loading')}`,
                  noOptionsMessage: `${t('label:noOptions')}`,
                  options: promiseSkillsOptions,
                  onChange: () => ({})
                },
                placeholder: `${t('candidates:tabs:candidateOverview:profileInformation:addSkills')}`,
                value: formatSkills,
                triggerUpdatedOnCreate: true,
                onCreate: async (value, callback) => {
                  const formatValue = (value as ISelectOption[]).filter((item) => item.__isNew__)
                  const originalSkills = info?.row?.original?.permittedFields?.skills?.value || []
                  const mappingValueIds = formatValue.map((item) => item.value)

                  onUpdateProfile({
                    skills: [...originalSkills, ...mappingValueIds],
                    row: info.row,
                    id: Number(info.row.original?.id),
                    paramType: 'skills'
                  })
                },
                onChange: (value, accessorKey) => {
                  const formatValue = value as ISelectOption[]
                  onUpdateProfile({
                    [accessorKey]: formatValue?.map((item) => item.supportingObj?.name),
                    row: info.row,
                    id: Number(info.row.original?.id),
                    paramType: accessorKey
                  })
                }
              }}
              schema={schemaCandidateProfileSkills(t)}
              accessorKey="skills"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.tags
      },
      {
        accessorKey: 'openToWork',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:tabs:candidateOverview:profileInformation:openToWork')
            }}
            accessorKey="openToWork"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => (
          <EditingInlineTableBody
            bodyConfig={{
              mode: ENUMS_EDITING_INLINE_MODE.editing,
              content: <></>
            }}
            editingConfig={{
              id: String(info.row.original?.id),
              mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.toggle,
              value: info?.row?.original?.permittedFields?.openToWork?.value,
              onChange: (value, accessorKey) =>
                onUpdateProfile({
                  [accessorKey]: value,
                  row: info.row,
                  id: Number(info.row.original?.id),
                  paramType: accessorKey
                })
            }}
            schema={schemaUpdateProfile(t)}
            accessorKey="openToWork"
          />
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.boolean
      },
      {
        accessorKey: 'languages',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:tabs:candidateOverview:profileInformation:languages')
            }}
            accessorKey="languages"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          const languages = info?.row?.original?.permittedFields?.languages?.value || []
          const formatLanguages = languages?.map((item: LanguagesType) => {
            return {
              value: String(item.language),
              supportingObj: {
                name: item.languageDescription
              },
              extras: {
                proficiency: item.proficiency,
                proficiencyDescription: item.proficiencyDescription
              }
            }
          })

          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.editing,
                content: (
                  <>
                    {formatLanguages?.length > 0 ? (
                      <div className="-mt-2 flex items-center">
                        <SuggestionInlineChips
                          size="sm"
                          source={formatLanguages?.map((item, index) => ({
                            label: convertValueToHTMLFromSearch({
                              value: String(item.supportingObj.name),
                              searchValue
                            }),
                            maxLength: 25
                          }))}
                          type="default"
                        />
                      </div>
                    ) : (
                      <TypographyText className="text-sm text-gray-900">-</TypographyText>
                    )}
                  </>
                )
              }}
              editingConfig={{
                id: String(info.row.original?.id),
                mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.select,
                select: {
                  isMulti: true,
                  options: languageList,
                  windowMenuList: {
                    width: 240,
                    height: 32
                  },
                  size: 'md',
                  configSelectOption: {
                    supportingText: ['name']
                  },
                  searchPlaceholder: `${t('label:placeholder:search')}`,
                  loadingMessage: `${t('label:loading')}`,
                  noOptionsMessage: `${t('label:noOptions')}`,
                  onChange: () => ({})
                },
                placeholder: `${t('candidates:tabs:candidateOverview:languages:languagePlaceholder')}`,
                value: formatLanguages,
                onChange: (value, accessorKey) =>
                  onUpdateProfile({
                    [accessorKey]: value,
                    row: info.row,
                    id: Number(info.row.original?.id),
                    paramType: accessorKey
                  })
              }}
              schema={schemaUpdateProfile(t)}
              accessorKey="languages"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.text
      },
      {
        accessorKey: 'nationality',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:tabs:candidateOverview:profileInformation:nationality')
            }}
            accessorKey="nationality"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          const nationality = info?.row?.original?.permittedFields?.nationality?.value
          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.editing,
                content: (
                  <>
                    {nationality ? (
                      <Tooltip align="start" content={nationality}>
                        <span
                          className="truncate text-sm font-normal text-gray-900"
                          dangerouslySetInnerHTML={{
                            __html: convertValueToHTMLFromSearch({
                              value: nationality,
                              searchValue
                            })
                          }}
                        />
                      </Tooltip>
                    ) : (
                      <TypographyText className="text-sm font-normal text-gray-900">-</TypographyText>
                    )}
                  </>
                )
              }}
              editingConfig={{
                id: String(info.row.original?.id),
                mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.input,
                placeholder: `${t('candidates:tabs:candidateOverview:profileInformation:inputCountry')}`,
                value: nationality,
                onChange: (value, accessorKey) =>
                  onUpdateProfile({
                    [accessorKey]: value,
                    row: info.row,
                    id: Number(info.row.original?.id),
                    paramType: accessorKey
                  })
              }}
              schema={schemaUpdateProfile(t)}
              accessorKey="nationality"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.text
      },
      {
        accessorKey: 'birthday',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              sortingShow: true,
              sortingDescTitle: `${t('candidates:publicIdOrder:desc')}`,
              sortingAscTitle: `${t('candidates:publicIdOrder:asc')}`,
              title: t('candidates:tabs:candidateOverview:profileInformation:birthday'),
              sorting,
              setSorting
            }}
            accessorKey="birthday"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          const birthdayFull = info.row.original.permittedFields?.birthday
          const year = birthdayFull?.value?.birth_year
          const month = birthdayFull?.value?.birth_month
          const date = birthdayFull?.value?.birth_date
          const formatBirthday = birthdayFull
            ? {
                year,
                month,
                date
              }
            : undefined

          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.editing,
                content: convertValueToHTMLFromSearch({
                  value: info.row?.original?.createdAt
                    ? year && typeof birthdayFull === 'object'
                      ? !!year && !month && !date
                        ? year
                        : defaultFormatDate(
                            formatDatePickerToDate({
                              year,
                              month,
                              date
                            })
                          )
                      : '-'
                    : '-',
                  searchValue
                })
              }}
              editingConfig={{
                id: String(info.row.original?.id),
                mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.date,
                placeholder: `${t('candidates:tabs:candidateOverview:profileInformation:selectTotalYearOfExp')}`,
                value: formatBirthday,
                onChange: (value, accessorKey) =>
                  onUpdateProfile({
                    [accessorKey]: value,
                    row: info.row,
                    id: Number(info.row.original?.id),
                    paramType: accessorKey
                  })
              }}
              schema={schemaUpdateProfile(t)}
              accessorKey="birthday"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.text
      },
      {
        accessorKey: 'willingToRelocate',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:tabs:candidateOverview:profileInformation:willingToRelocate')
            }}
            accessorKey="willingToRelocate"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => (
          <EditingInlineTableBody
            bodyConfig={{
              mode: ENUMS_EDITING_INLINE_MODE.editing,
              content: <></>
            }}
            editingConfig={{
              id: String(info.row.original?.id),
              mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.toggle,
              value: info?.row?.original?.permittedFields?.willingToRelocate?.value,
              onChange: (value, accessorKey) =>
                onUpdateProfile({
                  [accessorKey]: value,
                  row: info.row,
                  id: Number(info.row.original?.id),
                  paramType: accessorKey
                })
            }}
            schema={schemaUpdateProfile(t)}
            accessorKey="willingToRelocate"
          />
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.boolean
      },
      {
        accessorKey: 'preferredWorkStates',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:preferredWorkStates')
            }}
            accessorKey="preferredWorkStateIds"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          const preferredWorkStates = info?.row?.original?.permittedFields?.preferredWorkStates?.value || []
          const formatPreferredWorkStates = preferredWorkStates?.map((item: PreferredWorkStatesType) => {
            return {
              id: String(item.id),
              value: String(item.full_name),
              supportingObj: {
                name: item.full_name || ''
              }
            }
          })
          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.editing,
                content: (
                  <>
                    {preferredWorkStates?.length > 0 ? (
                      <div className="-mt-2 flex items-center">
                        <SuggestionInlineChips
                          size="sm"
                          source={preferredWorkStates?.map((item: PreferredWorkStatesType) => ({
                            label: String(item?.full_name),
                            maxLength: 25
                          }))}
                          type="default"
                        />
                      </div>
                    ) : (
                      <TypographyText className="text-sm text-gray-900">-</TypographyText>
                    )}
                  </>
                )
              }}
              editingConfig={{
                id: String(info.row.original?.id),
                mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.select,
                select: {
                  isMulti: true,
                  configSelectOption: {
                    option: 'checkbox',
                    supportingText: ['name']
                  },
                  size: 'md',
                  searchPlaceholder: `${t('label:placeholder:search')}`,
                  loadingMessage: `${t('label:loading')}`,
                  noOptionsMessage: `${t('label:noOptions')}`,
                  options: promiseCountryStateOptions,
                  onChange: () => ({})
                },
                placeholder: `${t('candidates:tabs:candidateOverview:profileInformation:addPreferredWorkStates')}`,
                value: formatPreferredWorkStates,
                onChange: (value, accessorKey) =>
                  onUpdateProfile({
                    [accessorKey]: value,
                    row: info.row,
                    id: Number(info.row.original?.id),
                    paramType: accessorKey
                  })
              }}
              schema={schemaCandidateProfilePreferredWorkStateIds(t)}
              accessorKey="preferredWorkStateIds"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.tags
      },
      {
        accessorKey: 'noticeToPeriodDays',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:tabs:candidateOverview:profileInformation:noticePeriod')
            }}
            accessorKey="noticeToPeriodDays"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          const noticeOfPeriods = info?.row?.original?.permittedFields?.noticeToPeriodDays?.value
          const formatNoticeOfPeriods = noticeOfPeriods
            ? {
                value: noticeOfPeriods,
                supportingObj: {
                  name: noticeOfPeriods
                }
              }
            : undefined

          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.editing,
                content: convertValueToHTMLFromSearch({
                  value: noticeOfPeriods || '-',
                  searchValue
                })
              }}
              editingConfig={{
                id: String(info.row.original?.id),
                mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.select,
                select: {
                  creatable: true,
                  options: ListSuggestNoticeOfPeriod.map((item) => ({
                    value: `${item.value} ${t('label:days')}`,
                    supportingObj: {
                      name: `${item.value} ${t('label:days')}`
                    }
                  })),
                  size: 'md',
                  configSelectOption: {
                    supportingText: ['name']
                  },
                  searchPlaceholder: `${t('label:placeholder:search')}`,
                  loadingMessage: `${t('label:loading')}`,
                  noOptionsMessage: `${t('label:noOptions')}`,
                  onChange: () => ({})
                },
                placeholder: `${t('candidates:tabs:candidateOverview:profileInformation:selectTimePeriod')}`,
                value: formatNoticeOfPeriods,
                onChange: (value, accessorKey) => {
                  const formatValue = value as ISelectOption
                  onUpdateProfile({
                    [accessorKey]: formatValue?.value || '',
                    row: info.row,
                    id: Number(info.row.original?.id),
                    paramType: accessorKey
                  })
                }
              }}
              schema={schemaCandidateProfileNoticeToPeriodDays(t)}
              accessorKey="noticeToPeriodDays"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.text
      },
      {
        accessorKey: 'currentSalary',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:tabs:candidateOverview:profileInformation:currentSalary')
            }}
            accessorKey="currentSalary"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          const currentSalary = info?.row?.original?.permittedFields?.currentSalary?.value
          const currentSalaryCurrency =
            info?.row?.original?.permittedFields?.currentSalaryCurrency?.value || user?.currentTenant?.currency
          const salaryDisplay = Number(currentSalary)
            ? `${numberWithCommas(Number(currentSalary))} ${currentSalaryCurrency}`
            : '-'

          const findSalary = currencySalary.find(
            (item: ISelectOption) => item.value === (currentSalaryCurrency || user?.currentTenant?.currency)
          )
          const formatCurrentSalaryCurrency = findSalary
            ? ({
                value: currentSalaryCurrency || user?.currentTenant?.currency,
                supportingObj: {
                  name: findSalary?.supportingObj?.name || ''
                }
              } as ISelectOption)
            : undefined

          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.editing,
                content: convertValueToHTMLFromSearch({
                  value: salaryDisplay,
                  searchValue
                })
              }}
              editingConfig={{
                id: String(info.row.original?.id),
                mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.inputRight,
                inputRight: {
                  isClearable: false,
                  isSearchable: false,
                  size: 'sm',
                  options: currencySalary,
                  value: formatCurrentSalaryCurrency,
                  classNameOverride: {
                    container: 'max-w-[100px]!',
                    bordered: 'none',
                    loadingMessage: `${t('label:loading')}`,
                    noOptionsMessage: `${t('label:noOptions')}`
                  },
                  onChange: () => ({})
                },
                placeholder: `${t('candidates:tabs:candidateOverview:profileInformation:typeOfCurrentSalary')}`,
                value: Number(currentSalary),
                onChange: (value, accessorKey) => {
                  const formatValue = value as ISelectOption & {
                    valueRight: ISelectOption
                  }
                  onUpdateProfile({
                    [accessorKey]: {
                      currentSalary: Number(formatValue.value),
                      currentSalaryCurrency: formatValue.valueRight?.value,
                      typeOfCurrentSalary: info?.row?.original?.permittedFields?.typeOfCurrentSalary?.value
                    },
                    row: info.row,
                    id: Number(info.row.original?.id),
                    paramType: accessorKey
                  })
                }
              }}
              schema={schemaCandidateProfileNumberDynamic(t, 'currentSalary')}
              accessorKey="currentSalary"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.text
      },
      {
        accessorKey: 'expectedSalary',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:tabs:candidateOverview:profileInformation:expectedSalary')
            }}
            accessorKey="expectedSalary"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          const expectedSalary = info?.row?.original?.permittedFields?.expectedSalary?.value
          const expectedSalaryCurrency =
            info?.row?.original?.permittedFields?.expectedSalaryCurrency?.value || user?.currentTenant?.currency
          const salaryDisplay = Number(expectedSalary)
            ? `${numberWithCommas(Number(expectedSalary))} ${expectedSalaryCurrency}`
            : '-'

          const findSalary = currencySalary.find(
            (item: ISelectOption) => item.value === (expectedSalaryCurrency || user?.currentTenant?.currency)
          )
          const formatExpectedSalaryCurrency = findSalary
            ? ({
                value: expectedSalaryCurrency || user?.currentTenant?.currency,
                supportingObj: {
                  name: findSalary?.supportingObj?.name || ''
                }
              } as ISelectOption)
            : undefined

          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.editing,
                content: convertValueToHTMLFromSearch({
                  value: salaryDisplay,
                  searchValue
                })
              }}
              editingConfig={{
                id: String(info.row.original?.id),
                mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.inputRight,
                inputRight: {
                  isClearable: false,
                  isSearchable: false,
                  size: 'sm',
                  options: currencySalary,
                  value: formatExpectedSalaryCurrency,
                  classNameOverride: {
                    container: 'max-w-[100px]!',
                    bordered: 'none',
                    loadingMessage: `${t('label:loading')}`,
                    noOptionsMessage: `${t('label:noOptions')}`
                  },
                  onChange: () => ({})
                },
                placeholder: `${t('candidates:tabs:candidateOverview:profileInformation:typeOfCurrentSalary')}`,
                value: Number(expectedSalary),
                onChange: (value, accessorKey) => {
                  const formatValue = value as ISelectOption & {
                    valueRight: ISelectOption
                  }
                  onUpdateProfile({
                    [accessorKey]: {
                      expectedSalary: Number(formatValue.value),
                      expectedSalaryCurrency: formatValue.valueRight?.value,
                      typeOfExpectedSalary: info?.row?.original?.permittedFields?.typeOfExpectedSalary?.value
                    },
                    row: info.row,
                    id: Number(info.row.original?.id),
                    paramType: accessorKey
                  })
                }
              }}
              schema={schemaCandidateProfileNumberDynamic(t, 'expectedSalary')}
              accessorKey="expectedSalary"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.text
      },
      {
        accessorKey: 'profileLevel',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:tabs:candidateOverview:profileInformation:experienceLevel')
            }}
            accessorKey="profileLevel"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          const level = profileLevel.find(
            (level: { value: string; supportingObj: { name: string } }) =>
              level.value == info?.row?.original?.permittedFields?.profileLevel?.value
          )

          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.editing,
                content: convertValueToHTMLFromSearch({
                  value: level?.supportingObj?.name || '-',
                  searchValue
                })
              }}
              editingConfig={{
                id: String(info.row.original?.id),
                mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.select,
                select: {
                  options: profileLevel,
                  size: 'md',
                  configSelectOption: {
                    supportingText: ['name']
                  },
                  searchPlaceholder: `${t('label:placeholder:search')}`,
                  loadingMessage: `${t('label:loading')}`,
                  noOptionsMessage: `${t('label:noOptions')}`,
                  onChange: () => ({})
                },
                placeholder: `${t('candidates:tabs:candidateOverview:profileInformation:selectExperienceLevel')}`,
                value: level,
                onChange: (value, accessorKey) => {
                  const formatValue = value as ISelectOption
                  onUpdateProfile({
                    [accessorKey]: formatValue?.value || null,
                    row: info.row,
                    id: Number(info.row.original?.id),
                    paramType: accessorKey
                  })
                }
              }}
              schema={schemaUpdateProfile(t)}
              accessorKey="profileLevel"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.text
      },
      {
        accessorKey: 'totalYearsOfExp',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:yearsOfExperience')
            }}
            accessorKey="totalYearsOfExp"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          const totalYearsOfExp = info?.row?.original?.permittedFields?.totalYearsOfExp?.value
          const checked = ['number', 'string'].includes(typeof totalYearsOfExp)
          const formatTotalYearOfExp = checked
            ? {
                value: String(totalYearsOfExp),
                supportingObj: {
                  name:
                    totalYoeOptions
                      .map((item) => ({
                        ...item,
                        supportingObj: {
                          name: `${t(`candidates:yoeOptions:${item.value}`)}`
                        }
                      }))
                      .find((item: ISelectOption) => item.value === String(totalYearsOfExp))?.supportingObj?.name || ''
                }
              }
            : undefined

          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.editing,
                content: convertValueToHTMLFromSearch({
                  value:
                    totalYearsOfExp !== null
                      ? transformYearsOfExperience(
                          Number(info?.row?.original?.permittedFields?.totalYearsOfExp?.value),
                          t
                        )
                      : '-',
                  searchValue
                })
              }}
              editingConfig={{
                id: String(info.row.original?.id),
                mode: ENUMS_EDITING_INLINE_MODE_COMPONENT.select,
                select: {
                  options: totalYoeOptions.map((item) => ({
                    ...item,
                    supportingObj: {
                      name: `${t(`candidates:yoeOptions:${item.value}`)}`
                    }
                  })),
                  size: 'md',
                  configSelectOption: {
                    supportingText: ['name']
                  },
                  searchPlaceholder: `${t('label:placeholder:search')}`,
                  loadingMessage: `${t('label:loading')}`,
                  noOptionsMessage: `${t('label:noOptions')}`,
                  onChange: () => ({})
                },
                placeholder: `${t('candidates:tabs:candidateOverview:profileInformation:selectTotalYearOfExp')}`,
                value: formatTotalYearOfExp,
                onChange: (value, accessorKey) => {
                  const formatValue = value as ISelectOption
                  onUpdateProfile({
                    [accessorKey]:
                      typeof formatValue?.value === 'number' || typeof formatValue?.value === 'string'
                        ? Number(formatValue.value)
                        : null,
                    row: info.row,
                    id: Number(info.row.original?.id),
                    paramType: accessorKey
                  })
                }
              }}
              schema={schemaUpdateProfile(t)}
              accessorKey="totalYearsOfExp"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.text
      },
      {
        accessorKey: 'educations',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:educations')
            }}
            accessorKey="educations"
          />
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const educations = (
            (info?.row?.original?.permittedFields?.educations?.value as unknown as string[]) || []
          ).map((item: string, index: number) => ({
            id: index,
            schoolName: item,
            profileId: index
          }))

          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.onlyView,
                content: (
                  <>
                    {educations?.length > 0 ? (
                      <div className="-mt-2 flex items-center">
                        <SuggestionInlineChips
                          size="sm"
                          source={educations?.map((item) => ({
                            label: convertValueToHTMLFromSearch({
                              value: String(item?.schoolName),
                              searchValue
                            }),
                            maxLength: 25
                          }))}
                          type="default"
                        />
                      </div>
                    ) : (
                      <TypographyText className="text-sm text-gray-900">-</TypographyText>
                    )}
                  </>
                )
              }}
              accessorKey="educations"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.tags
      },
      {
        accessorKey: 'workExperiences',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: t('candidates:candidateTable:workExperiences')
            }}
            accessorKey="workExperiences"
          />
        ),
        cell: (info: { row: { original: IProfileListType }; getValue: Function }) => {
          const workExperiences = (
            (info?.row?.original?.permittedFields?.workExperiences?.value as unknown as string[]) || []
          ).map((item: string, index: number) => ({
            id: index,
            company: item,
            profileId: index
          }))

          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.onlyView,
                content: (
                  <>
                    {workExperiences?.length > 0 ? (
                      <div className="-mt-2 flex items-center">
                        <SuggestionInlineChips
                          size="sm"
                          source={workExperiences?.map((item) => ({
                            label: convertValueToHTMLFromSearch({
                              value: String(item?.company),
                              searchValue
                            }),
                            maxLength: 25
                          }))}
                          type="default"
                        />
                      </div>
                    ) : (
                      <TypographyText className="text-sm text-gray-900">-</TypographyText>
                    )}
                  </>
                )
              }}
              accessorKey="workExperiences"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.tags
      },
      {
        accessorKey: 'createdAt',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              sortingShow: true,
              sortingDescTitle: `${t('candidates:lastActivity:desc')}`,
              sortingAscTitle: `${t('candidates:lastActivity:asc')}`,
              title: t('candidates:candidateTable:createAt'),
              sorting,
              setSorting
            }}
            accessorKey="createdAt"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => (
          <EditingInlineTableBody
            bodyConfig={{
              mode: ENUMS_EDITING_INLINE_MODE.onlyView,
              content: convertValueToHTMLFromSearch({
                value: info.row.original.createdAt ? defaultFormatDate(new Date(info.row.original.createdAt)) : '-',
                searchValue
              })
            }}
            accessorKey="createdAt"
          />
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.text
      },
      {
        accessorKey: 'lastActivity',
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              sortingShow: true,
              sortingDescTitle: `${t('candidates:lastActivity:desc')}`,
              sortingAscTitle: `${t('candidates:lastActivity:asc')}`,
              title: t('candidates:candidateTable:lastActivity'),
              sorting,
              setSorting
            }}
            accessorKey="updatedAt"
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => (
          <EditingInlineTableBody
            bodyConfig={{
              mode: ENUMS_EDITING_INLINE_MODE.onlyView,
              content: convertValueToHTMLFromSearch({
                value: info.row.original.updatedAt ? defaultFormatDate(new Date(info.row.original.updatedAt)) : '-',
                searchValue
              })
            }}
            accessorKey="updatedAt"
          />
        ),
        footer: (props: { column: { id: string } }) => props.column.id,
        size: configuration.tableColumnSize.mapping.text
      },
      ...filterConfigUserDisplay.map((mapping) => ({
        accessorKey: mapping.accessorKey,
        header: () => (
          <EditingInlineTableHeader
            headerConfig={{
              title: String(mapping.label)
            }}
            accessorKey={mapping.accessorKey}
          />
        ),
        cell: (info: { row: { original: IProfileListType; index: number }; getValue: Function }) => {
          const fieldKind = String(mapping.fieldKind)
          const customFields = info.row.original?.customFields || []
          const findCustomField = customFields?.find(
            (item) => String(item.customSettingId) === String(mapping.customSettingId)
          )
          const mergeViewData = [...(mappingsFilterProfileField || [])]
          const selectOptions = mergeViewData?.find(
            (item) => String(item.id) === String(mapping.customSettingId)
          )?.selectOptions
          const formatDate = findCustomField?.value as {
            year?: number
            month?: number
            date?: number
          }
          let fieldValue: {
            value: string | number | undefined
            valueEditing?: string | number | ISelectOption | ISelectOption[]
            className?: string
          } = { value: '' }

          let editingInlineComponent = undefined
          let renderPlaceholder = undefined
          let editingComponentConfig = {}

          if (['string', 'number'].includes(fieldKind)) {
            fieldValue = {
              value: findCustomField?.value,
              valueEditing: findCustomField?.value
            }
            editingInlineComponent =
              fieldKind === 'number'
                ? ENUMS_EDITING_INLINE_MODE_COMPONENT.number
                : ENUMS_EDITING_INLINE_MODE_COMPONENT.input
            renderPlaceholder = `${t('form:additional_field_input', {
              field_name: mapping.value
            })}`
          } else if (['array', 'multiple'].includes(fieldKind)) {
            if (fieldKind === 'multiple') {
              const raw = findCustomField?.selectedOptionKeys
              const selectedOptionKeys = !raw ? [] : findCustomField?.selectedOptionKeys
              fieldValue = {
                value: selectedOptionKeys
                  ?.map(
                    (i) => (selectOptions || []).find((item) => String(item.value) === String(i))?.supportingObj?.name
                  )
                  .join(', '),
                valueEditing: selectedOptionKeys?.map((i) =>
                  (selectOptions || []).find((item) => String(item.value) === String(i))
                ) as ISelectOption[]
              }
            } else {
              fieldValue = {
                value: (selectOptions || []).find(
                  (item) => String(item.value) === String(findCustomField?.selectedOptionKeys?.[0])
                )?.supportingObj?.name,
                valueEditing: (selectOptions || []).find(
                  (item) => String(item.value) === String(findCustomField?.selectedOptionKeys?.[0])
                )
              }
            }
            editingInlineComponent = ENUMS_EDITING_INLINE_MODE_COMPONENT.select
            renderPlaceholder = `${t('form:additional_field_select')}`
            editingComponentConfig = {
              select: {
                isMulti: fieldKind === 'multiple',
                options: selectOptions,
                size: 'md',
                configSelectOption: {
                  supportingText: ['name']
                },
                searchPlaceholder: `${t('label:placeholder:search')}`,
                loadingMessage: `${t('label:loading')}`,
                noOptionsMessage: `${t('label:noOptions')}`,
                onChange: () => ({})
              }
            }
          } else if (fieldKind === 'boolean') {
            fieldValue = {
              value: findCustomField?.value,
              valueEditing: findCustomField?.value
            }
            editingInlineComponent = ENUMS_EDITING_INLINE_MODE_COMPONENT.toggle
            renderPlaceholder = ''
          } else if (fieldKind === 'date') {
            fieldValue = {
              value:
                formatDate?.year && typeof formatDate === 'object'
                  ? !!formatDate?.year && !formatDate.month && !formatDate.date
                    ? formatDate.year
                    : defaultFormatDate(formatDatePickerToDate(formatDate))
                  : '-',
              valueEditing: formatDate as keyof unknown
            }
            editingInlineComponent = ENUMS_EDITING_INLINE_MODE_COMPONENT.date
            renderPlaceholder = `${t('form:additional_field_select')}`
          } else {
            fieldValue = {
              value: findCustomField?.value || '',
              valueEditing: findCustomField?.value || ''
            }
            editingInlineComponent = ENUMS_EDITING_INLINE_MODE_COMPONENT.richEditor
            renderPlaceholder = `${t('form:additional_field_input', {
              field_name: mapping.value
            })}`
            editingComponentConfig = {
              editor: {
                title: mapping.value,
                cancel: `${t('button:cancel')}`,
                save: `${t('button:save')}`,
                limit: 10000,
                showCount: true,
                size: 'sm'
              }
            }
          }

          return (
            <EditingInlineTableBody
              bodyConfig={{
                mode: ENUMS_EDITING_INLINE_MODE.editing,
                content: convertValueToHTMLFromSearch({
                  value:
                    fieldKind === 'text' ? removeHTMLTags(String(fieldValue?.value)) || '-' : fieldValue?.value || '-',
                  searchValue
                })
              }}
              editingConfig={{
                id: `${String(info.row.original?.id)}-${String(mapping.customSettingId)}`,
                mode: editingInlineComponent,
                ...editingComponentConfig,
                placeholder: renderPlaceholder,
                value: fieldValue?.valueEditing,
                onChange: (newValue, accessorKey) =>
                  onUpdateProfile({
                    [accessorKey]: {
                      ...formatInitialValueCustomField(customFields),
                      [`${mapping.customSettingId}-${mapping.fieldKind}`]: {
                        ...formatInitialValueCustomField(customFields)?.[
                          `${mapping.customSettingId}-${mapping.fieldKind}`
                        ],
                        value:
                          fieldKind === 'array'
                            ? (newValue as ISelectOption)?.value
                            : fieldKind === 'multiple'
                              ? (newValue as ISelectOption[])?.map((item: ISelectOption) => item.value)
                              : newValue
                      }
                    },
                    row: info.row,
                    id: Number(info.row.original?.id),
                    paramType: accessorKey
                  })
              }}
              schema={
                fieldKind === 'text'
                  ? schemaCandidateProfileRichEditorDynamic(t, 'customFields', 10000)
                  : fieldKind === 'string'
                    ? schemaCandidateProfileTextDynamic(t, 'customFields')
                    : fieldKind === 'number'
                      ? schemaCandidateProfileNumberDynamic(t, 'customFields')
                      : undefined
              }
              accessorKey="customFields"
            />
          )
        },
        footer: (props: { column: { id: string } }) => props.column.id,
        size: getCustomFieldSize(String(mapping.fieldKind))
      }))
    ]

    return defaultColumns
  }, [data, configUserDisplay, filterConfigUserDisplay, bulkValues, filter?.search])

  const LAST_KEYS = ['createdAt', 'lastActivity']
  const columnOrder = configUserDisplay
    ?.slice()
    .sort((a, b) => {
      return (LAST_KEYS.includes(a.accessorKey) ? 1 : 0) - (LAST_KEYS.includes(b.accessorKey) ? 1 : 0)
    })
    .map((item) => item.accessorKey)

  const visibleTableColumns: {
    [key: string]: boolean
  } = {
    ...((configUserDisplay
      ? visibleColumns
      : columns.map((col) => ({
          [col.accessorKey]: true
        }))) as VisibilityState),
    rowSelection: enableRowSelection
  }

  const stickyConfig = () => {
    if (enableRowSelection) {
      if (visibleTableColumns?.publicId) {
        return [
          {
            index: 0,
            position: 'left',
            value: 0,
            useShadow: true,
            accessorKey: 'rowSelection'
          },
          {
            index: 1,
            position: 'left',
            value: 40,
            useShadow: true,
            accessorKey: 'publicId'
          },
          {
            index: 2,
            position: 'left',
            value: 120,
            useShadow: true,
            accessorKey: 'fullName'
          }
        ]
      }

      return [
        {
          index: 0,
          position: 'left',
          value: 0,
          useShadow: true,
          accessorKey: 'rowSelection'
        },
        {
          index: 1,
          position: 'left',
          value: 40,
          useShadow: true,
          accessorKey: 'fullName'
        }
      ]
    }

    if (visibleTableColumns?.publicId) {
      return [
        {
          index: 0,
          position: 'left',
          value: 0,
          useShadow: true,
          accessorKey: 'publicId'
        },
        {
          index: 1,
          position: 'left',
          value: 40,
          useShadow: true,
          accessorKey: 'fullName'
        }
      ]
    }

    return [
      {
        index: 0,
        position: 'left',
        value: 0,
        useShadow: true,
        accessorKey: 'fullName'
      }
    ]
  }

  return (
    <TableEditingPagination
      tableRef={(tableEditor: any) => {
        callbackTableRef && callbackTableRef(tableEditor)
        return (tableRef.current = tableEditor)
      }}
      search={{
        globalFilter: [
          ...(!!filter?.search ? [filter?.search] : []),
          ...(filter?.fieldsFilter?.length ? (filter?.fieldsFilter || []).map((item) => item) : [])
        ].join(' ')
      }}
      textOverride={{
        of: `${t('label:of')}`,
        page: `${t('label:page')}`,
        placeholder: `${t('label:placeholder:select')}`,
        search: `${t('label:placeholder:search')}`,
        loading: `${t('label:loading')}`,
        noOptions: `${t('label:noOptions')}`,
        rowsPerPage: `${t('label:rowsPerPage')}`
      }}
      emptyConfig={{
        classNameEmpty: cn('flex h-full items-center justify-center', calcHeightScroll, classNameEmpty),
        title: `${t('candidates:candidateTable:emptyData:title')}`,
        description: `${t('candidates:candidateTable:emptyData:description')}`,
        buttonTitle: actionProfile.create ? `${t('candidates:candidateTable:emptyData:buttonAddCandidate')}` : '',
        buttonTitleOnClick: () => setOpenCreateCandidate(true),
        titleSearch: `${t('candidates:candidateTable:emptySearch:title')}`,
        descriptionSearch: `${t('candidates:candidateTable:emptySearch:description')}`,
        buttonTitleSearch: `${t('candidates:candidateTable:emptySearch:buttonClear')}`,
        buttonTitleSearchOnClick: () => clearFilter()
      }}
      tableConfig={{
        defaultPageSize: configuration.defaultPageSize,
        showRowsPerPage: true,
        paginationInsideScroll: true,
        heightOfColumn: 33
      }}
      dataQuery={{
        isFetching,
        fetcher,
        data
      }}
      columnOrder={columnOrder?.length ? ['rowSelection', ...columnOrder] : columnOrder}
      columnVisibility={visibleTableColumns}
      columns={columns.filter((col) =>
        configUserDisplay
          ? !hiddenColumns.includes(col.accessorKey) && visibleColumns[col.accessorKey] === true
          : !hiddenColumns.includes(col.accessorKey)
      )}
      isHeaderSticky
      stickyConfig={stickyConfig()}
      classNameTable={cn(
        'max-w-full border-t border-solid border-t-gray-100 bg-white',
        classNameTable,
        calcHeightScroll
      )}
    />
  )
}

export default CandidateEditingListingTable
