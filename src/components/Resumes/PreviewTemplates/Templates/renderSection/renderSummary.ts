import type { js<PERSON><PERSON> } from 'jspdf'

import { isHTML, removeHTMLTags } from '~/core/utilities/common'

import type { IResume } from '~/lib/features/settings/profiles/edit/types'
import {
  FONT_SIZE_ENUMS,
  PDF_SIZE_TITLE_ENUMS,
  PDF_VIEW_ENUMS
} from '~/lib/features/settings/profiles/edit/utilities/enum'

import type { IKeyTemplate } from '../../KeysTemplate/KeysTemplate'
import { formattedRichText } from '../utilities/formatter'
import tableRenderer from '../utilities/tableRenderer'
import { drawBorderCell, estimateRichContentHeight, renderRichContentToPDF } from '../utilities/tableRenderer'
import { TEMPLATE_JAPANESE } from '../utilities/templateConfig'
import renderTitleSection from '../utilities/titleSection'

const renderSummary = ({
  keys,
  resumeData,
  pdf,
  currentFontSize = FONT_SIZE_ENUMS.small,
  currentFont,
  startY,
  watermark
}: {
  keys: IKeyTemplate
  resumeData?: IResume
  pdf: jsPDF
  currentFontSize?: number
  currentFont: string
  startY: number
  watermark: string
}) => {
  const columnWidth = PDF_VIEW_ENUMS.width - (PDF_VIEW_ENUMS.paddingLeft + PDF_VIEW_ENUMS.paddingRight)

  // Render title
  const { yStart: yStartTitle, hTitle } = renderTitleSection({
    title: keys.summaryTitle,
    pdf,
    pdfPosition: startY + TEMPLATE_JAPANESE.sectionMargin,
    watermark,
    fontSize: Number(PDF_SIZE_TITLE_ENUMS[currentFontSize]),
    currentFont,
    hasBorderBottom: false
  })

  const yEndTitle = yStartTitle + hTitle
  let endYSection = startY
  const valueSummary = resumeData?.permittedFields?.summary?.value
  if (!valueSummary || (isHTML(valueSummary) && !removeHTMLTags(valueSummary))) {
    return yEndTitle
  }

  const rich = formattedRichText(valueSummary)

  // let bottomMarkPage = pdf.getCurrentPageInfo().pageNumber
  // let bottomMarkY = yEndTitle
  let yAfterParagraph = 0 // đáy KHUNG (sau khi mình tự vẽ border)

  tableRenderer({
    pdf,
    startY: yEndTitle - TEMPLATE_JAPANESE.sectionMargin + TEMPLATE_JAPANESE.titleSectionMargin,
    body: [[{ content: '', richContent: rich }]],
    currentFontSize,
    currentFont,
    columnWidth,
    numberOfColumn: 1,
    tableStyles: {
      lineWidth: 0,
      lineColor: [255, 255, 255]
    },

    onWillDrawCell: (data) => {
      const raw0 = data?.row?.raw?.[0]
      if (data.section !== 'body' || data.column.index !== 0 || !raw0?.richContent) return

      const usableWidth = data.cell.width - data.cell.padding('left') - data.cell.padding('right')

      const est = estimateRichContentHeight(pdf, raw0.richContent, usableWidth, currentFontSize, currentFont)

      const vpad = data.cell.padding('top') + data.cell.padding('bottom')

      data.cell.height = Math.max(28, est + vpad)
      data.cell.styles.lineWidth = 0
      data.cell.styles.lineColor = [255, 255, 255]
      data.cell.text = []
    },

    onDidDrawCell: (data) => {
      const { row, column, cell, section } = data
      const raw0 = row?.raw?.[0]
      if (section !== 'body' || column.index !== 0 || !raw0?.richContent) return

      const startX = cell.x + cell.padding('left')
      const startY = cell.y + cell.padding('top')
      const usableWidth = cell.width - cell.padding('left') - cell.padding('right')

      const { pages, lastPageNumber, lastY } = renderRichContentToPDF({
        pdf,
        blocks: raw0.richContent,
        currentFont,
        startY,
        startX,
        maxWidth: usableWidth,
        isCell: true,
        currentFontSize,
        keepoutBottom: 16,
        continuedTopPadding: 0
      })

      const marginX = PDF_VIEW_ENUMS.paddingLeft
      const rectX = marginX - 2
      const rectW = PDF_VIEW_ENUMS.widthAfterRemovePadding

      const { yEndFlow, lastPageNumber: lp } = drawBorderCell({
        pdf,
        pages,
        rectX,
        rectW,
        cellY: cell.y,
        numberOfColumn: 1,
        currentFontSize,
        lastYOfText: lastY
      })

      // bottomMarkPage = lastPageNumber // đúng trang cuối có nội dung
      // bottomMarkY = lastY // đúng y đáy trên trang cuối
      pdf.setPage(lp)
      yAfterParagraph = yEndFlow
    }
  })

  return yAfterParagraph ? yAfterParagraph : endYSection
}

export default renderSummary
