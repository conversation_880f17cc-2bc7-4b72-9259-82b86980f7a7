import type { TFunction } from 'i18next'
import type { jsPDF } from 'jspdf'

import type { ISelectOption } from '~/core/ui/Select'

import type { CertificatesType } from '~/lib/features/candidates/types'
import {
  checkCertificateFieldFormatDate,
  getCertificateFieldFormatDate
} from '~/lib/features/candidates/utilities/format'
import type { IResume, ISectionCustomFieldParamType } from '~/lib/features/settings/profiles/edit/types'
import {
  FONT_SIZE_ENUMS,
  PDF_SIZE_TITLE_ENUMS,
  PDF_VIEW_ENUMS
} from '~/lib/features/settings/profiles/edit/utilities/enum'

import type { IKeyTemplate } from '../../KeysTemplate/KeysTemplate'
import { drawTwoColHeaderWithCustomFont } from '../utilities/pdf'
import tableRenderer, {
  drawBorderCell,
  estimateRichContentHeight,
  renderRichContentToPDF
} from '../utilities/tableRenderer'
import { TEMPLATE_JAPANESE } from '../utilities/templateConfig'
import renderTitleSection from '../utilities/titleSection'

const renderCertificates = ({
  section,
  resumeData,
  pdf,
  currentFontSize = FONT_SIZE_ENUMS.small,
  currentFont,
  startY,
  dateFormatContent,
  keys,
  locale,
  extraData,
  watermark,
  t
}: {
  section: ISectionCustomFieldParamType
  resumeData?: IResume
  pdf: jsPDF
  currentFontSize?: number
  currentFont: string
  startY: number
  dateFormatContent?: ISelectOption
  keys: IKeyTemplate
  locale: string
  extraData?: {
    [key: string]: ISelectOption[]
  }
  watermark: string
  t?: TFunction
}) => {
  const firstColumnWidth = TEMPLATE_JAPANESE.firstColumnWidth
  const columnWidth =
    PDF_VIEW_ENUMS.width - (PDF_VIEW_ENUMS.paddingLeft + PDF_VIEW_ENUMS.paddingRight) - firstColumnWidth

  let tables: any[] = []
  let body: any[] = []

  // --- Title luôn render
  const { yStart: yStartTitle, hTitle } = renderTitleSection({
    title: keys.certificatesTitle,
    pdf,
    pdfPosition: startY + TEMPLATE_JAPANESE.sectionMargin,
    watermark,
    fontSize: Number(PDF_SIZE_TITLE_ENUMS[currentFontSize]),
    currentFont,
    hasBorderBottom: false
  })
  const yEndTitle = yStartTitle + hTitle

  const certificates = resumeData?.permittedFields?.certificates?.value
  if (!certificates?.length) {
    // Không có data: trả về ngay dưới title (giữ khoảng cách đúng cho section kế tiếp)
    return yEndTitle
  }

  for (const c of certificates as CertificatesType[]) {
    const showDt = checkCertificateFieldFormatDate({ attributes: c })
    const date = showDt
      ? getCertificateFieldFormatDate({
          attributes: c,
          isTemplate: true,
          dateFormatContent: dateFormatContent?.value,
          locale
        })
      : ''

    const title = `${c.certificateName || keys.undefined}${c.institution ? ` · ${c.institution}` : ''}`

    const parts = [
      {
        contentType: 'paragraph',
        content: [{ text: title, fontStyle: 'bold' }]
      }
    ]

    body.push([date, { content: '', richContent: parts }])
    tables.push(body)
    body = []
  }

  if (body.length) tables.push(body)

  // --- Header table (v5: lấy đáy header trong didDrawCell khi section === 'head')
  const headerStartY = yEndTitle - TEMPLATE_JAPANESE.sectionMargin + TEMPLATE_JAPANESE.titleSectionMargin

  // let endYHeader = yEndTitle
  const endYHeader = drawTwoColHeaderWithCustomFont({
    pdf,
    startY: yEndTitle - TEMPLATE_JAPANESE.sectionMargin + TEMPLATE_JAPANESE.titleSectionMargin,
    leftLabel: t ? t('candidates:headerTable:date') : 'Date',
    rightLabel: t ? t('candidates:headerTable:certificate') : 'Certificate - Institution',
    firstColumnWidth,
    columnWidth,
    currentFont,
    currentFontSize,
    tableRenderer
  })

  // --- Body
  let yAfterParagraph = endYHeader
  let endYSection = endYHeader

  tables.forEach((table, index) => {
    const startYTable = index === 0 ? endYHeader : yAfterParagraph

    endYSection = tableRenderer({
      pdf,
      startY: startYTable,
      body: table,
      currentFontSize,
      currentFont,
      firstColumnWidth,
      columnWidth,
      isResetY: index !== 0,
      colStyles: { col1Style: { halign: 'center' } },
      onWillDrawCell: (data) => {
        yAfterParagraph = 0
        const raw1 = data.row.raw[1]
        if (typeof raw1 === 'object' && raw1?.richContent && data.column.index === 0) {
          data.cell.styles.lineWidth = 0 // Remove border
        }
        if (typeof raw1 === 'object' && raw1?.richContent && data.column.index === 1) {
          data.cell.styles.lineWidth = 0 // Remove border
          const height = estimateRichContentHeight(
            pdf,
            raw1?.richContent,
            data.cell.width,
            currentFontSize,
            currentFont
          )
          data.cell.height = height + 8 // add padding,
        }
      },

      // Vẽ rich content thật + border đa trang
      onDidDrawCell: (data) => {
        const { row, column, cell } = data
        const raw1 = row.raw[1]
        if (typeof raw1 === 'object' && raw1?.richContent && raw1.type !== 'header' && column.index === 1) {
          // Remove border
          cell.styles.lineWidth = 0
          cell.styles.lineColor = [255, 255, 255]
          const { pages, lastY } = renderRichContentToPDF({
            pdf,
            blocks: raw1?.richContent,
            currentFont,
            startY: cell.y + cell.padding('top'),
            startX: cell.x,
            currentFontSize,
            maxWidth: cell.width - cell.padding('left') - cell.padding('right'),
            keepoutBottom: 16,
            continuedTopPadding: cell.padding('top')
          })

          const marginX = PDF_VIEW_ENUMS.paddingLeft
          const rectX = marginX - 2
          const rectW = PDF_VIEW_ENUMS.widthAfterRemovePadding
          const colSplit = marginX + TEMPLATE_JAPANESE.firstColumnWidth - 2

          const { yEndFlow, lastPageNumber: lp } = drawBorderCell({
            pdf,
            pages,
            rectX,
            rectW,
            colSplitX: colSplit,
            cellY: cell.y,
            padTop: cell.padding('top'),
            padBottom: cell.padding('bottom'),
            numberOfColumn: 2,
            currentFontSize,
            lastYOfText: lastY
          })
          pdf.setPage(lp)
          yAfterParagraph = yEndFlow
        }
      }
    })
  })

  return yAfterParagraph ? yAfterParagraph : endYSection
}

export default renderCertificates
