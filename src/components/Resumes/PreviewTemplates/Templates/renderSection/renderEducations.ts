import type { TFunction } from 'i18next'
import type { jsPDF } from 'jspdf'

import type { ISelectOption } from '~/core/ui/Select'

import type { EducationsType } from '~/lib/features/candidates/types'
import { getEducationFieldFormatDate } from '~/lib/features/candidates/utilities/format'
import type { IResume, ISectionCustomFieldParamType } from '~/lib/features/settings/profiles/edit/types'
import { getFormatDegreeEducation } from '~/lib/features/settings/profiles/edit/utilities'
import {
  FONT_SIZE_ENUMS,
  PDF_SIZE_TITLE_ENUMS,
  PDF_VIEW_ENUMS
} from '~/lib/features/settings/profiles/edit/utilities/enum'

import type { IKeyTemplate } from '../../KeysTemplate/KeysTemplate'
import { formattedRichText } from '../utilities/formatter'
import { drawTwoColHeaderWithCustomFont } from '../utilities/pdf'
import tableRenderer, {
  didDrawHeader,
  drawBorderCell,
  estimateRich<PERSON>ontentHeight,
  renderRichContentToPDF,
  willDrawCell
} from '../utilities/tableRenderer'
import { TEMPLATE_JAPANESE } from '../utilities/templateConfig'
import renderTitleSection from '../utilities/titleSection'

const renderEducations = ({
  section,
  resumeData,
  pdf,
  currentFontSize = FONT_SIZE_ENUMS.small,
  currentFont,
  startY,
  dateFormatContent,
  keys,
  locale,
  extraData,
  watermark,
  t
}: {
  section: ISectionCustomFieldParamType
  resumeData?: IResume
  pdf: jsPDF
  currentFontSize?: number
  currentFont: string
  startY: number
  dateFormatContent?: ISelectOption
  keys: IKeyTemplate
  locale: string
  extraData?: {
    [key: string]: ISelectOption[]
  }
  watermark: string
  t?: TFunction
}) => {
  const firstColumnWidth = TEMPLATE_JAPANESE.firstColumnWidth
  const columnWidth =
    PDF_VIEW_ENUMS.width - (PDF_VIEW_ENUMS.paddingLeft + PDF_VIEW_ENUMS.paddingRight) - firstColumnWidth

  let body: any = []
  let tables = []

  const { yStart: yStartTitle, hTitle } = renderTitleSection({
    title: keys.educationTitle,
    pdf,
    pdfPosition: startY + TEMPLATE_JAPANESE.sectionMargin,
    watermark,
    fontSize: Number(PDF_SIZE_TITLE_ENUMS[currentFontSize]),
    currentFont,
    hasBorderBottom: false
  })

  const yEndTitle = yStartTitle + hTitle

  const educations = resumeData?.permittedFields?.educations?.value
  if (!educations?.length) return yStartTitle

  for (let i = 0; i < educations.length; i++) {
    let bodyContent = []
    let date = ''
    const education = educations[i] as EducationsType
    const isShowDateTime =
      getEducationFieldFormatDate({
        type: 'from',
        attributes: education,
        dateFormatContent: dateFormatContent?.value || '',
        locale
      }) && section.customRelatedFields?.datetime
    const isShowDegree = education.degree && section.customRelatedFields?.degree
    const isShowDegreeSubject = education.degreeSubject && section.customRelatedFields?.major
    const isShowSchool = section.customRelatedFields?.school && education.schoolName
    const isShowDescription = education.description && section.customRelatedFields?.description

    const configShowDateTime: {
      fontSize: number
      content: string
      isChange: boolean
    } = {
      fontSize: currentFontSize,
      content: '',
      isChange: false
    }

    if (isShowDateTime) {
      date = `${getEducationFieldFormatDate({
        type: 'from',
        attributes: education,
        isTemplate: true,
        keys: {
          undefined: keys.undefined
        },
        dateFormatContent: dateFormatContent?.value || '',
        locale
      })}- ${getEducationFieldFormatDate({
        type: 'to',
        attributes: education,
        isTemplate: true,
        keys: {
          undefined: keys.undefined
        },
        dateFormatContent: dateFormatContent?.value || '',
        locale
      })}`

      // rowData = { ...rowData, date }
      // const fontSize = currentFontSize

      // Need to discuss
      // configShowDateTime = {
      //   fontSize,
      //   content,
      //   isChange: false
      // }
    }

    if (isShowDegree || isShowDegreeSubject || isShowSchool) {
      const isShow = section.customRelatedFields?.degree || section.customRelatedFields?.major
      const descriptionText = `${isShowSchool ? education.schoolName : ''}${
        isShow
          ? isShowSchool && (education.degree || education.degreeSubject)
            ? ` · ${getFormatDegreeEducation({
                currentLanguage: locale,
                degree: section.customRelatedFields?.degree ? education.degree : undefined,
                degreeSubject: section.customRelatedFields?.major ? education.degreeSubject : undefined,
                listDegrees: extraData?.educations
              })}`
            : getFormatDegreeEducation({
                currentLanguage: locale,
                degree: section.customRelatedFields?.degree ? education.degree : undefined,
                degreeSubject: section.customRelatedFields?.major ? education.degreeSubject : undefined,
                listDegrees: extraData?.educations
              })
          : ''
      }`

      const titleHTML = {
        contentType: 'paragraph',
        content: [{ text: descriptionText, fontStyle: 'bold' }]
      }

      bodyContent.push(titleHTML)

      // configShowDateTime.isChange = true
    }

    if (isShowDescription) {
      const descriptionHTML = formattedRichText(education.description)
      bodyContent = [...bodyContent, ...descriptionHTML]
    }

    body.push([
      date,
      {
        content: '',
        richContent: bodyContent
      }
    ])
    tables.push(body)
    body = []
  }

  if (body.length) tables.push(body)

  const headerStartY = yEndTitle - TEMPLATE_JAPANESE.sectionMargin + TEMPLATE_JAPANESE.titleSectionMargin
  // Render header table
  const endYHeader = drawTwoColHeaderWithCustomFont({
    pdf,
    startY: headerStartY,
    leftLabel: t ? t('candidates:headerTable:date') : 'Date',
    rightLabel: t ? t('candidates:headerTable:education') : 'Education',
    firstColumnWidth,
    columnWidth,
    currentFont,
    currentFontSize,
    tableRenderer
  })

  let yAfterParagraph = endYHeader
  let endYSection = endYHeader

  // Render body
  tables.forEach((table, index) => {
    const startYTable = index === 0 ? endYHeader : yAfterParagraph

    endYSection = tableRenderer({
      pdf,
      startY: startYTable,
      body: table,
      currentFontSize,
      currentFont,
      firstColumnWidth,
      columnWidth,
      isResetY: index !== 0,
      colStyles: {
        col1Style: {
          halign: 'center'
        }
      },
      onWillDrawCell: (data) => {
        yAfterParagraph = 0
        const raw1 = data.row.raw[1]
        if (typeof raw1 === 'object' && raw1?.richContent && data.column.index === 0) {
          data.cell.styles.lineWidth = 0 // Remove border
        }
        if (typeof raw1 === 'object' && raw1?.richContent && data.column.index === 1) {
          data.cell.styles.lineWidth = 0 // Remove border
          const height = estimateRichContentHeight(
            pdf,
            raw1?.richContent,
            data.cell.width,
            currentFontSize,
            currentFont
          )
          data.cell.height = height + 8 // add padding,
        }
      },
      onDidDrawCell: (data) => {
        const { row, column, cell } = data
        const raw1 = row.raw[1]
        if (typeof raw1 === 'object' && raw1?.richContent && raw1.type !== 'header' && column.index === 1) {
          // Remove border
          cell.styles.lineWidth = 0
          cell.styles.lineColor = [255, 255, 255]
          const { pages, lastY } = renderRichContentToPDF({
            pdf,
            blocks: raw1?.richContent,
            currentFont,
            startY: cell.y + cell.padding('top'),
            startX: cell.x,
            currentFontSize,
            maxWidth: cell.width - cell.padding('left') - cell.padding('right') - 4,
            keepoutBottom: 16,
            continuedTopPadding: cell.padding('top'),
            continuedLeftPadding: cell.padding('left')
          })

          const marginX = PDF_VIEW_ENUMS.paddingLeft
          const rectX = marginX - 2
          const rectW = PDF_VIEW_ENUMS.widthAfterRemovePadding
          const colSplit = marginX + TEMPLATE_JAPANESE.firstColumnWidth - 2

          const { yEndFlow, lastPageNumber: lp } = drawBorderCell({
            pdf,
            pages,
            rectX,
            rectW,
            colSplitX: colSplit,
            cellY: cell.y,
            padTop: cell.padding('top'),
            padBottom: cell.padding('bottom'),
            numberOfColumn: 2,
            currentFontSize,
            lastYOfText: lastY
          })
          pdf.setPage(lp)
          yAfterParagraph = yEndFlow
        }
      }
    })
  })

  return yAfterParagraph ? yAfterParagraph : endYSection
}

export default renderEducations
