import type { TFunction } from 'i18next'
import type { jsPDF } from 'jspdf'

import type { ISelectOption } from '~/core/ui/Select'

import type { CustomFieldViewType } from '~/lib/features/settings/profile-fields/types/custom-field'
import type { IResume, ISectionCustomFieldParamType } from '~/lib/features/settings/profiles/edit/types'
import {
  FONT_SIZE_ENUMS,
  PDF_SIZE_TITLE_ENUMS,
  PDF_VIEW_ENUMS
} from '~/lib/features/settings/profiles/edit/utilities/enum'

import type { IKeyTemplate } from '../../KeysTemplate/KeysTemplate'
import mappingData from '../utilities/mappingData'
import tableRenderer from '../utilities/tableRenderer'
import { drawBorderCell, estimateRichContentHeight, renderRichContentToPDF } from '../utilities/tableRenderer'
import { TEMPLATE_JAPANESE } from '../utilities/templateConfig'
import renderTitleSection from '../utilities/titleSection'

const renderProfileInformation = ({
  keys,
  section,
  resumeData,
  pdf,
  currentFontSize = FONT_SIZE_ENUMS.small,
  currentFont,
  startY,
  dateFormatContent,
  extraData,
  watermark,
  customFieldViewData,
  t
}: {
  keys: IKeyTemplate
  section: ISectionCustomFieldParamType
  resumeData?: IResume
  pdf: jsPDF
  currentFontSize?: number
  currentFont: string
  startY: number
  dateFormatContent?: ISelectOption
  extraData?: {
    [type: string]: ISelectOption[]
  }
  watermark: string
  customFieldViewData?: CustomFieldViewType[]
  t?: TFunction
}) => {
  const firstColumnWidth = TEMPLATE_JAPANESE.firstColumnWidth
  const columnWidth =
    PDF_VIEW_ENUMS.width - (PDF_VIEW_ENUMS.paddingLeft + PDF_VIEW_ENUMS.paddingRight) - firstColumnWidth

  let endYSection = 0

  // Render title
  const { yStart: yStartTitle, hTitle } = renderTitleSection({
    title: keys.profileInformationTitle,
    pdf,
    pdfPosition: startY + TEMPLATE_JAPANESE.sectionMargin,
    watermark,
    fontSize: Number(PDF_SIZE_TITLE_ENUMS[currentFontSize]),
    currentFont,
    hasBorderBottom: false
  })

  const yEndTitle = yStartTitle + hTitle

  const tables = mappingData({
    resumeData,
    section,
    customFieldViewData,
    keys,
    extraData,
    dateFormatContent,
    watermark,
    t
  })

  let yAfterParagraph = 0
  tables.forEach((table, index) => {
    const y =
      index === 0 ? yEndTitle - TEMPLATE_JAPANESE.sectionMargin + TEMPLATE_JAPANESE.titleSectionMargin : yAfterParagraph

    endYSection = tableRenderer({
      pdf,
      startY: y,
      body: table,
      currentFontSize,
      currentFont,
      firstColumnWidth,
      columnWidth,
      onWillDrawCell: (data) => {
        yAfterParagraph = 0
        const raw1 = data.row.raw[1]
        if (typeof raw1 === 'object' && raw1?.richContent && data.column.index === 0) {
          data.cell.styles.lineWidth = 0 // Remove border
        }
        if (typeof raw1 === 'object' && raw1?.richContent && data.column.index === 1) {
          data.cell.styles.lineWidth = 0 // Remove border
          const height = estimateRichContentHeight(
            pdf,
            raw1?.richContent,
            data.cell.width,
            currentFontSize,
            currentFont
          )
          data.cell.height = height + 8 // add padding,
        }
      },
      onDidDrawCell: (data) => {
        const { row, column, cell } = data
        const raw1 = row.raw[1]
        if (typeof raw1 === 'object' && raw1?.richContent && column.index === 1) {
          // Remove border
          cell.styles.lineWidth = 0
          cell.styles.lineColor = [255, 255, 255]
          const { pages, lastPageNumber, lastY } = renderRichContentToPDF({
            pdf,
            blocks: raw1?.richContent,
            currentFont,
            startY: cell.y + cell.padding('top'),
            startX: cell.x, // Use actual cell padding instead of hardcoded value
            currentFontSize,
            maxWidth: cell.width - cell.padding('left') - cell.padding('right') - 4,
            keepoutBottom: 16,
            continuedTopPadding: cell.padding('top'),
            continuedLeftPadding: cell.padding('left')
          })

          const marginX = PDF_VIEW_ENUMS.paddingLeft
          const rectX = marginX - 2
          const rectW = PDF_VIEW_ENUMS.widthAfterRemovePadding
          const colSplit = marginX + TEMPLATE_JAPANESE.firstColumnWidth - 2

          const { yEndFlow, lastPageNumber: lp } = drawBorderCell({
            pdf,
            pages,
            rectX,
            rectW,
            colSplitX: colSplit,
            cellY: cell.y,
            padTop: cell.padding('top'),
            padBottom: cell.padding('bottom'),
            numberOfColumn: 2,
            currentFontSize,
            lastYOfText: lastY
          })

          pdf.setPage(lp)
          yAfterParagraph = yEndFlow
        }
      }
    })
  })

  return yAfterParagraph ? yAfterParagraph : endYSection
}

export default renderProfileInformation
