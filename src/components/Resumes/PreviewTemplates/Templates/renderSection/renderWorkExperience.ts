import type { TFunction } from 'i18next'
import type { jsPDF } from 'jspdf'

import type { ISelectOption } from '~/core/ui/Select'

import type { WorkExperiencesType } from '~/lib/features/candidates/types'
import { getWorkExpFieldFormatDate } from '~/lib/features/candidates/utilities/format'
import type { IResume, ISectionCustomFieldParamType } from '~/lib/features/settings/profiles/edit/types'
import {
  FONT_SIZE_ENUMS,
  PDF_SIZE_TITLE_ENUMS,
  PDF_VIEW_ENUMS
} from '~/lib/features/settings/profiles/edit/utilities/enum'

import type { IKeyTemplate } from '../../KeysTemplate/KeysTemplate'
import { formattedRichText } from '../utilities/formatter'
import { drawTwoColHeaderWithCustomFont } from '../utilities/pdf'
import tableRenderer, {
  drawBorderCell,
  estimateRichContentHeight,
  renderRichContentToPDF
} from '../utilities/tableRenderer'
import { TEMPLATE_JAPANESE } from '../utilities/templateConfig'
import renderTitleSection from '../utilities/titleSection'

const renderWorkExperience = ({
  section,
  resumeData,
  pdf,
  currentFontSize = FONT_SIZE_ENUMS.small,
  currentFont,
  startY,
  dateFormatContent,
  keys,
  locale,
  extraData,
  watermark,
  t
}: {
  section: ISectionCustomFieldParamType
  resumeData?: IResume
  pdf: jsPDF
  currentFontSize?: number
  currentFont: string
  startY: number
  dateFormatContent?: ISelectOption
  keys: IKeyTemplate
  locale?: string
  extraData?: {
    [key: string]: ISelectOption[]
  }
  watermark: string
  t?: TFunction
}) => {
  const firstColumnWidth = TEMPLATE_JAPANESE.firstColumnWidth
  const columnWidth =
    PDF_VIEW_ENUMS.width - (PDF_VIEW_ENUMS.paddingLeft + PDF_VIEW_ENUMS.paddingRight) - firstColumnWidth

  let body: any = []
  const tables = []
  const workExperiences = resumeData?.permittedFields?.workExperiences?.value

  const { yStart: yStartTitle, hTitle } = renderTitleSection({
    title: keys.workExperiencesTitle,
    pdf,
    pdfPosition: startY + TEMPLATE_JAPANESE.sectionMargin,
    watermark,
    fontSize: Number(PDF_SIZE_TITLE_ENUMS[currentFontSize]),
    currentFont,
    hasBorderBottom: false
  })

  const yEndTitle = yStartTitle + hTitle

  if (!workExperiences?.length) return yStartTitle

  for (let i = 0; i < workExperiences.length; i++) {
    let bodyContent = []
    const workExperience = workExperiences[i] as WorkExperiencesType
    let date = ''

    const isShowDateTime =
      getWorkExpFieldFormatDate({
        type: 'from',
        attributes: workExperience,
        isTemplate: true,
        dateFormatContent: dateFormatContent?.value,
        locale
      }) && section.customRelatedFields?.datetime
    const isShowTitle = workExperience.title && section.customRelatedFields?.title
    const isShowCompany = workExperience.company && section.customRelatedFields?.company
    const isShowLocation = workExperience.location && section.customRelatedFields?.location
    const isShowDescription = workExperience.description && section.customRelatedFields?.description

    const configShowDateTime: {
      fontSize: number
      content: string
      isChange: boolean
    } = {
      fontSize: currentFontSize,
      content: '',
      isChange: false
    }
    if (isShowDateTime) {
      date = `${getWorkExpFieldFormatDate({
        type: 'from',
        attributes: workExperience,
        isTemplate: true,
        dateFormatContent: dateFormatContent?.value,
        keys: {
          undefined: keys.undefined
        },
        locale
      })}${
        workExperience?.currentWorking
          ? `- ${keys.present}`
          : `- ${getWorkExpFieldFormatDate({
              type: 'to',
              attributes: workExperience,
              isTemplate: true,
              dateFormatContent: dateFormatContent?.value,
              keys: {
                undefined: keys.undefined
              },
              locale
            })}`
      }`
      // const fontSize = currentFontSize

      // configShowDateTime = {
      //   fontSize,
      //   content,
      //   isChange: false
      // }
    }

    if (isShowTitle || isShowCompany) {
      const titleText = `${isShowTitle ? workExperience.title : ''}${
        section.customRelatedFields?.company
          ? isShowTitle && workExperience.company
            ? ` · ${workExperience.company}`
            : workExperience.company
          : ''
      }`

      const titleHTML = {
        contentType: 'paragraph',
        content: [{ text: titleText, fontStyle: 'bold' }]
      }

      bodyContent.push(titleHTML)
      configShowDateTime.isChange = true
    }

    if (isShowLocation) {
      const locationText =
        typeof workExperience.location === 'object'
          ? // @ts-expect-error
            workExperience.location?.value
          : workExperience.location

      const locationHTML = {
        contentType: 'paragraph',
        content: [{ text: locationText }]
      }

      bodyContent.push(locationHTML)
      configShowDateTime.isChange = true
    }

    if (isShowDescription) {
      const descriptionHTML = formattedRichText(workExperience.description)
      bodyContent = [...bodyContent, ...descriptionHTML]
    }

    body.push([
      date,
      {
        content: '',
        richContent: bodyContent
      }
    ])

    tables.push(body)
    body = []
  }

  if (body.length) tables.push(body)

  const headerStartY = yEndTitle - TEMPLATE_JAPANESE.sectionMargin + TEMPLATE_JAPANESE.titleSectionMargin
  // Render header table
  // --- Header (dùng head thay vì body)
  // let endYHeader = yEndTitle
  const endYHeader = drawTwoColHeaderWithCustomFont({
    pdf,
    startY: headerStartY,
    leftLabel: t ? t('candidates:headerTable:date') : 'Date',
    rightLabel: t ? t('candidates:headerTable:placeOfEmployment') : 'Place of employment and job details',
    firstColumnWidth,
    columnWidth,
    currentFont,
    currentFontSize,
    tableRenderer // truyền wrapper autoTable bạn đã viết
  })

  let yAfterParagraph = endYHeader
  let endYSection = endYHeader
  // Render body
  tables.forEach((table, index) => {
    const y = index === 0 ? endYHeader : yAfterParagraph
    endYSection = tableRenderer({
      pdf,
      startY: y,
      body: table,
      currentFontSize,
      currentFont,
      firstColumnWidth,
      columnWidth,
      isResetY: index !== 0,
      colStyles: {
        col1Style: {
          halign: 'center'
        }
      },
      onWillDrawCell: (data) => {
        yAfterParagraph = 0
        const raw1 = data.row.raw[1]
        if (typeof raw1 === 'object' && raw1?.richContent && data.column.index === 0) {
          data.cell.styles.lineWidth = 0 // Remove border
        }
        if (typeof raw1 === 'object' && raw1?.richContent && data.column.index === 1) {
          data.cell.styles.lineWidth = 0 // Remove border
          const height = estimateRichContentHeight(
            pdf,
            raw1?.richContent,
            data.cell.width,
            currentFontSize,
            currentFont
          )
          data.cell.height = height + 8 // add padding,
        }
      },
      onDidDrawCell: (data) => {
        const { row, column, cell } = data
        const raw1 = row.raw[1]
        if (typeof raw1 === 'object' && raw1?.richContent && column.index === 1) {
          const x = cell.x
          // Remove border
          cell.styles.lineWidth = 0
          cell.styles.lineColor = [255, 255, 255]
          const { pages, lastY } = renderRichContentToPDF({
            pdf,
            blocks: raw1?.richContent,
            currentFont,
            startY: cell.y + cell.padding('top'),
            startX: x, // Use actual cell padding instead of hardcoded value
            currentFontSize,
            maxWidth: cell.width - cell.padding('left') - cell.padding('right'), // Small buffer to prevent overflow
            keepoutBottom: 0,
            continuedTopPadding: 0,
            continuedLeftPadding: cell.padding('left')
          })

          const marginX = PDF_VIEW_ENUMS.paddingLeft
          const rectX = marginX - 2
          const rectW = PDF_VIEW_ENUMS.widthAfterRemovePadding
          const colSplit = marginX + TEMPLATE_JAPANESE.firstColumnWidth - 2

          const { yEndFlow, lastPageNumber: lp } = drawBorderCell({
            pdf,
            pages,
            rectX,
            rectW,
            colSplitX: colSplit,
            cellY: cell.y,
            padTop: cell.padding('top'),
            padBottom: cell.padding('bottom'),
            numberOfColumn: 2,
            currentFontSize,
            lastYOfText: lastY
          })

          pdf.setPage(lp)
          yAfterParagraph = yEndFlow
        }
      }
    })
  })

  return yAfterParagraph ? yAfterParagraph : endYSection
}

export default renderWorkExperience
