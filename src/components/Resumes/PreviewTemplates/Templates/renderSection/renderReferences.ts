import type { TFunction } from 'i18next'
import type { jsPDF } from 'jspdf'

import type { ISelectOption } from '~/core/ui/Select'

import type { ReferencesType } from '~/lib/features/candidates/types'
import type { IResume, ISectionCustomFieldParamType } from '~/lib/features/settings/profiles/edit/types'
import {
  FONT_SIZE_ENUMS,
  PDF_SIZE_TITLE_ENUMS,
  PDF_VIEW_ENUMS
} from '~/lib/features/settings/profiles/edit/utilities/enum'

import type { IKeyTemplate } from '../../KeysTemplate/KeysTemplate'
import { drawTwoColHeaderWithCustomFont } from '../utilities/pdf'
import tableRenderer from '../utilities/tableRenderer'
import { TEMPLATE_JAPANESE } from '../utilities/templateConfig'
import renderTitleSection from '../utilities/titleSection'

const renderReferences = ({
  section,
  resumeData,
  pdf,
  currentFontSize = FONT_SIZE_ENUMS.small,
  currentFont,
  startY,
  dateFormatContent,
  keys,
  locale,
  extraData,
  watermark,
  t
}: {
  section: ISectionCustomFieldParamType
  resumeData?: IResume
  pdf: jsPDF
  currentFontSize?: number
  currentFont: string
  startY: number
  dateFormatContent?: ISelectOption
  keys: IKeyTemplate
  locale: string
  extraData?: {
    [key: string]: ISelectOption[]
  }
  watermark: string
  t?: TFunction
}) => {
  const firstColumnWidth = TEMPLATE_JAPANESE.firstColumnWidth
  const columnWidth =
    PDF_VIEW_ENUMS.width - (PDF_VIEW_ENUMS.paddingLeft + PDF_VIEW_ENUMS.paddingRight) - firstColumnWidth

  let body: any = []

  const { yStart: yStartTitle, hTitle } = renderTitleSection({
    title: keys.referencesTitle,
    pdf,
    pdfPosition: startY + TEMPLATE_JAPANESE.sectionMargin,
    watermark,
    fontSize: Number(PDF_SIZE_TITLE_ENUMS[currentFontSize]),
    currentFont,
    hasBorderBottom: false
  })

  const references = resumeData?.permittedFields?.references?.value
  if (!references?.length) return yStartTitle

  let yEndTitle = yStartTitle + hTitle

  for (let i = 0; i < references.length; i++) {
    const certificate = references[i] as ReferencesType
    let rowData = { name: '', email: '' }
    const isShowContent = certificate.name || certificate.email

    if (isShowContent) {
      const name = certificate.name || keys.undefined

      rowData = { ...rowData, name }

      if (certificate.email) {
        rowData = { ...rowData, email: certificate.email }
      }
    }

    body.push([rowData.name, rowData.email])
  }
  // Render header table
  const headerStartY = yEndTitle - TEMPLATE_JAPANESE.sectionMargin + TEMPLATE_JAPANESE.titleSectionMargin

  const endYHeader = drawTwoColHeaderWithCustomFont({
    pdf,
    startY: headerStartY,
    leftLabel: t ? t('candidates:headerTable:name') : 'Name',
    rightLabel: t ? t('candidates:headerTable:email') : 'Email',
    firstColumnWidth,
    columnWidth,
    currentFont,
    currentFontSize,
    tableRenderer
  })

  return tableRenderer({
    pdf,
    startY: endYHeader,
    body,
    currentFontSize,
    currentFont,
    firstColumnWidth,
    columnWidth
  })
}

export default renderReferences
