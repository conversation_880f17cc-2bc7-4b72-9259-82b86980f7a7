import type { js<PERSON><PERSON> } from 'jspdf'

import { FONT_SIZE_ENUMS, PDF_SIZE_TITLE_ENUMS } from '~/lib/features/settings/profiles/edit/utilities/enum'
import { PDF_LINE_HEIGHT_ENUMS, PDF_VIEW_ENUMS } from '~/lib/features/settings/profiles/edit/utilities/enum'

import { TEMPLATE_JAPANESE } from '../utilities/templateConfig'

/** Ước lượng tối thiểu để section kế tiếp hiển thị trọn phần mở đầu */
export function minHeadroomForNextSection(fs: number): number {
  const title = PDF_SIZE_TITLE_ENUMS[fs] || PDF_SIZE_TITLE_ENUMS[FONT_SIZE_ENUMS.small] || 16
  return (
    TEMPLATE_JAPANESE.sectionMargin + title + TEMPLATE_JAPANESE.titleSectionMargin + 28 // minCellHeight của header autotable
  )
}

type DrawHeaderParams = {
  pdf: jsPDF
  startY: number
  leftLabel: string
  rightLabel: string
  firstColumnWidth: number
  columnWidth: number
  currentFont: string
  currentFontSize: number
  tableRenderer: Function // truyền wrapper autoTable của bạn vào
}

export function drawTwoColHeaderWithCustomFont({
  pdf,
  startY,
  leftLabel,
  rightLabel,
  firstColumnWidth,
  columnWidth,
  currentFont,
  currentFontSize,
  tableRenderer
}: DrawHeaderParams): number {
  const cellPad = PDF_VIEW_ENUMS.cellPadding
  const minH = 28
  const lh = PDF_LINE_HEIGHT_ENUMS[currentFontSize] || PDF_LINE_HEIGHT_ENUMS[FONT_SIZE_ENUMS.small] || 18

  let endYHeader = startY

  tableRenderer({
    pdf,
    startY,
    // 1 hàng body “giả header” (để tự control font & canh giữa)
    body: [[{ content: '__HDR__' }, { content: '__HDR__' }]],
    currentFontSize,
    currentFont,
    firstColumnWidth,
    columnWidth,
    numberOfColumn: 2,
    tableStyles: {
      lineWidth: 0, // tắt grid của AutoTable (tự vẽ)
      lineColor: [255, 255, 255],
      minCellHeight: minH,
      valign: 'middle' // đề phòng
    },

    // Ép cell header đủ cao + không vẽ text mặc định
    onWillDrawCell: (d: any) => {
      if (d.section !== 'body') return
      d.cell.height = Math.max(minH, lh + cellPad * 2)
      d.cell.styles.lineWidth = 0
      d.cell.styles.lineColor = [255, 255, 255]
      d.cell.text = [] // chặn AutoTable vẽ text
    },

    // Vẽ border + vẽ nhãn vào tâm ô (center/middle)
    onDidDrawCell: (d: any) => {
      if (d.section !== 'body') return

      // Vẽ border ngoài & vạch giữa (chỉ khi cột phải xong)
      if (d.column.index === 1) {
        const y = d.cell.y
        const h = d.cell.height
        const marginX = PDF_VIEW_ENUMS.paddingLeft

        pdf.setDrawColor(12, 12, 12)
        pdf.setLineWidth(0.5)

        // Border outside
        pdf.rect(marginX - 2, y, PDF_VIEW_ENUMS.widthAfterRemovePadding, h, 'S')

        // line center firstColumnWidth
        pdf.line(marginX + firstColumnWidth - 2, y, marginX + firstColumnWidth - 2, y + h)

        endYHeader = Math.max(endYHeader, y + h)
      }

      // Vẽ text vào tâm ô (center/middle)
      const label = d.column.index === 0 ? leftLabel : rightLabel
      const xCenter = d.cell.x + d.cell.width / 2
      const yCenter = d.cell.y + d.cell.height / 2

      pdf.setFont(currentFont, 'medium') // mapping bold -> medium
      pdf.setFontSize(currentFontSize)
      pdf.setTextColor('#0C0C0C')

      pdf.text(label, xCenter, yCenter, { align: 'center', baseline: 'middle' })
    }
  })

  return endYHeader
}
