import type { jsPD<PERSON> } from 'jspdf'
import autoTable from 'jspdf-autotable'

import {
  DEFAULT_FONT,
  DEFAULT_FONT_SIZE_HEADING,
  PDF_HEADING_SIZE_ENUMS,
  PDF_LINE_HEIGHT_ENUMS,
  PDF_VIEW_ENUMS
} from '~/lib/features/settings/profiles/edit/utilities/enum'

import { rgbaToHex } from '../utilities/formatter'
import { TEMPLATE_JAPANESE } from './templateConfig'

type PageMark = { page: number; yStart: number; yEnd: number }

type RenderTableParams = {
  pdf: jsPDF
  startY: number
  body: any
  currentFontSize: number
  firstColumnWidth?: number
  columnWidth: number
  currentFont: string
  numberOfColumn?: number
  header?: { values: Array<any>; styles: any }
  colStyles?: {
    col1Style?: any
    col2Style?: any
  }
  tableStyles?: any
  onDidDrawCell?: (data: any) => void
  onDidDrawPage?: (data: any) => void
  onWillDrawCell?: (data: any) => void
  title?: { yStart: number; hTitle: number }
  config?: any
  isResetY?: boolean
}

type RichBlock = {
  contentType: 'heading' | 'paragraph' | 'bulletList' | 'orderedList'
  level?: 1 | 2 | 3 // for heading
  content: RichTextSpan[]
  subs?: [any]
}

type RichTextSpan = {
  text: string
  fontStyle: string
  color?: string
  href?: string
}

const getFontStyle = (style?: string) => {
  if (!style) return 'normal'
  if (style?.includes('bold')) return 'medium'
  if (style?.includes('italic')) return 'italic'
  if (style?.includes('underline')) return 'normal'
  return 'normal'
}

const countPreviousOrdered = (blocks: RichBlock[], currentIndex: number): number => {
  let count = 1
  for (let i = 0; i < currentIndex; i++) {
    if (blocks[i]?.contentType === 'orderedList') count++
  }
  return count
}

export const renderRichContentToPDF = ({
  pdf,
  blocks,
  startY,
  startX,
  currentFont,
  maxWidth,
  isCell = true,
  currentFontSize,
  keepoutBottom = 16,
  continuedTopPadding = 0,
  continuedLeftPadding = 0
}: {
  pdf: jsPDF
  blocks: RichBlock[]
  startY: number
  startX: number
  currentFont: string
  maxWidth: number
  isCell?: boolean
  currentFontSize: number
  keepoutBottom?: number
  continuedTopPadding?: number
  continuedLeftPadding?: number
}) => {
  let y = startY
  const pageH = pdf.internal.pageSize.height
  const bottom = PDF_VIEW_ENUMS.paddingBottom
  const bulletIndent = PDF_VIEW_ENUMS.bulletIndent
  const xRightAbs = startX + maxWidth
  let xText = 0

  const pages: PageMark[] = []
  const touch = () => {
    const cur = pdf.getCurrentPageInfo().pageNumber
    const idx = pages.findIndex((p) => p.page === cur)
    if (idx === -1) pages.push({ page: cur, yStart: y, yEnd: y })
    else if (pages[idx]) pages[idx].yEnd = y
  }
  touch()

  const pickFontStyle = (style?: string) =>
    !style ? 'normal' : style.includes('bold') ? 'medium' : style.includes('italic') ? 'italic' : 'normal'

  const drawSpan = (span: RichTextSpan, isHeading: boolean, fsHeading: number) => {
    const fs = isHeading ? fsHeading : currentFontSize
    pdf.setFont(currentFont, pickFontStyle(span.fontStyle))
    pdf.setFontSize(fs)
    pdf.setTextColor(span.color ? rgbaToHex(span.color) : '#000000')
    return fs
  }

  const shouldPreBreak = (lh: number) => y + lh > pageH - bottom - keepoutBottom

  const breakIfNeeded = (lh: number, cont?: () => void) => {
    if (!isCell) return cont?.()
    if (y + lh > pageH - bottom || shouldPreBreak(lh)) {
      touch()
      pdf.addPage()
      y = PDF_VIEW_ENUMS.paddingTopForNewPage + continuedTopPadding
      xText = startX + continuedLeftPadding
      touch()
    }
    cont?.()
  }

  const renderText = (
    text: string,
    fs: number,
    x: number,
    yTop: number,
    underline: boolean,
    isLink: boolean,
    lh: number
  ) => {
    pdf.setFontSize(fs)
    if (isLink) {
      pdf.textWithLink(x === startX ? text.trim() : text, x, yTop, {
        url: text,
        align: 'left',
        baseline: 'top'
      })
    } else {
      pdf.text(x === startX ? text.trim() : text, x, yTop, {
        align: 'left',
        baseline: 'top'
      })
    }
    const w = pdf.getTextDimensions(text, { fontSize: fs }).w
    if (underline) pdf.line(x, yTop + lh, x + w, yTop + lh)
    return w
  }

  const lineH = (fs: number) =>
    (PDF_LINE_HEIGHT_ENUMS?.[fs] || PDF_LINE_HEIGHT_ENUMS?.[12] || 1) * (TEMPLATE_JAPANESE?.lineHeight ?? 1)

  const renderParagraph = (span: RichTextSpan, lh: number, x: number, startPL: number, fs: number) => {
    const isLink = !!span.href
    const underline = String(span.fontStyle).includes('underline')
    const txt = span.text || ''
    const wAll = pdf.getTextDimensions(txt, { fontSize: fs }).w

    breakIfNeeded(lh)

    if (xText + wAll > xRightAbs) {
      const wRemain = xRightAbs - xText
      const [first] = pdf.splitTextToSize(txt, wRemain)
      renderText(first, fs, xText, y, underline, isLink, lh)

      xText = x // break line

      const rest = txt.slice(first.length)
      if (rest) {
        const wForRest = xRightAbs - (x + startPL)
        const lines = pdf.splitTextToSize(rest, wForRest)
        for (let i = 0; i < lines.length; i++) {
          breakIfNeeded(lh, () => (y += lh))
          const w = renderText(lines[i], fs, xText, y, underline, isLink, lh)
          if (i === lines.length - 1) xText += w
        }
      }
    } else {
      const w = renderText(txt, fs, xText, y, underline, isLink, lh)
      xText += w
    }
  }

  blocks.forEach((block, idx) => {
    const isHeading = block.contentType === 'heading'
    const xBase = startX + (block.contentType === 'paragraph' ? 0 : bulletIndent)
    let xWithList = 0

    if (block.contentType === 'bulletList') {
      xWithList = PDF_VIEW_ENUMS.cellPadding * 2
      pdf.setFont(currentFont, 'normal')
      pdf.setFontSize(currentFontSize)
      pdf.setTextColor('#000000')
      pdf.text(PDF_VIEW_ENUMS.bulletSymbol, xBase + PDF_VIEW_ENUMS.cellPadding, y + PDF_VIEW_ENUMS.cellPadding + 1)
    } else if (block.contentType === 'orderedList') {
      const orderIdx = 1 + blocks.slice(0, idx).filter((b) => b.contentType === 'orderedList').length
      xWithList = PDF_VIEW_ENUMS.cellPadding * 2
      pdf.setFont(currentFont, 'normal')
      pdf.setFontSize(currentFontSize)
      pdf.setTextColor('#000000')
      pdf.text(`${orderIdx}. `, xBase + PDF_VIEW_ENUMS.cellPadding, y + PDF_VIEW_ENUMS.cellPadding + 2)
    }

    const fsHeading =
      PDF_HEADING_SIZE_ENUMS[
        (Number(block.level) > 3 ? DEFAULT_FONT_SIZE_HEADING : Number(block.level)) as 1 | 2 | 3
      ] || 1
    const lhPara = lineH(currentFontSize)
    const lhHead = lineH(fsHeading)

    xText = xBase + PDF_VIEW_ENUMS.cellPadding + xWithList

    for (const span of block.content || []) {
      const fs = drawSpan(span, isHeading, fsHeading)
      renderParagraph(
        span,
        isHeading ? lhHead : lhPara,
        xBase + PDF_VIEW_ENUMS.cellPadding + xWithList,
        block.contentType === 'paragraph' ? 0 : 30,
        fs
      )
      touch()
    }

    y += isHeading ? lhPara + lhHead : lhPara
    touch()

    if (idx < blocks.length - 1) {
      breakIfNeeded(isHeading ? lhPara + lhHead : lhPara)
    }
  })

  const lastPage = pages[pages.length - 1]
  return {
    pages,
    lastPageNumber: lastPage?.page ?? pdf.getCurrentPageInfo().pageNumber,
    lastY: lastPage?.yEnd ?? y
  }
}

export const willDrawCell = ({
  data,
  pdf,
  currentFont,
  currentFontSize
}: {
  data: any
  pdf: jsPDF
  currentFont: string
  currentFontSize: number
}) => {
  const raw1 = data.row.raw[1]
  if (typeof raw1 === 'object' && raw1?.richContent && data.column.index === 0) {
    data.cell.styles.lineWidth = 0 // Remove border
  }
  if (typeof raw1 === 'object' && raw1?.richContent && data.column.index === 1) {
    data.cell.styles.lineWidth = 0 // Remove border
    const height = estimateRichContentHeight(pdf, raw1?.richContent, data.cell.width, currentFontSize, currentFont)
    data.cell.height = height + 8 // add padding,
  }
}

export const didDrawHeader = ({
  data,
  pdf,
  currentFont,
  currentFontSize
}: {
  data: any
  pdf: jsPDF
  currentFont: string
  currentFontSize: number
}) => {
  const { row, cell, column } = data
  const type = row.raw[column.index].type
  const richContent = row.raw[column.index]?.richContent
  if (type !== 'header' || !richContent) return

  let yEnd = cell.y
  pdf.setFont(currentFont, 'medium')
  pdf.setFontSize(currentFontSize)

  // Get proper content based on column
  const richBlock = row.raw[column.index]?.richContent

  const { pages } = renderRichContentToPDF({
    pdf,
    blocks: richBlock,
    currentFont,
    startY: cell.y + cell.padding('top'),
    startX: cell.x + cell.padding('left'),
    maxWidth: cell.width - cell.padding('left') - cell.padding('right'),
    isCell: true,
    currentFontSize,
    keepoutBottom: 16,
    continuedTopPadding: cell.padding('top')
  })

  const marginX = PDF_VIEW_ENUMS.paddingLeft
  pages.forEach((page) => {
    const { page: numberOfPage } = page
    pdf.setPage(numberOfPage)
    // Set font again (break new page)
    pdf.setFont(currentFont, 'medium')
    pdf.setDrawColor(0)
    pdf.setLineWidth(0.5)
    pdf.rect(
      marginX - 2,
      cell.y,
      PDF_VIEW_ENUMS.widthAfterRemovePadding,
      28 //endY - (yStart + hTitle)
    )
    pdf.line(marginX + 130, cell.y, marginX + 130, cell.y + 28)
    yEnd = cell.y + 28
  })

  return yEnd
}

export const estimateRichContentHeight = (
  doc: jsPDF,
  blocks: Array<any>,
  maxWidth: number,
  currentFontSize: number,
  currentFont: string
) => {
  let y = 0
  blocks.forEach((block) => {
    let x = 0
    let fontSize = currentFontSize
    if (block.type === 'heading') {
      fontSize = Number(
        PDF_HEADING_SIZE_ENUMS[Number(block.level) > 3 ? DEFAULT_FONT_SIZE_HEADING : Number(block.level)]
      )
    }

    block.content.forEach((span: any) => {
      const fontStyle = getFontStyle(span.fontStyle)
      doc.setFont(currentFont, fontStyle)
      doc.setFontSize(fontSize)

      const text = span.text.replace(/\n/g, ' \n ')
      const words = text.split(' ')
      for (const word of words) {
        const wordWidth = doc.getTextWidth(word + ' ')
        if (x + wordWidth > maxWidth) {
          y += PDF_VIEW_ENUMS.lineHeight
          x = 0
        }
        x += wordWidth
      }
    })

    y += PDF_VIEW_ENUMS.lineHeight
  })

  return y
}

export const drawBorderCell = ({
  pdf,
  pages,
  rectX,
  rectW,
  colSplitX, // If numberOfColumn===2
  cellY,
  padTop = 0,
  padBottom = 0,
  numberOfColumn = 2,
  currentFontSize,
  lastYOfText, // lastY thật từ renderRichContentToPDF (trang cuối)
  extendAtBottomPrevPages = true,
  extendAtBottomLastPage = true
}: {
  pdf: jsPDF
  pages: PageMark[]
  rectX: number
  rectW: number
  colSplitX?: number
  cellY: number
  padTop?: number
  padBottom?: number
  numberOfColumn?: number
  currentFontSize: number
  lastYOfText?: number
  extendAtBottomPrevPages?: boolean
  extendAtBottomLastPage?: boolean
}) => {
  const lh = (PDF_LINE_HEIGHT_ENUMS[currentFontSize] || PDF_LINE_HEIGHT_ENUMS[12] || 1) * TEMPLATE_JAPANESE.lineHeight
  const minCell = 28

  let yEndFrameOnLast = cellY
  let yEndFlowOnLast = cellY
  let lastPageNumber = pages[pages.length - 1]?.page ?? pdf.getCurrentPageInfo().pageNumber

  pages.forEach((p, idx) => {
    const isLast = idx === pages.length - 1

    // Đỉnh khung của trang hiện tại
    const yTop =
      idx === 0
        ? cellY
        : typeof p.yStart === 'number'
          ? p.yStart - padTop
          : PDF_VIEW_ENUMS.paddingTopForNewPage + padTop

    // Đáy nội dung: trang cuối ưu tiên lastY + padBottom; còn lại dùng yEnd
    const rawBottom = isLast && typeof lastYOfText === 'number' ? lastYOfText + padBottom : p.yEnd

    // Không thấp hơn minCell
    const contentBottom = Math.max(yTop + minCell, rawBottom)

    // Nếu quá sát đáy trang, nới khung cho đẹp (không ảnh hưởng flow)
    const nearBottom =
      contentBottom + lh + PDF_VIEW_ENUMS.paddingBottom + PDF_VIEW_ENUMS.sectionMargin >= PDF_VIEW_ENUMS.height

    const doExtend = isLast ? extendAtBottomLastPage : extendAtBottomPrevPages
    const frameBottom = nearBottom && doExtend ? contentBottom + lh : contentBottom

    // Vẽ khung
    pdf.setPage(p.page)
    pdf.setDrawColor(12, 12, 12)
    pdf.setLineWidth(0.5)
    pdf.rect(rectX, yTop, rectW, frameBottom - yTop, 'S')

    // Vạch giữa nếu là bảng 2 cột
    if (numberOfColumn === 2 && typeof colSplitX === 'number') {
      pdf.line(colSplitX, yTop, colSplitX, frameBottom)
    }

    if (isLast) {
      yEndFrameOnLast = frameBottom
      yEndFlowOnLast = contentBottom
      lastPageNumber = p.page
    }
  })

  return {
    yEndFrame: yEndFrameOnLast, // đáy KHUNG (chỉ để tham khảo)
    yEndFlow: yEndFlowOnLast, // đáy FLOW → dùng set y cho section kế tiếp
    lastPageNumber
  }
}

const tableRenderer = ({
  pdf,
  startY,
  body,
  currentFontSize,
  firstColumnWidth = 0,
  columnWidth,
  currentFont,
  numberOfColumn = 2,
  header,
  colStyles,
  tableStyles,
  config,
  onDidDrawCell,
  onDidDrawPage,
  onWillDrawCell
}: RenderTableParams) => {
  autoTable(pdf, {
    startY,
    theme: 'grid',
    ...(header?.values ? { head: header.values, showHead: 'firstPage' } : { showHead: 'never' }),
    body,
    styles: {
      fontSize: currentFontSize,
      cellPadding: PDF_VIEW_ENUMS.cellPadding,
      lineColor: '#0C0C0C',
      valign: 'top',
      lineWidth: 0.5,
      fillColor: false,
      minCellHeight: 28,
      ...tableStyles
    },
    headStyles: {
      fillColor: false,
      fontStyle: 'bold',
      lineWidth: 0.5,
      lineColor: '#0C0C0C',
      cellPadding: PDF_VIEW_ENUMS.cellPadding, // Ensure same padding as body
      ...header?.styles
    },
    columnStyles:
      numberOfColumn == 2
        ? {
            0: {
              cellWidth: firstColumnWidth,
              valign: 'top',
              ...colStyles?.col1Style
            },
            1: { cellWidth: columnWidth, ...colStyles?.col2Style }
          }
        : { 0: { cellWidth: columnWidth, ...colStyles?.col1Style } },
    tableWidth: 'wrap',
    // Add page break configuration
    pageBreak: 'auto',
    // Ensure borders are drawn on all pages
    showFoot: 'lastPage',
    // Improve border rendering
    // margin: { top: 25, right: 0, bottom: 25, left: 0 },
    didParseCell: (data) => {
      const { cell, section, column } = data

      cell.styles.minCellHeight = 28
      // Ensure borders are always drawn
      cell.styles.lineWidth = 0.5
      cell.styles.lineColor = '#0C0C0C'

      // Ensure consistent padding for both header and body
      cell.styles.cellPadding = PDF_VIEW_ENUMS.cellPadding

      if (config && config.fontStyle && column.index === 1) {
        cell.styles.fontStyle = config.fontStyle.value
      }
      // Only apply font on body
      if (section === 'body') {
        cell.styles.font = currentFont || DEFAULT_FONT
        cell.styles.fontSize = currentFontSize
        cell.styles.textColor = [24, 25, 30] // #18191E
      }
    },
    willDrawCell: (data: any) => {
      onWillDrawCell?.(data)
    },
    didDrawCell: (data: any) => {
      onDidDrawCell?.(data)
    },
    didDrawPage: function (data) {
      onDidDrawPage?.(data)
    }
  })

  return (pdf as any).lastAutoTable.finalY
}

export default tableRenderer
