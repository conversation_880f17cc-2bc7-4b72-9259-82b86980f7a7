import { t } from 'i18next'
import type { jsPDF } from 'jspdf'

import type { ISelectOption } from '~/core/ui/Select'
import { formatDatePickerToDate } from '~/core/ui/SingleDateWithYearOnlyPicker'
import { formatDateWithKey } from '~/core/utilities/format-date'

import type { PreferredWorkStatesType, TalentPoolType } from '~/lib/features/candidates/types'
import { getYearOld } from '~/lib/features/candidates/utilities'
import { DEFAULT_CURRENCY } from '~/lib/features/candidates/utilities/enum'
import type { IResume } from '~/lib/features/settings/profiles/edit/types'
import { LIST_SECTIONS_FIELD_DEFAULT } from '~/lib/features/settings/profiles/edit/utilities/enum'

import type { IKeyTemplate } from '../../KeysTemplate/KeysTemplate'
import renderFieldSection from './fieldSection'

const renderDataByKey = ({
  key,
  resumeData,
  percentScale,
  hideTitle = false,
  keys,
  extraData,
  validateHidden = false,
  customRelatedFields,
  pdf,
  pdfPosition,
  watermark,
  currentFontSize = 12,
  dateFormatContent,
  currentFont,
  maxWidthTitle,
  paddingLeftTitle = 8,
  paddingTopTitle,
  startIndent,
  paddingIndent,
  lineHeightTemplate = 1
}: {
  key: string
  percentScale: number
  resumeData?: IResume
  hideTitle?: boolean
  keys: IKeyTemplate
  extraData?: {
    [key: string]: ISelectOption[]
  }
  validateHidden?: boolean
  customRelatedFields?: {
    [key: string]: boolean
  }
  pdf: jsPDF
  pdfPosition: number
  watermark: string
  currentFontSize?: number
  dateFormatContent?: ISelectOption
  currentFont?: string
  maxWidthTitle?: number
  paddingLeftTitle?: number
  paddingTopTitle?: number
  startIndent?: number
  paddingIndent?: number
  lineHeightTemplate?: number
}) => {
  if (key === LIST_SECTIONS_FIELD_DEFAULT.publicId) {
    if (validateHidden && !resumeData?.permittedFields?.publicId) return { y: pdfPosition }
    return renderFieldSection({
      currentFontSize,
      title: keys.publicId,
      content: String(resumeData?.permittedFields?.publicId?.value),
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.fullName) {
    if (validateHidden && !resumeData?.permittedFields?.fullName) return { y: pdfPosition }
    return renderFieldSection({
      currentFontSize,
      title: keys.fullName,
      content: resumeData?.permittedFields?.fullName?.value,
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.email) {
    if (validateHidden && !resumeData?.permittedFields?.email) return { y: pdfPosition }
    return renderFieldSection({
      currentFontSize,
      title: keys.email,
      content: resumeData?.permittedFields?.email?.value,
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.phoneNumber) {
    if (validateHidden && !resumeData?.permittedFields?.phoneNumber) return { y: pdfPosition }
    return renderFieldSection({
      currentFontSize,
      title: keys.phoneNumber,
      content: resumeData?.permittedFields?.phoneNumber?.value,
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.location) {
    if (validateHidden && !resumeData?.permittedFields?.location) return { y: pdfPosition }
    return renderFieldSection({
      currentFontSize,
      title: keys.location,
      content: resumeData?.permittedFields?.location?.value,
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.links) {
    if (validateHidden && !resumeData?.permittedFields?.links) return { y: pdfPosition }
    const content = []
    let line = 0
    for (let i = 0; i < Object.keys(resumeData?.permittedFields?.links?.value || {}).length; i++) {
      const item = Object.keys(resumeData?.permittedFields?.links?.value || {})[i]
      line += (resumeData?.permittedFields?.links?.value?.[item as string] || []).length

      for (let y = 0; y < (resumeData?.permittedFields?.links?.value?.[item as string] || []).length; y++) {
        const subItem = resumeData?.permittedFields?.links?.value?.[item as string]?.[y]
        content.push(String(subItem))
      }
    }

    return renderFieldSection({
      title: keys.links,
      content,
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: line || 1,
      currentFontSize,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.headline) {
    if (validateHidden && !resumeData?.permittedFields?.headline) return { y: pdfPosition }
    return renderFieldSection({
      currentFontSize,
      title: keys.headline,
      content: resumeData?.permittedFields?.headline?.value,
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.departments) {
    if (validateHidden && !resumeData?.permittedFields?.departments) return { y: pdfPosition }

    const departments: string[] = resumeData?.permittedFields?.departments?.value?.departments || []
    const allDepartments = resumeData?.permittedFields?.departments?.value?.all_departments

    const content = allDepartments
      ? t('settings:departments:allDepartments')
      : departments.map((item) => item).join(', ')

    return renderFieldSection({
      currentFontSize,
      title: keys.departments,
      content,
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.totalYearsOfExp) {
    if (validateHidden && !resumeData?.permittedFields?.totalYearsOfExp) return { y: pdfPosition }
    return renderFieldSection({
      currentFontSize,
      title: keys.totalYearsOfExp,
      content:
        String(resumeData?.permittedFields?.totalYearsOfExp?.value) !== 'null'
          ? `${
              keys.yoeOptions?.[
                String(resumeData?.permittedFields?.totalYearsOfExp?.value || 0) === '0'
                  ? 999999
                  : String(resumeData?.permittedFields?.totalYearsOfExp?.value || '')
              ]
            }`
          : undefined,
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.skills) {
    if (validateHidden && !resumeData?.permittedFields?.skills) return { y: pdfPosition }
    return renderFieldSection({
      currentFontSize,
      title: keys.skills,
      content: (resumeData?.permittedFields?.skills?.value || []).map((item) => item).join(', '),
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.languages) {
    if (validateHidden && !resumeData?.permittedFields?.languages) return { y: pdfPosition }
    const content = []
    for (let i = 0; i < (resumeData?.permittedFields?.languages?.value || []).length; i++) {
      const item = resumeData?.permittedFields?.languages?.value?.[i]
      content.push(
        `${item?.languageDescription}${item?.proficiencyDescription ? ` - ${item?.proficiencyDescription}` : ''}`
      )
    }

    return renderFieldSection({
      currentFontSize,
      title: keys.languages,
      content,
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: (resumeData?.permittedFields?.languages?.value || []).length || 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.currentSalary) {
    if (validateHidden && !resumeData?.permittedFields?.currentSalary) return { y: pdfPosition }
    return renderFieldSection({
      currentFontSize,
      title: keys.currentSalary,
      content:
        Number(resumeData?.permittedFields?.currentSalary?.value) > 0
          ? `${Number(resumeData?.permittedFields?.currentSalary?.value)
              .toString()
              .replace(
                /\B(?=(\d{3})+(?!\d))/g,
                ','
              )} ${resumeData?.permittedFields?.currentSalaryCurrency?.value || DEFAULT_CURRENCY} ${
              resumeData?.permittedFields?.typeOfCurrentSalary?.value === 'monthly'
                ? keys.typeOfSalaryMonthly
                : keys.typeOfSalaryAnnual
            }`
          : undefined,
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.expectedSalary) {
    if (validateHidden && !resumeData?.permittedFields?.expectedSalary) return { y: pdfPosition }
    return renderFieldSection({
      currentFontSize,
      title: keys.expectedSalary,
      content:
        Number(resumeData?.permittedFields?.expectedSalary?.value) > 0
          ? `${Number(resumeData?.permittedFields?.expectedSalary?.value)
              .toString()
              .replace(
                /\B(?=(\d{3})+(?!\d))/g,
                ','
              )} ${resumeData?.permittedFields?.expectedSalaryCurrency?.value || DEFAULT_CURRENCY} ${
              resumeData?.permittedFields?.typeOfExpectedSalary?.value === 'monthly'
                ? keys.typeOfSalaryMonthly
                : keys.typeOfSalaryAnnual
            }`
          : undefined,
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.birthday) {
    if (validateHidden && !resumeData?.permittedFields?.birthday) return { y: pdfPosition }
    let content
    const formatYearOld = `${getYearOld(
      formatDatePickerToDate({
        year: resumeData?.permittedFields?.birthday?.value?.year,
        month: resumeData?.permittedFields?.birthday?.value?.month,
        date: resumeData?.permittedFields?.birthday?.value?.date
      })
    )}${keys.birthdayYearsOld}`
    if (resumeData?.permittedFields?.birthday?.value?.year) {
      if (customRelatedFields?.age) {
        content = formatYearOld
      } else {
        if (
          !!resumeData?.permittedFields?.birthday?.value?.year &&
          !resumeData?.permittedFields?.birthday?.value?.month &&
          !resumeData?.permittedFields?.birthday?.value?.date
        ) {
          content = `${resumeData?.permittedFields?.birthday?.value.year} (${formatYearOld})`
        } else {
          content = `${formatDateWithKey(
            formatDatePickerToDate({
              year: resumeData?.permittedFields?.birthday?.value?.year,
              month: resumeData?.permittedFields?.birthday?.value?.month,
              date: resumeData?.permittedFields?.birthday?.value?.date
            }),
            dateFormatContent?.value
          )} (${formatYearOld})`
        }
      }
    }

    return renderFieldSection({
      currentFontSize,
      title: customRelatedFields?.age ? keys.age : keys.birthday,
      content,
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.profileLevel) {
    if (validateHidden && !resumeData?.permittedFields?.profileLevel) return { y: pdfPosition }
    return renderFieldSection({
      currentFontSize,
      title: keys.profileLevel,
      content: resumeData?.permittedFields?.profileLevel?.value
        ? (extraData?.profileLevel || [])?.find(
            (item: ISelectOption) => String(item.value) === String(resumeData?.permittedFields?.profileLevel?.value)
          )?.supportingObj?.name
        : undefined,
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.nationality) {
    if (validateHidden && !resumeData?.permittedFields?.nationality) return { y: pdfPosition }
    return renderFieldSection({
      currentFontSize,
      title: keys.nationality,
      content: resumeData?.permittedFields?.nationality?.value,
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.willingToRelocate) {
    if (validateHidden && !resumeData?.permittedFields?.willingToRelocate) return { y: pdfPosition }
    return renderFieldSection({
      currentFontSize,
      title: keys.willingToRelocate,
      content: resumeData?.permittedFields?.willingToRelocate?.value ? keys.yes : keys.no,
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.preferredWorkStates) {
    if (validateHidden && !resumeData?.permittedFields?.preferredWorkStates) return { y: pdfPosition }

    const preferredWorkState: PreferredWorkStatesType[] = resumeData?.permittedFields?.preferredWorkStates?.value || []

    const content = preferredWorkState.map((item) => item.full_name).join(', ')

    return renderFieldSection({
      currentFontSize,
      title: keys.preferredWorkStates,
      content,
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.noticeToPeriodDays) {
    if (validateHidden && !resumeData?.permittedFields?.noticeToPeriodDays) return { y: pdfPosition }
    return renderFieldSection({
      currentFontSize,
      title: keys.noticeToPeriodDays,
      content: resumeData?.permittedFields?.noticeToPeriodDays?.value,
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.talentPools) {
    if (validateHidden && !resumeData?.permittedFields?.talentPools) return { y: pdfPosition }
    return renderFieldSection({
      currentFontSize,
      title: keys.talentPools,
      content: (resumeData?.permittedFields?.talentPools?.value || [])
        .map((item: TalentPoolType) => item.name)
        .join(', '),
      percentScale,
      hideTitle,
      pdf,
      pdfPosition,
      watermark,
      height: 12,
      line: (resumeData?.permittedFields?.talentPools?.value || []).length || 1,
      currentFont,
      maxWidthTitle,
      paddingLeftTitle,
      paddingTopTitle,
      startIndent,
      paddingIndent,
      lineHeightTemplate
    })
  }

  return { y: pdfPosition }
}

export default renderDataByKey
