import Bold from '@tiptap/extension-bold'
import { Color } from '@tiptap/extension-color'
import Link from '@tiptap/extension-link'
import TextAlign from '@tiptap/extension-text-align'
import TextStyle from '@tiptap/extension-text-style'
import Underline from '@tiptap/extension-underline'
import StarterKit from '@tiptap/starter-kit'
import type { TFunction } from 'i18next'

import type { ISelectOption } from '~/core/ui/Select'
import { formatDatePickerToDate } from '~/core/ui/SingleDateWithYearOnlyPicker'
import { formatDateWithKey } from '~/core/utilities/format-date'

import type { PreferredWorkStatesType, TalentPoolType } from '~/lib/features/candidates/types'
import { getYearOld } from '~/lib/features/candidates/utilities'
import { DEFAULT_CURRENCY } from '~/lib/features/candidates/utilities/enum'

import type { IKeyTemplate } from '../../KeysTemplate/KeysTemplate'
import { generateJSON } from '../../utilities/generateJSON'

const headingFontSizes: Record<string, number> = {
  1: 30,
  2: 20,
  3: 18
}

export const rgbaToHex = (colorStr: string, forceRemoveAlpha: boolean = false) => {
  // Check if the input string contains '/'
  const hasSlash = colorStr.includes('/')

  if (hasSlash) {
    // Extract the RGBA values from the input string
    const rgbaValues = colorStr.match(/(\d+)\s+(\d+)\s+(\d+)\s+\/\s+([\d.]+)/)

    if (!rgbaValues) {
      return colorStr // Return the original string if it doesn't match the expected format
    }

    const [red, green, blue, alpha] = rgbaValues.slice(1, 5).map(parseFloat)

    // Convert the RGB values to hexadecimal format
    const redHex = red?.toString(16).padStart(2, '0')
    const greenHex = green?.toString(16).padStart(2, '0')
    const blueHex = blue?.toString(16).padStart(2, '0')

    // Convert alpha to a hexadecimal format (assuming it's already a decimal value in the range [0, 1])
    const alphaHex = forceRemoveAlpha
      ? ''
      : Math.round(Number(alpha) * 255)
          .toString(16)
          .padStart(2, '0')

    // Combine the hexadecimal values to form the final hex color string
    const hexColor = `#${redHex}${greenHex}${blueHex}${alphaHex}`

    return hexColor
  } else {
    // Use the second code block for the case when '/' is not present
    return (
      '#' +
      colorStr
        .replace(/^rgba?\(|\s+|\)$/g, '') // Get's rgba / rgb string values
        .split(',') // splits them at ","
        .filter((string, index) => !forceRemoveAlpha || index !== 3)
        .map((string) => parseFloat(string)) // Converts them to numbers
        .map((number, index) => (index === 3 ? Math.round(number * 255) : number)) // Converts alpha to 255 number
        .map((number) => number.toString(16)) // Converts numbers to hex
        .map((string) => (string.length === 1 ? '0' + string : string)) // Adds 0 when length of one number is 1
        .join('')
    )
  }
}

export const birthday = ({
  keys,
  value,
  dateFormatContent,
  customRelatedFields
}: {
  keys: IKeyTemplate
  value: any
  dateFormatContent?: any
  customRelatedFields?: any
}) => {
  let content
  const formatYearOld = `${getYearOld(
    formatDatePickerToDate({
      year: value?.year,
      month: value?.month,
      date: value?.date
    })
  )}${keys.birthdayYearsOld}`

  if (value?.year) {
    if (customRelatedFields?.age) {
      content = formatYearOld
    } else {
      if (!!value?.year && !value?.month && !value?.date) {
        content = `${value.year} (${formatYearOld})`
      } else {
        content = `${formatDateWithKey(
          formatDatePickerToDate({
            year: value?.year,
            month: value?.month,
            date: value?.date
          }),
          dateFormatContent?.value
        )} (${formatYearOld})`
      }
    }
  }

  return content
}

export const profileLevel = ({
  value,
  extraData
}: {
  value: any
  extraData?: {
    [type: string]: ISelectOption[]
  }
}) => {
  return value
    ? (extraData?.profileLevel || [])?.find((item: ISelectOption) => String(item.value) === String(value))
        ?.supportingObj?.name
    : undefined
}

export const languages = (values: any) => {
  const content = []
  for (let i = 0; i < (values || []).length; i++) {
    const item = values?.[i]
    content.push(
      `${item?.languageDescription}${item?.proficiencyDescription ? ` - ${item?.proficiencyDescription}` : ''}`
    )
  }

  return content.join('\n')
}

export const formattedRichText = (content = '') => {
  const { content: contentJSON } = generateJSON(
    content,
    [
      Bold,
      Underline,
      Color,
      TextStyle,
      Link,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
        alignments: ['left', 'center', 'right']
      }),
      // @ts-expect-error
      StarterKit.configure({
        bulletList: {
          keepMarks: true,
          keepAttributes: false
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false
        }
      })
    ],
    {
      preserveWhitespace: 'full'
    }
  )
  const formatted: Array<unknown> = []

  if (contentJSON.length) {
    const recurseList = (
      node: {
        content?: Array<{
          content?: Array<{
            type: string
            text: string
            marks?: never
          }>
          type: string
        }>
        type?: string
      },
      indexX: number,
      results: {
        contentType: string
        level?: number
        content: Array<object>
        subs: Array<object>
      },
      contentType: string
    ) => {
      if (node.content?.length) {
        for (let i = 0; i < node.content.length; i++) {
          if (node.content[i]?.type === 'paragraph') {
            const itemContent = node?.content?.[i]?.content || []
            for (let index = 0; index < itemContent?.length; index++) {
              const itemSubContent = itemContent[index]
              const contentParagraph = itemSubContent?.text
              const typeContent = updateFormatTypePDF(itemSubContent?.marks || [])

              if (itemSubContent?.type === 'hardBreak') {
                results.content.push({
                  text: '',
                  hardBreak: true,
                  ...typeContent
                })
              } else {
                results.content.push({
                  text: contentParagraph,
                  ...typeContent
                })
              }
            }
          } else {
            const subItem = node.content[i]
            const totalLength = subItem?.content?.length || 0
            const mappings = Array.from(Array(totalLength).keys()).map(() => ({
              contentType,
              content: [],
              subs: []
            }))
            results.subs = mappings

            if (subItem?.content?.length) {
              for (let index = 0; index < subItem.content.length; index++) {
                const itemSubContent = subItem.content[index]
                const contentParagraph = itemSubContent?.text
                const typeContent = updateFormatTypePDF(itemSubContent?.marks || [])

                if (itemSubContent?.type === 'hardBreak') {
                  results.content.push({
                    text: '',
                    hardBreak: true,
                    ...typeContent
                  })
                } else {
                  if (itemSubContent?.type === 'text') {
                    results.content.push({
                      text: contentParagraph,
                      ...typeContent
                    })
                  } else {
                    recurseList(
                      itemSubContent as {
                        content?:
                          | {
                              content?:
                                | {
                                    type: string
                                    text: string
                                    marks?: undefined
                                  }[]
                                | undefined
                              type: string
                            }[]
                          | undefined
                        type?: string | undefined
                      },
                      index,
                      results.subs[index] as unknown as {
                        contentType: string
                        level?: number
                        content: Array<object>
                        subs: Array<object>
                      },
                      contentType
                    )
                  }
                }
              }
            }
          }
        }
      }
    }

    const updateFormatTypePDF = (typeContent = [], options = { forceBold: false }) => {
      const result: {
        fontStyle?: string
        color?: string
        href?: string
      } = {
        fontStyle: options?.forceBold ? 'bold' : 'normal'
      }

      for (let i = 0; i < typeContent.length; i++) {
        const formatType = typeContent[i] as unknown as {
          type: string
          attrs?: {
            color?: string
            align: string
            href?: string
            level?: number
          }
        }

        if (['bold', 'italic', 'underline'].includes(formatType.type) || options?.forceBold) {
          result.fontStyle = options?.forceBold
            ? result.fontStyle !== 'bold'
              ? `bold,${result.fontStyle},${formatType.type}`
              : `bold,${formatType.type}`
            : result.fontStyle !== 'normal'
              ? `${result.fontStyle},${formatType.type}`
              : formatType.type
        }

        if (formatType.type === 'textStyle') {
          const attrColor = formatType?.attrs?.color
          const rgbaRegex =
            /^(?:#(?:[A-Fa-f0-9]{3}){1,2}|(?:rgb[(](?:\s*0*(?:\d\d?(?:\.\d+)?(?:\s*%)?|\.\d+\s*%|100(?:\.0*)?\s*%|(?:1\d\d|2[0-4]\d|25[0-5])(?:\.\d+)?)\s*(?:,(?![)])|(?=[)]))){3}|hsl[(]\s*0*(?:[12]?\d{1,2}|3(?:[0-5]\d|60))\s*(?:\s*,\s*0*(?:\d\d?(?:\.\d+)?\s*%|\.\d+\s*%|100(?:\.0*)?\s*%)){2}\s*|(?:rgba[(](?:\s*0*(?:\d\d?(?:\.\d+)?(?:\s*%)?|\.\d+\s*%|100(?:\.0*)?\s*%|(?:1\d\d|2[0-4]\d|25[0-5])(?:\.\d+)?)\s*,){3}|hsla[(]\s*0*(?:[12]?\d{1,2}|3(?:[0-5]\d|60))\s*(?:\s*,\s*0*(?:\d\d?(?:\.\d+)?\s*%|\.\d+\s*%|100(?:\.0*)?\s*%)){2}\s*,)\s*0*(?:\.\d+|1(?:\.0*)?)\s*)[)])$/

          if (attrColor) {
            if (rgbaRegex.test(attrColor)) {
              const isRGBA = attrColor.substring(0, 4) === 'rgba'
              if (isRGBA) {
                result.color = rgbaToHex(attrColor)
              } else {
                result.color = attrColor
              }
            }
          }
        }

        if (formatType.type === 'link') {
          result.href = formatType?.attrs?.href
        }
      }

      return result
    }

    for (let i = 0; i < contentJSON.length; i++) {
      const item = contentJSON[i] as {
        type: string
        attrs?: {
          level: number
        }
        content?: Array<{
          text: string
          marks?: never
        }>
      }
      if (item.type === 'heading') {
        const results: {
          contentType: string
          level?: number
          content: Array<object>
        } = {
          contentType: 'heading',
          level: item?.attrs?.level,
          content: []
        }

        if (item.content?.length) {
          for (let ii = 0; ii < item.content.length; ii++) {
            const subItem = item.content[ii]
            const contentHeading = subItem?.text
            const typeContent = updateFormatTypePDF(subItem?.marks || [], {
              forceBold: true
            })

            results.content.push({
              text: contentHeading,
              fontSize: headingFontSizes[results.level || 1],
              ...typeContent
            })
          }

          formatted.push(results)
        }
      }

      if (item.type === 'paragraph') {
        const results: {
          contentType: string
          content: Array<object>
        } = {
          contentType: 'paragraph',
          content: []
        }

        if (item.content?.length) {
          for (let index = 0; index < item.content.length; index++) {
            const subItem = item.content[index] as {
              type: string
              text: string
              marks: never
            }
            const contentParagraph = subItem.text
            const typeContent = updateFormatTypePDF(subItem?.marks || [])

            if (subItem.type === 'hardBreak') {
              results.content.push({
                text: '',
                hardBreak: true,
                ...typeContent
              })
            } else {
              results.content.push({
                text: contentParagraph,
                ...typeContent
              })
            }
          }
        } else {
          results.content.push({
            text: '',
            hardBreak: true
          })
        }

        formatted.push(results)
      }

      if (item.type === 'bulletList') {
        if (item.content?.length) {
          for (let ii = 0; ii < item.content.length; ii++) {
            const results: {
              contentType: string
              level?: number
              content: Array<object>
              subs: Array<{
                contentType: string
                level?: number
                content: Array<object>
                subs: Array<object>
              }>
            } = {
              contentType: 'bulletList',
              content: [],
              subs: []
            }

            const subItem = item.content[ii] as {
              content?: Array<{
                content: Array<{ text: string; marks: never }>
              }>
            }
            if (subItem.content?.length) {
              for (let iii = 0; iii < subItem.content.length; iii++) {
                const itemContent = subItem.content[iii]
                if (itemContent?.content?.length) {
                  for (let index = 0; index < itemContent.content.length; index++) {
                    const itemSubContent = itemContent.content[index] as {
                      type: string
                      text: string
                      marks: never
                    }
                    const contentParagraph = itemSubContent.text
                    const typeContent = updateFormatTypePDF(itemSubContent?.marks || [])

                    if (itemSubContent.type === 'hardBreak') {
                      results.content.push({
                        text: '',
                        hardBreak: true,
                        ...typeContent
                      })
                    } else {
                      if (itemSubContent.type === 'text') {
                        results.content.push({
                          text: contentParagraph,
                          ...typeContent
                        })
                      } else {
                        results.subs.push({
                          contentType: 'bulletList',
                          content: [],
                          subs: []
                        })
                        recurseList(
                          itemSubContent,
                          index,
                          results.subs[index] as {
                            contentType: string
                            level?: number | undefined
                            content: object[]
                            subs: object[]
                          },
                          'bulletList'
                        )
                      }
                    }
                  }
                }
              }
            }

            formatted.push(results)
          }
        }
      }

      if (item.type === 'orderedList') {
        if (item.content?.length) {
          for (let ii = 0; ii < item.content.length; ii++) {
            const results: {
              contentType: string
              level?: number
              content: Array<object>
              subs: Array<{
                contentType: string
                level?: number
                content: Array<object>
                subs: Array<object>
              }>
            } = {
              contentType: 'orderedList',
              content: [],
              subs: []
            }

            const subItem = item.content[ii] as {
              content?: Array<{
                content: Array<{ text: string; marks: never }>
              }>
            }
            if (subItem.content?.length) {
              for (let iii = 0; iii < subItem.content.length; iii++) {
                const itemContent = subItem.content[iii]
                if (itemContent?.content?.length) {
                  for (let index = 0; index < itemContent.content.length; index++) {
                    const itemSubContent = itemContent.content[index] as {
                      type: string
                      text: string
                      marks: never
                    }
                    const contentParagraph = itemSubContent.text
                    const typeContent = updateFormatTypePDF(itemSubContent?.marks || [])

                    if (itemSubContent.type === 'hardBreak') {
                      results.content.push({
                        text: '',
                        hardBreak: true,
                        ...typeContent
                      })
                    } else {
                      if (itemSubContent.type === 'text') {
                        results.content.push({
                          text: contentParagraph,
                          ...typeContent
                        })
                      } else {
                        results.subs.push({
                          contentType: 'orderedList',
                          content: [],
                          subs: []
                        })
                        recurseList(
                          itemSubContent,
                          index,
                          results.subs[index] as {
                            contentType: string
                            level?: number | undefined
                            content: object[]
                            subs: object[]
                          },
                          'orderedList'
                        )
                      }
                    }
                  }
                }
              }
            }

            formatted.push(results)
          }
        }
      }
    }
  }

  return formatted
}

export const talentPools = (values: TalentPoolType[]) => {
  return values.map((item: TalentPoolType) => item.name).join(', ')
}

export const preferredWorkStates = (values: PreferredWorkStatesType[]) => {
  return values.map((item: PreferredWorkStatesType) => item.full_name).join(', ')
}

export const skills = (values: string[]) => {
  return values.map((item: string) => item).join(', ')
}

export const totalYearsOfExp = (value: string | undefined, keys: IKeyTemplate) => {
  return String(value) !== 'null'
    ? `${keys.yoeOptions?.[String(value || 0) === '0' ? 999999 : String(value || '')]}`
    : undefined
}

export const salary = (
  value: number,
  currentSalaryCurrency: string,
  typeOfCurrentSalary: string,
  keys: IKeyTemplate
) => {
  return Number(value) > 0
    ? `${Number(value)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')} ${currentSalaryCurrency || DEFAULT_CURRENCY} ${
        typeOfCurrentSalary === 'monthly' ? keys.typeOfSalaryMonthly : keys.typeOfSalaryAnnual
      }`
    : undefined
}

export const links = (value: any) => {
  const content = []
  let line = 0
  for (let i = 0; i < Object.keys(value || {}).length; i++) {
    const item = Object.keys(value || {})[i]

    line += (value?.[item as string] || []).length

    for (let y = 0; y < (value?.[item as string] || []).length; y++) {
      const subItem = value?.[item as string][y]
      content.push(String(subItem))
    }
  }

  return content.join('\n')
}

export const departments = (value: any, t?: TFunction) => {
  if (value.all_departments) return t ? `${t('settings:departments:allDepartments')}` : 'All Teams/Departments'
  return value?.departments?.map((item: string) => item).join(', ')
}
