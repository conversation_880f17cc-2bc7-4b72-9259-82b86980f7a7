import type { TFunction } from 'i18next'

import type { ISelectOption } from '~/core/ui/Select'
import { removeHTMLTags } from '~/core/utilities/common'

import type { CustomFieldViewType } from '~/lib/features/settings/profile-fields/types/custom-field'
import type { IResume, ISectionCustomFieldParamType } from '~/lib/features/settings/profiles/edit/types'
import { LIST_SECTIONS_FIELD_DEFAULT } from '~/lib/features/settings/profiles/edit/utilities/enum'

import type { IKeyTemplate } from '../../KeysTemplate/KeysTemplate'
import {
  birthday,
  departments,
  formattedRichText,
  languages,
  links,
  preferredWorkStates,
  profileLevel,
  salary,
  skills,
  talentPools,
  totalYearsOfExp
} from './formatter'
import formatValueCustomField from './formatValueCustomField'

const mappingData = ({
  resumeData,
  section,
  customFieldViewData,
  keys,
  extraData,
  dateFormatContent,
  t
}: {
  resumeData?: IResume
  section: ISectionCustomFieldParamType
  customFieldViewData?: CustomFieldViewType[]
  keys: IKeyTemplate
  extraData?: {
    [type: string]: ISelectOption[]
  }
  dateFormatContent?: ISelectOption
  watermark?: string
  t?: TFunction
}) => {
  let body: any = []
  const tables = []
  let addNewTable = false

  const mappingsField = (customFieldViewData || [])?.filter((item) => item.visibleToEmployeeProfile && item.visibility)

  if (section.cvTemplateCustomFields?.length) {
    // const customFields = section.cvTemplateCustomFields.filter(
    //   (f) => f.isCustom && f.visibleToEmployeeProfile && f.visibility
    // )

    // const customFieldsMerged = customFields.map((item) => {
    //   const matched = customFieldViewData?.find(
    //     (i) => i.id === item.customSettingId
    //   )
    //   return {
    //     ...matched
    //   }
    // })

    for (let i = 0; i < section.cvTemplateCustomFields.length; i++) {
      const field = section.cvTemplateCustomFields[i]

      if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.fullName && resumeData?.permittedFields?.fullName) {
        body.push([keys.fullName, resumeData?.permittedFields?.fullName?.value || ''])
      } else if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.publicId && resumeData?.permittedFields?.publicId) {
        body.push([keys.publicId, resumeData?.permittedFields?.publicId?.value || ''])
      } else if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.email && resumeData?.permittedFields?.email) {
        body.push([keys.email, resumeData?.permittedFields?.email?.value || ''])
      } else if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.phoneNumber && resumeData?.permittedFields?.phoneNumber) {
        body.push([keys.phoneNumber, resumeData?.permittedFields?.phoneNumber?.value || ''])
      } else if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.location && resumeData?.permittedFields?.location) {
        body.push([keys.location, resumeData?.permittedFields?.location?.value || ''])
      } else if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.profileLevel && resumeData?.permittedFields?.profileLevel) {
        const value = profileLevel({
          value: resumeData?.permittedFields?.profileLevel?.value || '',
          extraData
        })
        body.push([keys.profileLevel, value])
      } else if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.languages && resumeData?.permittedFields?.languages) {
        const values = languages(resumeData?.permittedFields?.languages?.value || [])
        body.push([keys.languages, values])
      } else if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.skills && resumeData?.permittedFields?.skills) {
        body.push([keys.skills, skills(resumeData?.permittedFields?.skills?.value || [])])
      } else if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.birthday && resumeData?.permittedFields?.birthday) {
        const value = birthday({
          keys,
          value: resumeData?.permittedFields?.birthday?.value || '',
          dateFormatContent,
          customRelatedFields: field?.customRelatedFields
        })
        body.push([keys.birthday, value])
      } else if (
        field?.key === LIST_SECTIONS_FIELD_DEFAULT.willingToRelocate &&
        resumeData?.permittedFields?.willingToRelocate
      ) {
        body.push([keys.willingToRelocate, resumeData?.permittedFields?.willingToRelocate?.value ? keys.yes : keys.no])
      } else if (
        field?.key === LIST_SECTIONS_FIELD_DEFAULT.preferredWorkStates &&
        resumeData?.permittedFields?.preferredWorkStates
      ) {
        body.push([
          keys.preferredWorkStates,
          preferredWorkStates(resumeData?.permittedFields?.preferredWorkStates?.value || [])
        ])
      } else if (
        field?.key === LIST_SECTIONS_FIELD_DEFAULT.noticeToPeriodDays &&
        resumeData?.permittedFields?.noticeToPeriodDays
      ) {
        body.push([keys.noticeToPeriodDays, resumeData?.permittedFields?.noticeToPeriodDays?.value || ''])
      } else if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.headline && resumeData?.permittedFields?.headline) {
        body.push([keys.headline, resumeData?.permittedFields?.headline?.value || ''])
      } else if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.talentPools && resumeData?.permittedFields?.talentPools) {
        body.push([keys.talentPools, talentPools(resumeData?.permittedFields?.talentPools?.value || [])])
      } else if (
        field?.key === LIST_SECTIONS_FIELD_DEFAULT.totalYearsOfExp &&
        resumeData?.permittedFields?.totalYearsOfExp
      ) {
        body.push([keys.totalYearsOfExp, totalYearsOfExp(resumeData?.permittedFields?.totalYearsOfExp?.value, keys)])
      } else if (
        field?.key === LIST_SECTIONS_FIELD_DEFAULT.currentSalary &&
        resumeData?.permittedFields?.currentSalary
      ) {
        body.push([
          keys.currentSalary,
          salary(
            resumeData?.permittedFields?.currentSalary?.value || 0,
            resumeData?.permittedFields?.currentSalaryCurrency?.value || '',
            resumeData?.permittedFields?.typeOfCurrentSalary?.value || '',
            keys
          )
        ])
      } else if (
        field?.key === LIST_SECTIONS_FIELD_DEFAULT.expectedSalary &&
        resumeData?.permittedFields?.expectedSalary
      ) {
        body.push([
          keys.expectedSalary,
          salary(
            resumeData?.permittedFields?.expectedSalary?.value || 0,
            resumeData?.permittedFields?.expectedSalaryCurrency?.value || '',
            resumeData?.permittedFields?.typeOfExpectedSalary?.value || '',
            keys
          )
        ])
      } else if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.nationality && resumeData?.permittedFields?.nationality) {
        body.push([keys.nationality, resumeData?.permittedFields?.nationality?.value])
      } else if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.links && resumeData?.permittedFields?.links) {
        body.push([keys.links, links(resumeData?.permittedFields?.links?.value || {})])
      } else if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.departments && resumeData?.permittedFields?.departments) {
        body.push([
          keys.departments,
          departments(
            resumeData?.permittedFields?.departments?.value || {
              departments: []
            },
            t
          )
        ])
      } else {
        const fieldAvailable = field?.isCustom && field?.visibleToEmployeeProfile && field?.visibility

        if (fieldAvailable) {
          const customFieldMatched = customFieldViewData?.find((i) => i.id === field?.customSettingId)
          const value = !['select', 'multiple', 'paragraph'].includes(customFieldMatched?.type || '')
            ? resumeData?.customFields?.find((item) => String(item.customSettingId) === String(customFieldMatched?.id))
                ?.value
            : resumeData?.customFields?.find((item) => String(item.customSettingId) === String(customFieldMatched?.id))
                ?.selectedOptionKeys

          const findCustomView = mappingsField?.find((item) => String(item.id) === String(customFieldMatched?.id))

          if (customFieldMatched?.type === 'paragraph') {
            let htmlValue =
              resumeData?.customFields?.find((item) => String(item.customSettingId) === String(customFieldMatched?.id))
                ?.value || ''
            addNewTable = true
            if (!removeHTMLTags(htmlValue).trim()) {
              htmlValue = ''
            }
            body.push([
              customFieldMatched?.label,
              {
                content: '',
                richContent: formattedRichText(htmlValue)
              }
            ])
          } else {
            body.push([
              customFieldMatched?.label,
              formatValueCustomField({
                value,
                fieldKind: customFieldMatched?.type as
                  | 'text'
                  | 'number'
                  | 'paragraph'
                  | 'toggle'
                  | 'select'
                  | 'date'
                  | 'multiple',
                selectOptions: findCustomView?.selectOptions,
                keys,
                dateFormatContent
              })
            ])
          }

          if (addNewTable === true) {
            tables.push(body)
            addNewTable = false
            body = []
          }
        }
      }
    }
  }

  if (body.length) tables.push(body)

  return tables
}

export default mappingData
