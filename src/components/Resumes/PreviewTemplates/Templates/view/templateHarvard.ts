import type { js<PERSON><PERSON> } from 'jspdf'

import type { IUserInformation } from '~/core/@types/global'
import { getShortName } from '~/core/ui/Avatar'
import type { ISelectOption } from '~/core/ui/Select'
import { formatDateWithCustomSelect } from '~/core/utilities/format-date'

import type {
  CertificatesType,
  EducationsType,
  ReferencesType,
  WorkExperiencesType
} from '~/lib/features/candidates/types'
import {
  checkCertificateFieldFormatDate,
  getCertificateFieldFormatDate,
  getEducationFieldFormatDate,
  getWorkExpFieldFormatDate
} from '~/lib/features/candidates/utilities/format'
import { mappingCustomFieldKind } from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'
import type { CustomFieldViewType } from '~/lib/features/settings/profile-fields/types/custom-field'
import type {
  IResume,
  ISectionCustomFieldParamType,
  ISectionParamType
} from '~/lib/features/settings/profiles/edit/types'
import { checkShowSectionName, getFormatDegreeEducation } from '~/lib/features/settings/profiles/edit/utilities'
import {
  DEFAULT_COLOR_PREVIEW_TEMPLATE,
  DEFAULT_FONT,
  LIST_SECTIONS_DEFAULT,
  LIST_SECTIONS_FIELD_DEFAULT,
  PDF_HEIGHT_ENUMS,
  PDF_SIZE_TITLE_ENUMS,
  PDF_VIEW_ENUMS
} from '~/lib/features/settings/profiles/edit/utilities/enum'

import type { IKeyTemplate } from '../../KeysTemplate/KeysTemplate'
import addTemplatePage from '../utilities/addTemplatePage'
import addWatermark from '../utilities/addWatermark'
import renderDataByKey from '../utilities/dataByKey'
import renderFieldSection from '../utilities/fieldSection'
import formatValueCustomField from '../utilities/formatValueCustomField'
import renderTitleSection from '../utilities/titleSection'

const renderTemplateHeader = ({
  user,
  pdf,
  pdfPosition,
  locale,
  configHide,
  templateName,
  resumeData,
  keys,
  currentFontSize,
  currentFont = DEFAULT_FONT,
  dateFormatContent
}: {
  user?: IUserInformation
  pdf: jsPDF
  pdfPosition: number
  locale: string
  configHide?: {
    isDefault: boolean // param only apply for FE
    templateNameEnabling: boolean
    dateEnabling: boolean
    profileIdEnabling: boolean
    fullnameEnabling: boolean
    avatarEnabling: boolean
    logoEnabling: boolean
    watermarkEnabling: boolean
    emailEnabling: boolean
    phoneNumberEnabling: boolean
  }
  templateName?: string
  resumeData?: IResume
  keys: IKeyTemplate
  currentFontSize?: number
  currentFont?: string
  dateFormatContent?: ISelectOption
}) => {
  let position = pdfPosition
  const AVATAR_SIZE = 88
  const LOGO_SIZE = 68
  const RIGHT_SIZE = 100

  // Right Header
  const heightTopLogoEnabling = configHide?.logoEnabling ? LOGO_SIZE : 0
  const textDate = `${formatDateWithCustomSelect(
    new Date(),
    String(dateFormatContent?.value),
    locale
  )}${locale === 'ja' ? ` ${keys.present}` : ''}`
  const PADDING_TOP_DATE = configHide?.logoEnabling ? 8 : 0
  const fontSizeDate = 12
  pdf.setFont(currentFont, 'normal')
  pdf.setFontSize(fontSizeDate)
  const { h: hTextDate } = pdf.getTextDimensions(textDate, {
    fontSize: fontSizeDate,
    maxWidth: AVATAR_SIZE
  })
  const heightTopDateEnabling = configHide?.dateEnabling ? hTextDate + PADDING_TOP_DATE : 0
  const heightOfRightTemplate = heightTopLogoEnabling + heightTopDateEnabling
  // End Right Header

  // Left Header
  const heightAvatarEnabling = configHide?.avatarEnabling ? AVATAR_SIZE : 0

  const fontSizeTemplateName = 13
  const maxWidthTextTemplateName =
    PDF_VIEW_ENUMS.widthAfterRemovePadding -
    (heightAvatarEnabling + (heightAvatarEnabling ? 12 : 0)) -
    (configHide?.dateEnabling ? RIGHT_SIZE : heightTopLogoEnabling) -
    15
  pdf.setFont(currentFont, 'normal')
  pdf.setFontSize(fontSizeTemplateName)
  const { h: hTemplateName } = pdf.getTextDimensions(templateName || '', {
    fontSize: fontSizeTemplateName,
    maxWidth: maxWidthTextTemplateName
  })
  const PADDING_TOP_TEMPLATE = 0
  const heightTemplateNameEnabling = configHide?.templateNameEnabling ? hTemplateName + PADDING_TOP_TEMPLATE : 0

  const fullName = resumeData?.permittedFields?.fullName?.value
  const fontSizeFullName = 20
  const maxWidthTextFullName =
    PDF_VIEW_ENUMS.widthAfterRemovePadding -
    (heightAvatarEnabling + (heightAvatarEnabling ? 12 : 0)) -
    (configHide?.dateEnabling ? RIGHT_SIZE : heightTopLogoEnabling) -
    15
  pdf.setFont(currentFont, 'normal')
  pdf.setFontSize(fontSizeFullName)
  const { h: hFullName } = pdf.getTextDimensions(fullName || '', {
    fontSize: fontSizeFullName,
    maxWidth: maxWidthTextFullName
  })
  const PADDING_TOP_FULL_NAME =
    configHide?.templateNameEnabling || configHide?.profileIdEnabling ? (configHide?.profileIdEnabling ? 10 : 14) : 0
  const heightFullnameEnabling = configHide?.fullnameEnabling ? hFullName + PADDING_TOP_FULL_NAME : 0

  const textProfileId = `${keys.id} ${resumeData?.permittedFields?.publicId?.value}`
  const fontSizeProfileId = 12
  const maxWidthTextProfileId =
    PDF_VIEW_ENUMS.widthAfterRemovePadding -
    (heightAvatarEnabling + (heightAvatarEnabling ? 12 : 0)) -
    (configHide?.dateEnabling ? RIGHT_SIZE : heightTopLogoEnabling) -
    15
  pdf.setFont(currentFont, 'normal')
  pdf.setFontSize(fontSizeProfileId)
  const { h: hProfileId } = pdf.getTextDimensions(textProfileId, {
    fontSize: fontSizeProfileId,
    maxWidth: maxWidthTextProfileId
  })
  const PADDING_TOP_PUBLIC_ID = configHide?.templateNameEnabling ? 10 : 0
  const heightProfileIdEnabling = configHide?.profileIdEnabling ? hProfileId + PADDING_TOP_PUBLIC_ID : 0

  const email = configHide?.emailEnabling
    ? Array.isArray(resumeData?.permittedFields?.email?.value)
      ? resumeData?.permittedFields?.email?.value?.[0]
      : resumeData?.permittedFields?.email?.value
    : undefined
  const phoneNumber = configHide?.phoneNumberEnabling
    ? Array.isArray(resumeData?.permittedFields?.phoneNumber?.value)
      ? resumeData?.permittedFields?.phoneNumber?.value?.[0]
      : resumeData?.permittedFields?.phoneNumber?.value
    : undefined
  const textContact = [email, phoneNumber].filter((item) => item).join(' | ')
  const fontSizeContact = 12
  const maxWidthTextContact =
    PDF_VIEW_ENUMS.widthAfterRemovePadding -
    (heightAvatarEnabling + (heightAvatarEnabling ? 12 : 0)) -
    (configHide?.dateEnabling ? RIGHT_SIZE : heightTopLogoEnabling) -
    15
  pdf.setFont(currentFont, 'normal')
  pdf.setFontSize(fontSizeContact)
  const { h: hContact } = pdf.getTextDimensions(textContact, {
    fontSize: fontSizeContact,
    maxWidth: maxWidthTextContact
  })
  const PADDING_TOP_PUBLIC_EMAIL_PHONE =
    configHide?.templateNameEnabling || configHide?.profileIdEnabling || configHide?.fullnameEnabling ? 9 : 0
  const heightContactEnabling =
    configHide?.emailEnabling || configHide?.phoneNumberEnabling ? hContact + PADDING_TOP_PUBLIC_EMAIL_PHONE : 0

  const heightOfLeftTemplate =
    heightTemplateNameEnabling + heightFullnameEnabling + heightProfileIdEnabling + heightContactEnabling
  // // End Left Header

  // Case 1
  if (configHide?.logoEnabling === false && configHide?.avatarEnabling === false) {
    let positionLeft = PDF_VIEW_ENUMS.paddingTopCustom

    if (configHide?.templateNameEnabling) {
      pdf
        .setFont(currentFont, 'normal')
        .setFontSize(fontSizeTemplateName)
        .setTextColor('18191E')
        .text(templateName || '', PDF_VIEW_ENUMS.paddingLeft, positionLeft, {
          align: 'left',
          maxWidth: maxWidthTextTemplateName,
          baseline: 'top'
        })

      positionLeft += heightTemplateNameEnabling
    }

    if (configHide?.profileIdEnabling) {
      pdf
        .setFont(currentFont, 'normal')
        .setFontSize(fontSizeProfileId)
        .setTextColor('18191E')
        .text(textProfileId, PDF_VIEW_ENUMS.paddingLeft, positionLeft + PADDING_TOP_PUBLIC_ID, {
          align: 'left',
          maxWidth: maxWidthTextProfileId,
          baseline: 'top'
        })

      positionLeft += heightProfileIdEnabling
    }

    if (configHide?.fullnameEnabling) {
      pdf
        .setFont(currentFont, 'normal')
        .setFontSize(fontSizeFullName)
        .setTextColor('18191E')
        .text(fullName || '', PDF_VIEW_ENUMS.paddingLeft, positionLeft + PADDING_TOP_FULL_NAME, {
          align: 'left',
          maxWidth: maxWidthTextFullName,
          baseline: 'top'
        })

      positionLeft += heightFullnameEnabling
    }

    if (configHide?.emailEnabling || configHide?.phoneNumberEnabling) {
      pdf
        .setFont(currentFont, 'normal')
        .setFontSize(fontSizeContact)
        .setTextColor('18191E')
        .text(textContact || '', PDF_VIEW_ENUMS.paddingLeft, positionLeft + PADDING_TOP_PUBLIC_EMAIL_PHONE, {
          align: 'left',
          maxWidth: maxWidthTextContact,
          baseline: 'top'
        })

      positionLeft += heightContactEnabling
    }

    if (configHide?.dateEnabling) {
      const calcHeightOfCenterY = Math.max(0, (positionLeft - PDF_VIEW_ENUMS.paddingTopCustom) / 2 - 6)
      const centerY = PDF_VIEW_ENUMS.paddingTopCustom + calcHeightOfCenterY

      pdf.setFontSize(12).setTextColor('333645').text(textDate, PDF_VIEW_ENUMS.widthCanDraw, centerY, {
        align: 'right',
        maxWidth: RIGHT_SIZE,
        baseline: 'top'
      })
    }

    position = positionLeft
  } else {
    const getHeightLeftSection =
      heightOfLeftTemplate > heightAvatarEnabling ? heightOfLeftTemplate : heightAvatarEnabling
    const paddingLeftWithAvatar = PDF_VIEW_ENUMS.paddingLeft + (heightAvatarEnabling + (heightAvatarEnabling ? 12 : 0))

    if (getHeightLeftSection > heightOfRightTemplate) {
      if (heightOfLeftTemplate > heightAvatarEnabling) {
        let positionLeft = PDF_VIEW_ENUMS.paddingTopCustom

        if (configHide?.templateNameEnabling) {
          pdf
            .setFont(currentFont, 'normal')
            .setFontSize(fontSizeTemplateName)
            .setTextColor('18191E')
            .text(templateName || '', paddingLeftWithAvatar, positionLeft + PADDING_TOP_TEMPLATE, {
              align: 'left',
              maxWidth: maxWidthTextTemplateName,
              baseline: 'top'
            })

          positionLeft += heightTemplateNameEnabling
        }

        if (configHide?.profileIdEnabling) {
          pdf
            .setFont(currentFont, 'normal')
            .setFontSize(fontSizeProfileId)
            .setTextColor('18191E')
            .text(textProfileId, paddingLeftWithAvatar, positionLeft + PADDING_TOP_PUBLIC_ID, {
              align: 'left',
              maxWidth: maxWidthTextProfileId,
              baseline: 'top'
            })

          positionLeft += heightProfileIdEnabling
        }

        if (configHide?.fullnameEnabling) {
          pdf
            .setFont(currentFont, 'normal')
            .setFontSize(fontSizeFullName)
            .setTextColor('18191E')
            .text(fullName || '', paddingLeftWithAvatar, positionLeft + PADDING_TOP_FULL_NAME, {
              align: 'left',
              maxWidth: maxWidthTextFullName,
              baseline: 'top'
            })

          positionLeft += heightFullnameEnabling + 2
        }

        if (configHide?.emailEnabling || configHide?.phoneNumberEnabling) {
          pdf
            .setFont(currentFont, 'normal')
            .setFontSize(fontSizeContact)
            .setTextColor('18191E')
            .text(textContact || '', paddingLeftWithAvatar, positionLeft + PADDING_TOP_PUBLIC_EMAIL_PHONE, {
              align: 'left',
              maxWidth: maxWidthTextContact,
              baseline: 'top'
            })

          positionLeft += heightContactEnabling
        }

        position = positionLeft

        if (configHide?.avatarEnabling) {
          const avatarBase64 = resumeData?.avatarRoundedBase64 || ''
          if (avatarBase64) {
            const imageProps = pdf.getImageProperties(avatarBase64 as string)
            const pdfHeight = AVATAR_SIZE
            const pdfWidth = (imageProps.width * pdfHeight) / imageProps.height
            pdf.addImage({
              imageData: avatarBase64 as string,
              format: imageProps.fileType,
              x: PDF_VIEW_ENUMS.paddingLeft,
              y: PDF_VIEW_ENUMS.paddingTopCustom + (heightOfLeftTemplate - AVATAR_SIZE) / 2,
              width: pdfWidth,
              height: pdfHeight
            })
          } else {
            const shortName = getShortName(
              String(resumeData?.permittedFields?.fullName?.value || resumeData?.permittedFields?.email?.value?.[0])
            )
            pdf
              .setLineWidth(1)
              .setDrawColor('d4dae0')
              .roundedRect(
                PDF_VIEW_ENUMS.paddingLeft,
                PDF_VIEW_ENUMS.paddingTopCustom + (heightOfLeftTemplate - AVATAR_SIZE) / 2,
                AVATAR_SIZE,
                AVATAR_SIZE,
                AVATAR_SIZE / 2,
                AVATAR_SIZE / 2
              )
            pdf.setFontSize(19)
            pdf.setFont(currentFont, 'medium')
            pdf.setTextColor('1D1E27')
            const { w } = pdf.getTextDimensions(shortName, {
              fontSize: 19
            })
            pdf.text(
              shortName,
              PDF_VIEW_ENUMS.paddingLeft + AVATAR_SIZE / 2 - w / 2,
              PDF_VIEW_ENUMS.paddingTopCustom + (heightOfLeftTemplate - AVATAR_SIZE) / 2 + AVATAR_SIZE / 2 - 9,
              {
                align: 'left',
                maxWidth: AVATAR_SIZE,
                baseline: 'top'
              }
            )
          }
        }
      } else {
        let positionAvatar = PDF_VIEW_ENUMS.paddingTopCustom

        if (configHide?.avatarEnabling) {
          const avatarBase64 = resumeData?.avatarRoundedBase64 || ''
          if (avatarBase64) {
            const imageProps = pdf.getImageProperties(avatarBase64 as string)
            const pdfHeight = AVATAR_SIZE
            const pdfWidth = (imageProps.width * pdfHeight) / imageProps.height
            pdf.addImage({
              imageData: avatarBase64 as string,
              format: imageProps.fileType,
              x: PDF_VIEW_ENUMS.paddingLeft,
              y: PDF_VIEW_ENUMS.paddingTopCustom,
              width: pdfWidth,
              height: pdfHeight
            })
          } else {
            const shortName = getShortName(
              String(resumeData?.permittedFields?.fullName?.value || resumeData?.permittedFields?.email?.value?.[0])
            )
            pdf
              .setLineWidth(1)
              .setDrawColor('d4dae0')
              .roundedRect(
                PDF_VIEW_ENUMS.paddingLeft,
                PDF_VIEW_ENUMS.paddingTopCustom,
                AVATAR_SIZE,
                AVATAR_SIZE,
                AVATAR_SIZE / 2,
                AVATAR_SIZE / 2
              )
            pdf.setFontSize(19)
            pdf.setFont(currentFont, 'medium')
            pdf.setTextColor('1D1E27')
            const { w } = pdf.getTextDimensions(shortName, {
              fontSize: 19
            })
            pdf.text(
              shortName,
              PDF_VIEW_ENUMS.paddingLeft + AVATAR_SIZE / 2 - w / 2,
              PDF_VIEW_ENUMS.paddingTopCustom + (heightAvatarEnabling - AVATAR_SIZE) / 2 + AVATAR_SIZE / 2 - 9,
              {
                align: 'left',
                maxWidth: AVATAR_SIZE,
                baseline: 'top'
              }
            )
          }

          positionAvatar += heightAvatarEnabling
        }

        position = positionAvatar

        let positionLeft = PDF_VIEW_ENUMS.paddingTopCustom + (heightAvatarEnabling - heightOfLeftTemplate) / 2

        if (configHide?.templateNameEnabling) {
          pdf
            .setFont(currentFont, 'normal')
            .setFontSize(fontSizeTemplateName)
            .setTextColor('18191E')
            .text(
              templateName || '',
              paddingLeftWithAvatar,
              PDF_VIEW_ENUMS.paddingTopCustom +
                PADDING_TOP_TEMPLATE +
                (heightAvatarEnabling - heightOfLeftTemplate) / 2,
              {
                align: 'left',
                maxWidth: maxWidthTextTemplateName,
                baseline: 'top'
              }
            )

          positionLeft += heightTemplateNameEnabling
        }

        if (configHide?.profileIdEnabling) {
          pdf
            .setFont(currentFont, 'normal')
            .setFontSize(fontSizeProfileId)
            .setTextColor('18191E')
            .text(textProfileId, paddingLeftWithAvatar, positionLeft + PADDING_TOP_PUBLIC_ID, {
              align: 'left',
              maxWidth: maxWidthTextProfileId,
              baseline: 'top'
            })

          positionLeft += heightProfileIdEnabling
        }

        if (configHide?.fullnameEnabling) {
          pdf
            .setFont(currentFont, 'normal')
            .setFontSize(fontSizeFullName)
            .setTextColor('18191E')
            .text(fullName || '', paddingLeftWithAvatar, positionLeft + PADDING_TOP_FULL_NAME, {
              align: 'left',
              maxWidth: maxWidthTextFullName,
              baseline: 'top'
            })

          positionLeft += heightFullnameEnabling + 2
        }

        if (configHide?.emailEnabling || configHide?.phoneNumberEnabling) {
          pdf
            .setFont(currentFont, 'normal')
            .setFontSize(fontSizeContact)
            .setTextColor('18191E')
            .text(textContact || '', paddingLeftWithAvatar, positionLeft + PADDING_TOP_PUBLIC_EMAIL_PHONE, {
              align: 'left',
              maxWidth: maxWidthTextContact,
              baseline: 'top'
            })

          positionLeft += heightContactEnabling
        }
      }

      let positionRight = PDF_VIEW_ENUMS.paddingTopCustom + (getHeightLeftSection - heightOfRightTemplate) / 2

      if (configHide?.logoEnabling) {
        const logoBase64 = resumeData?.logoBase64 || ''
        if (logoBase64) {
          const imageProps = pdf.getImageProperties(logoBase64 as string)
          const pdfHeight = LOGO_SIZE
          const pdfWidth = (imageProps.width * pdfHeight) / imageProps.height

          pdf.addImage({
            imageData: logoBase64 as string,
            format: imageProps.fileType,
            x: PDF_VIEW_ENUMS.widthCanDraw - LOGO_SIZE + (pdfHeight - pdfWidth),
            y: positionRight,
            width: pdfWidth,
            height: pdfHeight
          })
        } else {
          const shortName = getShortName(String(user?.currentTenant?.name))

          pdf
            .setLineWidth(1)
            .setDrawColor('d4dae0')
            .roundedRect(PDF_VIEW_ENUMS.widthCanDraw - LOGO_SIZE, positionRight, LOGO_SIZE, LOGO_SIZE, 4, 4)

          pdf.setFontSize(19)
          pdf.setFont(currentFont, 'medium')
          pdf.setTextColor('1D1E27')
          const { w } = pdf.getTextDimensions(shortName, {
            fontSize: 19
          })
          pdf.text(shortName, PDF_VIEW_ENUMS.widthCanDraw - LOGO_SIZE / 2 - w / 2, positionRight + LOGO_SIZE / 2 - 9, {
            align: 'left',
            maxWidth: LOGO_SIZE,
            baseline: 'top'
          })
        }

        positionRight += heightTopLogoEnabling
      }

      if (configHide?.dateEnabling) {
        pdf
          .setFontSize(12)
          .setTextColor('333645')
          .text(textDate, PDF_VIEW_ENUMS.widthCanDraw, positionRight + PADDING_TOP_DATE, {
            align: 'right',
            maxWidth: RIGHT_SIZE,
            baseline: 'top'
          })
      }
    } else {
      let positionRight = PDF_VIEW_ENUMS.paddingTopCustom

      if (configHide?.logoEnabling) {
        const logoBase64 = resumeData?.logoBase64 || ''
        if (logoBase64) {
          const imageProps = pdf.getImageProperties(logoBase64 as string)
          const pdfHeight = LOGO_SIZE
          const pdfWidth = (imageProps.width * pdfHeight) / imageProps.height

          pdf.addImage({
            imageData: logoBase64 as string,
            format: imageProps.fileType,
            x: PDF_VIEW_ENUMS.widthCanDraw - LOGO_SIZE + (pdfHeight - pdfWidth),
            y: positionRight,
            width: pdfWidth,
            height: pdfHeight
          })
        } else {
          const shortName = getShortName(String(user?.currentTenant?.name))

          pdf
            .setLineWidth(1)
            .setDrawColor('d4dae0')
            .roundedRect(PDF_VIEW_ENUMS.widthCanDraw - LOGO_SIZE, positionRight, LOGO_SIZE, LOGO_SIZE, 4, 4)

          pdf.setFontSize(19)
          pdf.setFont(currentFont, 'medium')
          pdf.setTextColor('1D1E27')
          const { w } = pdf.getTextDimensions(shortName, {
            fontSize: 19
          })
          pdf.text(shortName, PDF_VIEW_ENUMS.widthCanDraw - LOGO_SIZE / 2 - w / 2, positionRight + LOGO_SIZE / 2 - 9, {
            align: 'left',
            maxWidth: LOGO_SIZE,
            baseline: 'top'
          })
        }

        positionRight += heightTopLogoEnabling
      }

      if (configHide?.dateEnabling) {
        pdf
          .setFontSize(12)
          .setTextColor('333645')
          .text(textDate, PDF_VIEW_ENUMS.widthCanDraw, positionRight + PADDING_TOP_DATE, {
            align: 'right',
            maxWidth: RIGHT_SIZE,
            baseline: 'top'
          })

        positionRight += heightTopDateEnabling
      }

      position = positionRight

      const calc =
        heightAvatarEnabling > heightOfLeftTemplate
          ? heightOfLeftTemplate
          : heightAvatarEnabling || heightOfLeftTemplate
      let positionLeft = PDF_VIEW_ENUMS.paddingTopCustom + (heightOfRightTemplate - calc) / 2

      if (configHide?.avatarEnabling) {
        const avatarBase64 = resumeData?.avatarRoundedBase64 || ''
        const calcAvatar =
          heightAvatarEnabling > heightOfLeftTemplate
            ? PDF_VIEW_ENUMS.paddingTopCustom
            : positionLeft + (getHeightLeftSection - AVATAR_SIZE) / 2
        if (avatarBase64) {
          const imageProps = pdf.getImageProperties(avatarBase64 as string)
          const pdfHeight = AVATAR_SIZE
          const pdfWidth = (imageProps.width * pdfHeight) / imageProps.height
          pdf.addImage({
            imageData: avatarBase64 as string,
            format: imageProps.fileType,
            x: PDF_VIEW_ENUMS.paddingLeft,
            y: calcAvatar,
            width: pdfWidth,
            height: pdfHeight
          })
        } else {
          const shortName = getShortName(
            String(resumeData?.permittedFields?.fullName?.value || resumeData?.permittedFields?.email?.value?.[0])
          )
          pdf
            .setLineWidth(1)
            .setDrawColor('d4dae0')
            .roundedRect(
              PDF_VIEW_ENUMS.paddingLeft,
              calcAvatar,
              AVATAR_SIZE,
              AVATAR_SIZE,
              AVATAR_SIZE / 2,
              AVATAR_SIZE / 2
            )
          pdf.setFontSize(19)
          pdf.setFont(currentFont, 'medium')
          pdf.setTextColor('1D1E27')
          const { w } = pdf.getTextDimensions(shortName, {
            fontSize: 19
          })
          pdf.text(shortName, PDF_VIEW_ENUMS.paddingLeft + AVATAR_SIZE / 2 - w / 2, calcAvatar + AVATAR_SIZE / 2 - 9, {
            align: 'left',
            maxWidth: AVATAR_SIZE,
            baseline: 'top'
          })
        }
      }

      if (configHide?.templateNameEnabling) {
        pdf
          .setFont(currentFont, 'normal')
          .setFontSize(fontSizeTemplateName)
          .setTextColor('18191E')
          .text(templateName || '', paddingLeftWithAvatar, positionLeft + PADDING_TOP_TEMPLATE, {
            align: 'left',
            maxWidth: maxWidthTextTemplateName,
            baseline: 'top'
          })

        positionLeft += heightTemplateNameEnabling
      }

      if (configHide?.profileIdEnabling) {
        pdf
          .setFont(currentFont, 'normal')
          .setFontSize(fontSizeProfileId)
          .setTextColor('18191E')
          .text(textProfileId, paddingLeftWithAvatar, positionLeft + PADDING_TOP_PUBLIC_ID, {
            align: 'left',
            maxWidth: maxWidthTextProfileId,
            baseline: 'top'
          })

        positionLeft += heightProfileIdEnabling
      }

      if (configHide?.fullnameEnabling) {
        pdf
          .setFont(currentFont, 'normal')
          .setFontSize(fontSizeFullName)
          .setTextColor('18191E')
          .text(fullName || '', paddingLeftWithAvatar, positionLeft + PADDING_TOP_FULL_NAME, {
            align: 'left',
            maxWidth: maxWidthTextFullName,
            baseline: 'top'
          })

        positionLeft += heightFullnameEnabling + 2
      }

      if (configHide?.emailEnabling || configHide?.phoneNumberEnabling) {
        pdf
          .setFont(currentFont, 'normal')
          .setFontSize(fontSizeContact)
          .setTextColor('18191E')
          .text(textContact || '', paddingLeftWithAvatar, positionLeft + PADDING_TOP_PUBLIC_EMAIL_PHONE, {
            align: 'left',
            maxWidth: maxWidthTextContact,
            baseline: 'top'
          })

        positionLeft += heightContactEnabling
      }
    }
  }

  const MARGIN_SECTION = 23
  return { y: position + MARGIN_SECTION }
}

const renderCustomSection = ({
  section,
  resumeData,
  percentScale,
  customFieldViewData,
  keys,
  extraData,
  pdf,
  pdfPosition,
  watermark,
  currentFontSize = 12,
  dateFormatContent,
  currentFont
}: {
  section: ISectionCustomFieldParamType
  resumeData?: IResume
  percentScale: number
  customFieldViewData?: CustomFieldViewType[]
  keys: IKeyTemplate
  extraData?: {
    [key: string]: ISelectOption[]
  }
  pdf: jsPDF
  pdfPosition: number
  watermark: string
  currentFontSize?: number
  dateFormatContent?: ISelectOption
  currentFont?: string
}) => {
  const mappingsField = (customFieldViewData || [])?.filter((item) => item.visibleToEmployeeProfile && item.visibility)

  const MARGIN_ITEM = 6
  let position = pdfPosition

  if (checkShowSectionName({ section, resumeData, mappingsField })) {
    const { y: yTitleSectionPosition } = renderTitleSection({
      title: section.name || '',
      pdf,
      pdfPosition,
      watermark,
      fontSize: Number(PDF_SIZE_TITLE_ENUMS[currentFontSize]) - 2,
      currentFont,
      formatUpperCase: true,
      paddingLine: 17
    })

    position = yTitleSectionPosition - 2
  }

  if (section.cvTemplateCustomFields?.length === 1) {
    const isShowCustomField =
      section.cvTemplateCustomFields?.[0]?.isCustom === true &&
      section.cvTemplateCustomFields?.[0]?.visibility &&
      section.cvTemplateCustomFields?.[0]?.visibleToEmployeeProfile
    const isShowDefaultField = section.cvTemplateCustomFields?.[0]?.isCustom === false

    if (isShowCustomField) {
      const customFieldKind = mappingCustomFieldKind(section.cvTemplateCustomFields?.[0]?.fieldKind || 'text')

      const { y } = renderFieldSection({
        currentFontSize,
        title: '',
        content: formatValueCustomField({
          value: !['select', 'multiple'].includes(customFieldKind)
            ? resumeData?.customFields?.find(
                (item) => String(item.customSettingId) === String(section.cvTemplateCustomFields?.[0]?.customSettingId)
              )?.value
            : resumeData?.customFields?.find(
                (item) => String(item.customSettingId) === String(section.cvTemplateCustomFields?.[0]?.customSettingId)
              )?.selectedOptionKeys,
          fieldKind: customFieldKind,
          selectOptions: mappingsField?.find(
            (item) => String(item.id) === String(section.cvTemplateCustomFields?.[0]?.customSettingId)
          )?.selectOptions,
          keys,
          dateFormatContent
        }),
        percentScale,
        hideTitle: true,
        pdf,
        pdfPosition: position,
        watermark,
        height: 12,
        line: 1,
        currentFont,
        maxWidthTitle: 120,
        paddingLeftTitle: 4,
        lineHeightTemplate: customFieldKind === 'paragraph' ? 1.2 : 1
      })

      position = y
    }

    if (isShowDefaultField) {
      const { y } = renderDataByKey({
        currentFontSize,
        key: section.cvTemplateCustomFields?.[0]?.key || '',
        resumeData,
        percentScale,
        hideTitle: true,
        keys,
        extraData,
        validateHidden: true,
        customRelatedFields: section.cvTemplateCustomFields?.[0]?.customRelatedFields,
        pdf,
        pdfPosition: position,
        watermark,
        dateFormatContent,
        currentFont,
        maxWidthTitle: 120,
        paddingLeftTitle: 4,
        lineHeightTemplate: 1
      })

      position = y
    }
  } else {
    if (section.cvTemplateCustomFields?.length) {
      for (let i = 0; i < section.cvTemplateCustomFields.length; i++) {
        const field = section.cvTemplateCustomFields[i]

        if (field?.isCustom && field?.visibility && field?.visibleToEmployeeProfile) {
          const customFieldKind = mappingCustomFieldKind(field?.fieldKind || 'text')
          const value = !['select', 'multiple'].includes(customFieldKind)
            ? resumeData?.customFields?.find((item) => String(item.customSettingId) === String(field.customSettingId))
                ?.value
            : resumeData?.customFields?.find((item) => String(item.customSettingId) === String(field.customSettingId))
                ?.selectedOptionKeys
          const findCustomView = mappingsField?.find((item) => String(item.id) === String(field.customSettingId))

          const { y } = renderFieldSection({
            currentFontSize,
            title: field?.fieldName || '',
            content: formatValueCustomField({
              value,
              fieldKind: customFieldKind,
              selectOptions: findCustomView?.selectOptions,
              keys,
              dateFormatContent
            }),
            percentScale,
            pdf,
            pdfPosition: position,
            watermark,
            height: 12,
            line: 1,
            currentFont,
            maxWidthTitle: 120,
            paddingLeftTitle: 4,
            lineHeightTemplate: customFieldKind === 'paragraph' ? 1.2 : 1
          })

          position = y + MARGIN_ITEM
        } else {
          const { y } = renderDataByKey({
            currentFontSize,
            dateFormatContent,
            key: field?.key || '',
            resumeData,
            percentScale,
            keys,
            extraData,
            validateHidden: true,
            customRelatedFields: field?.customRelatedFields,
            pdf,
            pdfPosition: position,
            watermark,
            currentFont,
            maxWidthTitle: 120,
            paddingLeftTitle: 4,
            lineHeightTemplate: 1,
            paddingTopTitle: [
              LIST_SECTIONS_FIELD_DEFAULT.languages,
              LIST_SECTIONS_FIELD_DEFAULT.links,
              LIST_SECTIONS_FIELD_DEFAULT.talentPools
            ].includes(String(field?.key))
              ? 10
              : 3
          })

          position =
            y +
            ([
              LIST_SECTIONS_FIELD_DEFAULT.languages,
              LIST_SECTIONS_FIELD_DEFAULT.links,
              LIST_SECTIONS_FIELD_DEFAULT.talentPools
            ].includes(String(field?.key))
              ? -2
              : MARGIN_ITEM)
        }
      }
    }
  }

  const MARGIN_SECTION = 15
  return { y: position + MARGIN_SECTION }
}

const renderSummary = ({
  section,
  resumeData,
  percentScale,
  keys,
  pdf,
  pdfPosition,
  watermark,
  currentFontSize = 12,
  currentFont
}: {
  section: ISectionCustomFieldParamType
  resumeData?: IResume
  percentScale: number
  keys: IKeyTemplate
  pdf: jsPDF
  pdfPosition: number
  watermark: string
  currentFontSize?: number
  currentFont?: string
}) => {
  if (!resumeData?.permittedFields?.summary) return { y: pdfPosition }

  const MARGIN_ITEM = 4
  let position = pdfPosition

  const { y: yTitleSectionPosition } = renderTitleSection({
    title: section.name || keys.summaryTitle,
    pdf,
    pdfPosition,
    watermark,
    fontSize: Number(PDF_SIZE_TITLE_ENUMS[currentFontSize]) - 2,
    currentFont,
    formatUpperCase: true,
    paddingLine: 17
  })

  position = yTitleSectionPosition - 2

  if (resumeData?.permittedFields?.summary?.value) {
    const { y } = renderFieldSection({
      currentFontSize,
      title: '',
      content: resumeData.permittedFields.summary.value,
      hideTitle: true,
      percentScale,
      pdf,
      pdfPosition: position,
      watermark,
      height: 12,
      line: 1,
      currentFont,
      maxWidthTitle: 120,
      paddingLeftTitle: 4,
      lineHeightTemplate: 1.2
    })

    position = y
  }

  const MARGIN_SECTION = 20
  return { y: position + MARGIN_SECTION }
}

const renderWorkExperiences = ({
  resumeData,
  percentScale,
  keys,
  section,
  pdf,
  pdfPosition,
  watermark,
  currentFontSize = 12,
  dateFormatContent,
  currentFont = DEFAULT_FONT
}: {
  resumeData?: IResume
  percentScale: number
  keys: IKeyTemplate
  section: ISectionCustomFieldParamType
  pdf: jsPDF
  pdfPosition: number
  watermark: string
  currentFontSize?: number
  dateFormatContent?: ISelectOption
  currentFont?: string
}) => {
  if (!resumeData?.permittedFields?.workExperiences) return { y: pdfPosition }

  let position = pdfPosition
  const MARGIN_ITEM = 4

  const { y: yTitleSectionPosition } = renderTitleSection({
    title: section.name || keys.workExperiencesTitle,
    pdf,
    pdfPosition,
    watermark,
    fontSize: Number(PDF_SIZE_TITLE_ENUMS[currentFontSize]) - 2,
    currentFont,
    formatUpperCase: true,
    paddingLine: 17
  })

  position = yTitleSectionPosition - 2

  if (resumeData?.permittedFields?.workExperiences?.value?.length) {
    for (let i = 0; i < resumeData.permittedFields.workExperiences.value.length; i++) {
      const item = resumeData.permittedFields.workExperiences.value[i] as WorkExperiencesType
      const isShowDateTime =
        getWorkExpFieldFormatDate({
          type: 'from',
          attributes: item,
          isTemplate: true,
          dateFormatContent: dateFormatContent?.value
        }) && section.customRelatedFields?.datetime
      const isShowTitle = item.title && section.customRelatedFields?.title
      const isShowCompany = item.company && section.customRelatedFields?.company
      const isShowLocation = item.location && section.customRelatedFields?.location
      const isShowDescription = item.description && section.customRelatedFields?.description

      const isAddPage = position + 20 >= PDF_VIEW_ENUMS.heightCanDraw
      if (isAddPage) {
        addTemplatePage({
          isAdd: true,
          pdf,
          watermark,
          fontSize: Number(PDF_SIZE_TITLE_ENUMS[currentFontSize]),
          currentFont
        })
        position = PDF_VIEW_ENUMS.paddingTopForNewPage
      }

      let positionSectionLeft = position
      let positionSectionRight = position

      if (isShowCompany) {
        const fontSize = currentFontSize
        const maxWidth = 337
        const content = item.company || ''

        pdf.setFont(currentFont, 'medium')
        pdf.setFontSize(fontSize)
        const { h } = pdf.getTextDimensions(content, {
          fontSize,
          maxWidth
        })

        pdf
          .setFont(currentFont, 'medium')
          .setFontSize(fontSize)
          .text(content, PDF_VIEW_ENUMS.paddingLeft, positionSectionLeft, {
            align: 'left',
            maxWidth,
            baseline: 'top'
          })

        positionSectionLeft += h + 7
      }

      if (isShowTitle) {
        const fontSize = currentFontSize
        const maxWidth = 337
        const content = item.title || ''

        pdf.setFont(currentFont, 'normal')
        pdf.setFontSize(fontSize)
        const { h } = pdf.getTextDimensions(content, {
          fontSize,
          maxWidth
        })

        pdf
          .setFont(currentFont, 'normal')
          .setFontSize(fontSize)
          .text(content, PDF_VIEW_ENUMS.paddingLeft, positionSectionLeft, {
            align: 'left',
            maxWidth,
            baseline: 'top'
          })

        positionSectionLeft += h + 6
      }

      if (isShowLocation) {
        const fontSize = currentFontSize
        const maxWidth = 150
        const formatLocation = item?.location as {
          id?: string
          value?: string
        }
        const content = String((formatLocation?.id ? formatLocation?.value : item.location) || '')

        pdf.setFont(currentFont, 'normal')
        pdf.setFontSize(fontSize)
        const { h } = pdf.getTextDimensions(content, {
          fontSize,
          maxWidth
        })

        pdf
          .setFont(currentFont, 'normal')
          .setFontSize(fontSize)
          .setTextColor('333645')
          .text(content.trim(), PDF_VIEW_ENUMS.widthCanDraw, positionSectionRight, {
            align: 'right',
            maxWidth,
            baseline: 'top'
          })

        positionSectionRight += h + 6
      }

      if (isShowDateTime) {
        const fontSize = currentFontSize
        const maxWidth = 150
        const content = `${getWorkExpFieldFormatDate({
          type: 'from',
          attributes: item,
          isTemplate: true,
          dateFormatContent: dateFormatContent?.value,
          keys: {
            undefined: keys.undefined
          }
        })}${
          item?.currentWorking
            ? `- ${keys.present}`
            : `- ${getWorkExpFieldFormatDate({
                type: 'to',
                attributes: item,
                isTemplate: true,
                dateFormatContent: dateFormatContent?.value,
                keys: {
                  undefined: keys.undefined
                }
              })}`
        }`

        pdf.setFont(currentFont, 'normal')
        pdf.setFontSize(fontSize)
        const { h } = pdf.getTextDimensions(content, {
          fontSize,
          maxWidth
        })

        pdf
          .setFont(currentFont, 'normal')
          .setFontSize(fontSize)
          .text(content.trim(), PDF_VIEW_ENUMS.widthCanDraw, positionSectionRight, {
            align: 'right',
            maxWidth,
            baseline: 'top'
          })

        positionSectionRight += h + 6
      }

      position = positionSectionRight > positionSectionLeft ? positionSectionRight : positionSectionLeft

      if (isShowDescription) {
        const { y } = renderFieldSection({
          title: '',
          content: item.description,
          hideTitle: true,
          percentScale,
          pdf,
          pdfPosition: position + MARGIN_ITEM * 1.5,
          watermark,
          height: 12,
          line: 1,
          currentFontSize,
          currentFont,
          maxWidthTitle: 120,
          paddingLeftTitle: 4,
          lineHeightTemplate: 1.2
        })

        position = y
      }

      position += 4

      if (i < resumeData?.permittedFields?.workExperiences?.value?.length - 1) {
        position += MARGIN_ITEM * 2
      }
    }
  }

  const MARGIN_SECTION = 15
  return { y: position + MARGIN_SECTION }
}

const renderEducations = ({
  resumeData,
  percentScale,
  keys,
  extraData,
  section,
  pdf,
  pdfPosition,
  watermark,
  currentFontSize = 12,
  dateFormatContent,
  locale,
  currentFont = DEFAULT_FONT
}: {
  resumeData?: IResume
  percentScale: number
  keys: IKeyTemplate
  extraData?: ISelectOption[]
  section: ISectionCustomFieldParamType
  pdf: jsPDF
  pdfPosition: number
  watermark: string
  currentFontSize?: number
  dateFormatContent?: ISelectOption
  locale: string
  currentFont?: string
}) => {
  if (!resumeData?.permittedFields?.educations) return { y: pdfPosition }

  let position = pdfPosition
  const MARGIN_ITEM = 4

  const { y: yTitleSectionPosition } = renderTitleSection({
    title: section.name || keys.educationTitle,
    pdf,
    pdfPosition,
    watermark,
    fontSize: Number(PDF_SIZE_TITLE_ENUMS[currentFontSize]) - 2,
    currentFont,
    formatUpperCase: true,
    paddingLine: 17
  })

  position = yTitleSectionPosition - 2

  if (resumeData?.permittedFields?.educations?.value?.length) {
    for (let i = 0; i < resumeData.permittedFields.educations.value.length; i++) {
      const item = resumeData.permittedFields.educations.value[i] as EducationsType
      const isShowDateTime =
        getEducationFieldFormatDate({
          type: 'from',
          attributes: item,
          dateFormatContent: dateFormatContent?.value || '',
          locale
        }) && section.customRelatedFields?.datetime
      const isShowDegree = item.degree && section.customRelatedFields?.degree
      const isShowDegreeSubject = item.degreeSubject && section.customRelatedFields?.major
      const isShowSchool = section.customRelatedFields?.school && item.schoolName
      const isShowDescription = item.description && section.customRelatedFields?.description

      const isAddPage = position + 20 >= PDF_VIEW_ENUMS.heightCanDraw
      if (isAddPage) {
        addTemplatePage({
          isAdd: true,
          pdf,
          watermark,
          fontSize: Number(PDF_SIZE_TITLE_ENUMS[currentFontSize]),
          currentFont
        })
        position = PDF_VIEW_ENUMS.paddingTopForNewPage
      }

      let positionSectionLeft = position
      const positionSectionRight = position

      if (isShowSchool) {
        const fontSize = currentFontSize
        const maxWidth = 337
        const content = item.schoolName || ''

        pdf.setFont(currentFont, 'medium')
        pdf.setFontSize(fontSize)
        const { h } = pdf.getTextDimensions(content, {
          fontSize,
          maxWidth
        })

        pdf
          .setFont(currentFont, 'medium')
          .setFontSize(fontSize)
          .text(content, PDF_VIEW_ENUMS.paddingLeft, positionSectionLeft, {
            align: 'left',
            maxWidth,
            baseline: 'top'
          })

        positionSectionLeft += h + (isShowDescription || isShowDegree || isShowDegreeSubject ? 6 : 10)
      }

      if (isShowDegree || isShowDegreeSubject) {
        const fontSize = currentFontSize
        const maxWidth = 337
        const content = getFormatDegreeEducation({
          currentLanguage: locale,
          degree: section.customRelatedFields?.degree ? item.degree : undefined,
          degreeSubject: section.customRelatedFields?.major ? item.degreeSubject : undefined,
          listDegrees: extraData
        })

        pdf.setFont(currentFont, 'normal')
        pdf.setFontSize(fontSize)
        const { h } = pdf.getTextDimensions(content, {
          fontSize,
          maxWidth
        })

        pdf
          .setFont(currentFont, 'normal')
          .setFontSize(fontSize)
          .text(content, PDF_VIEW_ENUMS.paddingLeft, positionSectionLeft, {
            align: 'left',
            maxWidth,
            baseline: 'top'
          })

        positionSectionLeft += h + (isShowDescription ? 6 : 10)
      }

      if (isShowDateTime) {
        const fontSize = currentFontSize
        const maxWidth = 150
        const content = `${getEducationFieldFormatDate({
          type: 'from',
          attributes: item,
          isTemplate: true,
          keys: {
            undefined: keys.undefined
          },
          dateFormatContent: dateFormatContent?.value || '',
          locale
        })}- ${getEducationFieldFormatDate({
          type: 'to',
          attributes: item,
          isTemplate: true,
          keys: {
            undefined: keys.undefined
          },
          dateFormatContent: dateFormatContent?.value || '',
          locale
        })}`

        pdf.setFont(currentFont, 'normal')
        pdf.setFontSize(fontSize)
        const { h } = pdf.getTextDimensions(content, {
          fontSize,
          maxWidth
        })

        pdf
          .setFont(currentFont, 'normal')
          .setFontSize(fontSize)
          .text(content.trim(), PDF_VIEW_ENUMS.widthCanDraw, positionSectionRight, {
            align: 'right',
            maxWidth,
            baseline: 'top'
          })

        position += h + 6
      }

      position = positionSectionRight > positionSectionLeft ? positionSectionRight : positionSectionLeft

      if (isShowDescription) {
        const { y } = renderFieldSection({
          title: '',
          content: item.description,
          hideTitle: true,
          percentScale,
          pdf,
          pdfPosition: position + MARGIN_ITEM * 1.5,
          watermark,
          height: 12,
          line: 1,
          currentFontSize,
          currentFont,
          maxWidthTitle: 120,
          paddingLeftTitle: 4,
          lineHeightTemplate: 1.2
        })

        position = y + 4
      }

      if (i < resumeData?.permittedFields?.educations?.value?.length - 1) {
        position += MARGIN_ITEM * 2
      }
    }
  }

  const MARGIN_SECTION = 20
  return { y: position + MARGIN_SECTION }
}

const renderCertificates = ({
  resumeData,
  percentScale,
  keys,
  section,
  pdf,
  pdfPosition,
  watermark,
  currentFontSize = 12,
  dateFormatContent,
  currentFont = DEFAULT_FONT,
  locale
}: {
  resumeData?: IResume
  percentScale: number
  keys: IKeyTemplate
  section: ISectionCustomFieldParamType
  pdf: jsPDF
  pdfPosition: number
  watermark: string
  currentFontSize?: number
  dateFormatContent?: ISelectOption
  currentFont?: string
  locale?: string
}) => {
  if (!resumeData?.permittedFields?.certificates) return { y: pdfPosition }

  let position = pdfPosition
  const MARGIN_ITEM = 4
  const LEFT_ITEM = 64

  const { y: yTitleSectionPosition } = renderTitleSection({
    title: section.name || keys.certificatesTitle,
    pdf,
    pdfPosition,
    watermark,
    fontSize: Number(PDF_SIZE_TITLE_ENUMS[currentFontSize]) - 2,
    currentFont,
    formatUpperCase: true,
    paddingLine: 17
  })

  position = yTitleSectionPosition - 2

  if (resumeData?.permittedFields?.certificates?.value?.length) {
    for (let i = 0; i < resumeData.permittedFields.certificates.value.length; i++) {
      const item = resumeData.permittedFields.certificates.value[i] as CertificatesType
      const isShowDateTime = checkCertificateFieldFormatDate({
        attributes: item
      })

      const isAddPage = position + 20 >= PDF_VIEW_ENUMS.heightCanDraw
      if (isAddPage) {
        addTemplatePage({
          isAdd: true,
          pdf,
          watermark,
          fontSize: Number(PDF_SIZE_TITLE_ENUMS[currentFontSize]),
          currentFont
        })
        position = PDF_VIEW_ENUMS.paddingTopForNewPage
      }

      let positionSectionLeft = position
      let positionSectionRight = position

      if (item.certificateName) {
        const fontSize = currentFontSize
        const maxWidth = 337
        const content = item.certificateName || keys.undefined

        pdf.setFont(currentFont, 'medium')
        pdf.setFontSize(fontSize)
        const { h } = pdf.getTextDimensions(content, {
          fontSize,
          maxWidth
        })

        pdf
          .setFont(currentFont, 'medium')
          .setFontSize(fontSize)
          .text(content, PDF_VIEW_ENUMS.paddingLeft, positionSectionLeft, {
            align: 'left',
            maxWidth,
            baseline: 'top'
          })

        positionSectionLeft += h + 6
      }

      if (item.institution) {
        const fontSize = currentFontSize
        const maxWidth = 337
        const content = item.institution || ''

        pdf.setFont(currentFont, 'normal')
        pdf.setFontSize(fontSize)
        const { h } = pdf.getTextDimensions(content, {
          fontSize,
          maxWidth
        })

        pdf
          .setFont(currentFont, 'normal')
          .setFontSize(fontSize)
          .text(content, PDF_VIEW_ENUMS.paddingLeft, positionSectionLeft, {
            align: 'left',
            maxWidth,
            baseline: 'top'
          })

        positionSectionLeft += h + 6
      }

      if (isShowDateTime) {
        const fontSize = currentFontSize
        const maxWidth = 150
        const content = String(
          getCertificateFieldFormatDate({
            attributes: item,
            isTemplate: true,
            dateFormatContent: dateFormatContent?.value,
            locale
          }) || ''
        )

        pdf.setFont(currentFont, 'normal')
        pdf.setFontSize(fontSize)
        const { h } = pdf.getTextDimensions(content, {
          fontSize,
          maxWidth
        })

        pdf
          .setFont(currentFont, 'normal')
          .setFontSize(fontSize)
          .text(content.trim(), PDF_VIEW_ENUMS.widthCanDraw, positionSectionRight, {
            align: 'right',
            maxWidth,
            baseline: 'top'
          })

        positionSectionRight += h + 6
      }

      position = positionSectionRight > positionSectionLeft ? positionSectionRight : positionSectionLeft

      if (i < resumeData?.permittedFields?.certificates?.value?.length - 1) {
        position += 13
      }
    }
  }

  const MARGIN_SECTION = 20
  return { y: position + MARGIN_SECTION }
}

const renderReferences = ({
  resumeData,
  percentScale,
  keys,
  section,
  pdf,
  pdfPosition,
  watermark,
  currentFontSize = 12,
  currentFont = DEFAULT_FONT
}: {
  resumeData?: IResume
  percentScale: number
  keys: IKeyTemplate
  section: ISectionCustomFieldParamType
  pdf: jsPDF
  pdfPosition: number
  watermark: string
  currentFontSize?: number
  currentFont?: string
}) => {
  if (!resumeData?.permittedFields?.references) return { y: pdfPosition }

  let position = pdfPosition
  const MARGIN_ITEM = 4

  const { y: yTitleSectionPosition } = renderTitleSection({
    title: section.name || keys.referencesTitle,
    pdf,
    pdfPosition,
    watermark,
    fontSize: Number(PDF_SIZE_TITLE_ENUMS[currentFontSize]) - 2,
    currentFont,
    formatUpperCase: true,
    paddingLine: 17
  })

  position = yTitleSectionPosition - 2

  if (resumeData?.permittedFields?.references?.value?.length) {
    for (let i = 0; i < resumeData.permittedFields.references.value.length; i++) {
      const item = resumeData.permittedFields.references.value[i] as ReferencesType
      const isShowContent = item.name || item.email

      const isAddPage = position + 20 >= PDF_VIEW_ENUMS.heightCanDraw
      if (isAddPage) {
        addTemplatePage({
          isAdd: true,
          pdf,
          watermark,
          fontSize: PDF_SIZE_TITLE_ENUMS[currentFontSize],
          currentFont
        })
        position = PDF_VIEW_ENUMS.paddingTopForNewPage
      }

      if (isShowContent) {
        const fontSize = currentFontSize

        pdf.setFillColor(DEFAULT_COLOR_PREVIEW_TEMPLATE).circle(PDF_VIEW_ENUMS.paddingLeft + 9, position + 5, 2, 'F')

        const name = item.name || keys.undefined
        pdf.setFont(currentFont, 'medium')
        pdf.setFontSize(fontSize)
        const { h, w: wName } = pdf.getTextDimensions(name, {
          fontSize,
          maxWidth: PDF_VIEW_ENUMS.widthAfterRemovePadding
        })

        pdf.setFont(currentFont, 'medium').text(name, PDF_VIEW_ENUMS.paddingLeft + 18, position, {
          align: 'left',
          maxWidth: PDF_VIEW_ENUMS.widthAfterRemovePadding,
          baseline: 'top'
        })

        if (item.email) {
          pdf.setFont(currentFont, 'normal')

          pdf
            .setFillColor(DEFAULT_COLOR_PREVIEW_TEMPLATE)
            .text(`- ${item.email}`, PDF_VIEW_ENUMS.paddingLeft + wName + 18 + 2, position, {
              align: 'left',
              maxWidth: PDF_VIEW_ENUMS.widthAfterRemovePadding,
              baseline: 'top'
            })
        }

        position += h + (PDF_HEIGHT_ENUMS[currentFontSize] || 2)
      }

      if (i < resumeData.permittedFields.references.value?.length - 1) {
        position += 9
      }
    }
  }

  const MARGIN_SECTION = 23
  return { y: position + MARGIN_SECTION }
}

const mapSectionToPdf = (
  params: {
    section: ISectionCustomFieldParamType
    percentScale: number
    keys: IKeyTemplate
    pdfConfig: {
      pdf: jsPDF
      pdfPosition: number
      watermark: string
      dateFormatContent?: ISelectOption
    }
  },
  resumeData?: IResume,
  currentFontSize?: number,
  customFieldViewData?: CustomFieldViewType[],
  extraData?: {
    [type: string]: ISelectOption[]
  },
  locale?: string,
  currentFont?: string
) => {
  const mergedConfig = {
    keys: params.keys,
    resumeData,
    section: params.section,
    percentScale: params.percentScale,
    pdf: params.pdfConfig.pdf,
    pdfPosition: params.pdfConfig.pdfPosition,
    watermark: params.pdfConfig.watermark,
    currentFontSize,
    currentFont
  }

  if (params.section.key === LIST_SECTIONS_DEFAULT.Summary) {
    return renderSummary(mergedConfig)
  }

  if (params.section.key === LIST_SECTIONS_DEFAULT.WorkExperiences) {
    return renderWorkExperiences({
      ...mergedConfig,
      dateFormatContent: params.pdfConfig?.dateFormatContent
    })
  }

  if (params.section.key === LIST_SECTIONS_DEFAULT.Educations) {
    return renderEducations({
      ...mergedConfig,
      extraData: extraData?.educations,
      dateFormatContent: params.pdfConfig?.dateFormatContent,
      locale: locale || ''
    })
  }

  if (params.section.key === LIST_SECTIONS_DEFAULT.Certificates) {
    return renderCertificates({
      ...mergedConfig,
      dateFormatContent: params.pdfConfig?.dateFormatContent,
      locale
    })
  }

  if (params.section.key === LIST_SECTIONS_DEFAULT.References) {
    return renderReferences(mergedConfig)
  }

  return renderCustomSection({
    ...mergedConfig,
    customFieldViewData,
    extraData,
    dateFormatContent: params.pdfConfig.dateFormatContent
  })
}

const renderTemplateHarvard = (props: {
  user?: IUserInformation
  resumeData?: IResume
  customFieldViewData?: CustomFieldViewType[]
  columns?: ISectionParamType
  locale: string
  keys: IKeyTemplate
  pdf: jsPDF
  pdfPosition: number
  percentScale: number
  listDegrees: ISelectOption[]
  profileLevel: ISelectOption[]
  companyName?: string
  currentFontSize?: number
  dateFormatContent?: ISelectOption
  currentFont?: string
}) => {
  const {
    user,
    pdf,
    pdfPosition: pdfPositionProps,
    locale,
    columns,
    resumeData,
    keys,
    percentScale,
    customFieldViewData,
    listDegrees,
    profileLevel,
    companyName,
    currentFontSize = 12,
    dateFormatContent,
    currentFont = DEFAULT_FONT
  } = props

  let pdfPosition = pdfPositionProps
  const watermark = columns?.watermarkEnabling && companyName ? companyName : ''

  addWatermark({
    pdf,
    watermark,
    fontSize: 19,
    currentFont
  })

  pdf.setFont(currentFont, 'normal')
  pdf.setTextColor('18191E')

  // Start rendered
  // Step 1 - Template Header
  const { y } = renderTemplateHeader({
    user,
    pdf,
    pdfPosition,
    locale,
    configHide: {
      isDefault: columns?.isDefault || false,
      templateNameEnabling: columns?.templateNameEnabling || false,
      dateEnabling: columns?.dateEnabling || false,
      profileIdEnabling: columns?.profileIdEnabling || false,
      fullnameEnabling: columns?.fullnameEnabling || false,
      avatarEnabling: columns?.avatarEnabling || false,
      logoEnabling: columns?.logoEnabling || false,
      watermarkEnabling: columns?.watermarkEnabling || false,
      emailEnabling: columns?.emailEnabling || false,
      phoneNumberEnabling: columns?.phoneNumberEnabling || false
    },
    templateName: columns?.templateName,
    resumeData,
    keys,
    currentFontSize,
    currentFont,
    dateFormatContent
  })

  pdfPosition = y

  // Step 2 - Dynamic field cvTemplateSections
  if (columns?.cvTemplateSections?.length) {
    for (let i = 0; i < columns.cvTemplateSections.length; i++) {
      const section = columns.cvTemplateSections[i]

      const { y: ySectionPosition } = mapSectionToPdf(
        {
          section: section as ISectionCustomFieldParamType,
          percentScale,
          keys,
          pdfConfig: { pdf, pdfPosition, watermark, dateFormatContent }
        },
        resumeData,
        currentFontSize,
        customFieldViewData,
        {
          educations: listDegrees,
          profileLevel
        },
        locale,
        currentFont
      )

      pdfPosition = ySectionPosition
    }
  }
}

export { renderTemplateHarvard }
