// @ts-nocheck - doesn't need to fix
import type { jsPD<PERSON> } from 'jspdf'

import type { IUserInformation } from '~/core/@types/global'
import { getShortName } from '~/core/ui/Avatar'
import type { ISelectOption } from '~/core/ui/Select'
import { monthYearTemplateFormatDate } from '~/core/utilities/format-date'

import type {
  CertificatesType,
  EducationsType,
  ReferencesType,
  WorkExperiencesType
} from '~/lib/features/candidates/types'
import {
  checkCertificateFieldFormatDate,
  getCertificateFieldFormatDate,
  getEducationFieldFormatDate,
  getWorkExpFieldFormatDate
} from '~/lib/features/candidates/utilities/format'
import { mappingCustomFieldKind } from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'
import type { CustomFieldViewType } from '~/lib/features/settings/profile-fields/types/custom-field'
import type {
  IResume,
  ISectionCustomFieldParamType,
  ISectionParamType
} from '~/lib/features/settings/profiles/edit/types'
import { checkShowSectionName, getFormatDegreeEducation } from '~/lib/features/settings/profiles/edit/utilities'
import {
  DEFAULT_FONT,
  INTER_FONT,
  LIST_SECTIONS_DEFAULT,
  MINCHO_FONT,
  PDF_HEIGHT_ENUMS,
  PDF_LEFT_SPACING_ENUMS,
  PDF_SIZE_FULL_NAME_ENUMS,
  PDF_SIZE_TITLE_ENUMS,
  PDF_VIEW_ENUMS
} from '~/lib/features/settings/profiles/edit/utilities/enum'

import type { IKeyTemplate } from '../../KeysTemplate/KeysTemplate'
import addTemplatePage from '../utilities/addTemplatePage'
import addWatermark from '../utilities/addWatermark'
import renderDataByKey from '../utilities/dataByKey'
import renderFieldSection from '../utilities/fieldSection'
import formatValueCustomField from '../utilities/formatValueCustomField'
import renderTitleSection from '../utilities/titleSection'

const renderTemplateHeader = ({
  user,
  pdf,
  pdfPosition,
  locale,
  configHide,
  templateName,
  resumeData,
  keys,
  currentFontSize,
  currentFont = DEFAULT_FONT
}: {
  user?: IUserInformation
  pdf: jsPDF
  pdfPosition: number
  locale: string
  configHide?: {
    isDefault: boolean // param only apply for FE
    templateNameEnabling: boolean
    dateEnabling: boolean
    profileIdEnabling: boolean
    fullnameEnabling: boolean
    avatarEnabling: boolean
    logoEnabling: boolean
    watermarkEnabling: boolean
    emailEnabling: boolean
    phoneNumberEnabling: boolean
  }
  templateName?: string
  resumeData?: IResume
  keys: IKeyTemplate
  currentFontSize?: number
  currentFont?: string
}) => {
  let position = pdfPosition
  const AVATAR_SIZE = 113
  const LOGO_SIZE = 72

  const paddingTopLogoEnabling = configHide?.logoEnabling ? LOGO_SIZE + 8 + 3 : 0
  const textDate = `${monthYearTemplateFormatDate(new Date(), locale)}${locale === 'ja' ? ` ${keys.present}` : ''}`
  const fontSizeDate = 12
  pdf.setFont(currentFont, 'normal')
  pdf.setFontSize(fontSizeDate)
  const { h: hTextDate } = pdf.getTextDimensions(textDate, {
    fontSize: fontSizeDate,
    maxWidth: AVATAR_SIZE
  })
  const paddingTopDateEnabling = configHide?.dateEnabling ? hTextDate + 3 : 0
  const paddingTopAvatarEnabling = configHide?.avatarEnabling ? AVATAR_SIZE : 0
  const heightOfRightTemplate = paddingTopLogoEnabling + paddingTopDateEnabling + paddingTopAvatarEnabling

  const fontSizeTemplateName = 24
  const maxWidthTextTemplateName =
    PDF_VIEW_ENUMS.width - PDF_VIEW_ENUMS.paddingLeft - PDF_VIEW_ENUMS.paddingRight - AVATAR_SIZE - 15
  pdf.setFont(currentFont, 'normal')
  pdf.setFontSize(fontSizeTemplateName)
  const { h: hTemplateName } = pdf.getTextDimensions(templateName || '', {
    fontSize: fontSizeTemplateName,
    maxWidth: maxWidthTextTemplateName
  })

  const fullName = resumeData?.permittedFields?.fullName?.value
  const fontSizeFullName = PDF_SIZE_FULL_NAME_ENUMS[String(currentFontSize)] || 16
  const maxWidthTextFullName =
    PDF_VIEW_ENUMS.width - PDF_VIEW_ENUMS.paddingLeft - PDF_VIEW_ENUMS.paddingRight - AVATAR_SIZE - 15
  pdf.setFont(currentFont, 'normal')
  pdf.setFontSize(fontSizeFullName)
  const { h: hFullName } = pdf.getTextDimensions(fullName || '', {
    fontSize: fontSizeFullName,
    maxWidth: maxWidthTextFullName
  })

  const textProfileId = `${keys.id} ${resumeData?.id}`
  const fontSizeProfileId = 12
  const maxWidthTextProfileId =
    PDF_VIEW_ENUMS.width - PDF_VIEW_ENUMS.paddingLeft - PDF_VIEW_ENUMS.paddingRight - AVATAR_SIZE - 15
  pdf.setFont(currentFont, 'normal')
  pdf.setFontSize(fontSizeProfileId)
  const { h: hProfileId } = pdf.getTextDimensions(textProfileId, {
    fontSize: fontSizeProfileId,
    maxWidth: maxWidthTextProfileId
  })
  const paddingTopFullNameEnabling = configHide?.fullnameEnabling ? hFullName + 10 : 0
  const paddingTopProfileIdEnabling = configHide?.profileIdEnabling ? hProfileId + 8 : 0
  const heightOfLeftTemplate = hTemplateName + 10 + paddingTopFullNameEnabling + paddingTopProfileIdEnabling

  // Case 1
  if (configHide?.logoEnabling === false && configHide?.avatarEnabling === false) {
    if (templateName) {
      pdf
        .setFontSize(fontSizeTemplateName)
        .text(templateName, PDF_VIEW_ENUMS.paddingLeft, PDF_VIEW_ENUMS.paddingTop + 5, {
          align: 'left',
          maxWidth: maxWidthTextTemplateName,
          baseline: 'top'
        })

      position = PDF_VIEW_ENUMS.paddingTop + PDF_VIEW_ENUMS.marginSection + hTemplateName + 10
    }

    if (fullName && configHide?.fullnameEnabling) {
      pdf.setFontSize(fontSizeFullName).text(fullName, PDF_VIEW_ENUMS.paddingLeft, position, {
        align: 'left',
        maxWidth: maxWidthTextFullName,
        baseline: 'top'
      })

      position += hFullName + 10
    }

    if (configHide?.profileIdEnabling) {
      pdf
        .setFontSize(fontSizeProfileId)
        .setTextColor('333645')
        .text(textProfileId, PDF_VIEW_ENUMS.paddingLeft, position, {
          align: 'left',
          maxWidth: maxWidthTextProfileId,
          baseline: 'top'
        })

      position += hProfileId + 8
    }

    if (configHide?.dateEnabling) {
      const calcHeightOfCenterY = Math.max(0, (position - PDF_VIEW_ENUMS.paddingTop) / 2 - 6)
      const centerY = PDF_VIEW_ENUMS.paddingTop + calcHeightOfCenterY

      pdf.setFontSize(12).setTextColor('333645').text(textDate, PDF_VIEW_ENUMS.widthCanDraw, centerY, {
        align: 'right',
        maxWidth: AVATAR_SIZE,
        baseline: 'top'
      })
    }
  } else {
    if (heightOfRightTemplate < heightOfLeftTemplate) {
      if (templateName) {
        pdf
          .setFontSize(fontSizeTemplateName)
          .text(templateName, PDF_VIEW_ENUMS.paddingLeft, PDF_VIEW_ENUMS.paddingTop + 5, {
            align: 'left',
            maxWidth: maxWidthTextTemplateName,
            baseline: 'top'
          })

        position = PDF_VIEW_ENUMS.paddingTop + PDF_VIEW_ENUMS.marginSection + hTemplateName + 10
      }

      if (fullName && configHide?.fullnameEnabling) {
        pdf.setFontSize(fontSizeFullName).text(fullName, PDF_VIEW_ENUMS.paddingLeft, position, {
          align: 'left',
          maxWidth: maxWidthTextFullName,
          baseline: 'top'
        })

        position += hFullName + 10
      }

      if (configHide?.profileIdEnabling) {
        pdf
          .setFontSize(fontSizeProfileId)
          .setTextColor('333645')
          .text(textProfileId, PDF_VIEW_ENUMS.paddingLeft, position, {
            align: 'left',
            maxWidth: maxWidthTextProfileId,
            baseline: 'top'
          })

        position += hProfileId + 8
      }

      if (configHide?.logoEnabling) {
        const logoBase64 = resumeData?.logoBase64 || ''
        if (logoBase64) {
          const imageProps = pdf.getImageProperties(logoBase64 as string)
          const pdfHeight = LOGO_SIZE
          const pdfWidth = (imageProps.width * pdfHeight) / imageProps.height

          pdf.addImage({
            imageData: logoBase64 as string,
            format: imageProps.fileType,
            x: PDF_VIEW_ENUMS.widthCanDraw - LOGO_SIZE + (pdfHeight - pdfWidth),
            y: PDF_VIEW_ENUMS.paddingTop,
            width: pdfWidth,
            height: pdfHeight
          })
        } else {
          const shortName = getShortName(String(user?.currentTenant?.name))

          pdf
            .setLineWidth(1)
            .setDrawColor('d4dae0')
            .roundedRect(PDF_VIEW_ENUMS.widthCanDraw - LOGO_SIZE, PDF_VIEW_ENUMS.paddingTop, LOGO_SIZE, LOGO_SIZE, 4, 4)

          pdf.setFontSize(19)
          pdf.setFont(currentFont, 'medium')
          pdf.setTextColor('1D1E27')
          const { w } = pdf.getTextDimensions(shortName, {
            fontSize: 19
          })
          pdf.text(
            shortName,
            PDF_VIEW_ENUMS.widthCanDraw - LOGO_SIZE / 2 - w / 2,
            PDF_VIEW_ENUMS.paddingTop + LOGO_SIZE / 2 - 9,
            {
              align: 'left',
              maxWidth: LOGO_SIZE,
              baseline: 'top'
            }
          )
        }
      }

      if (configHide?.dateEnabling) {
        pdf
          .setFontSize(12)
          .setTextColor('333645')
          .text(textDate, PDF_VIEW_ENUMS.widthCanDraw, PDF_VIEW_ENUMS.paddingTop + paddingTopLogoEnabling, {
            align: 'right',
            maxWidth: AVATAR_SIZE,
            baseline: 'top'
          })
      }

      if (configHide?.avatarEnabling) {
        const avatarBase64 = resumeData?.avatarBase64 || ''
        if (avatarBase64) {
          const imageProps = pdf.getImageProperties(avatarBase64 as string)
          const pdfHeight = AVATAR_SIZE
          const pdfWidth = (imageProps.width * pdfHeight) / imageProps.height

          pdf.addImage({
            imageData: avatarBase64 as string,
            format: imageProps.fileType,
            x: PDF_VIEW_ENUMS.widthCanDraw - AVATAR_SIZE + (pdfHeight - pdfWidth),
            y: PDF_VIEW_ENUMS.paddingTop + paddingTopLogoEnabling + paddingTopDateEnabling,
            width: pdfWidth,
            height: pdfHeight
          })
        } else {
          const shortName = getShortName(
            String(resumeData?.permittedFields?.fullName?.value || resumeData?.permittedFields?.email?.value?.[0])
          )

          pdf
            .setLineWidth(1)
            .setDrawColor('d4dae0')
            .roundedRect(
              PDF_VIEW_ENUMS.widthCanDraw - AVATAR_SIZE,
              PDF_VIEW_ENUMS.paddingTop + paddingTopLogoEnabling + paddingTopDateEnabling,
              AVATAR_SIZE,
              AVATAR_SIZE,
              4,
              4
            )

          pdf.setFontSize(19)
          pdf.setFont(currentFont, 'medium')
          pdf.setTextColor('1D1E27')
          const { w } = pdf.getTextDimensions(shortName, {
            fontSize: 19
          })
          pdf.text(
            shortName,
            PDF_VIEW_ENUMS.widthCanDraw - AVATAR_SIZE / 2 - w / 2,
            PDF_VIEW_ENUMS.paddingTop + paddingTopLogoEnabling + paddingTopDateEnabling + AVATAR_SIZE / 2 - 9,
            {
              align: 'left',
              maxWidth: AVATAR_SIZE,
              baseline: 'top'
            }
          )
        }
      }
    } else {
      if (templateName) {
        const calcHeightOfCenterY = Math.max(
          0,
          heightOfRightTemplate / 2 -
            (configHide?.fullnameEnabling ? hFullName / 2 : 0) -
            (configHide?.profileIdEnabling ? hProfileId / 2 : 0) -
            hTemplateName / 2
        )
        if (fullName) {
          const centerY = PDF_VIEW_ENUMS.paddingTop + calcHeightOfCenterY - 16

          pdf.setFontSize(fontSizeTemplateName).text(templateName, PDF_VIEW_ENUMS.paddingLeft, centerY, {
            align: 'left',
            maxWidth: maxWidthTextTemplateName,
            baseline: 'top',
            lineHeightFactor: 1.4
          })
        }
      }

      if (configHide?.fullnameEnabling) {
        const calcHeightOfCenterY = Math.max(
          0,
          heightOfRightTemplate / 2 +
            hTemplateName / 2 -
            (configHide?.profileIdEnabling ? hProfileId / 2 : 0) -
            hFullName / 2
        )
        if (fullName) {
          const centerY = PDF_VIEW_ENUMS.paddingTop + calcHeightOfCenterY

          pdf.setFontSize(fontSizeFullName).text(fullName, PDF_VIEW_ENUMS.paddingLeft, centerY, {
            align: 'left',
            maxWidth: maxWidthTextFullName,
            baseline: 'top',
            lineHeightFactor: 0.9
          })
        }
      }

      if (configHide?.profileIdEnabling) {
        const calcHeightOfCenterY = Math.max(
          0,
          heightOfRightTemplate / 2 +
            hTemplateName / 2 +
            (configHide?.fullnameEnabling ? hFullName / 2 : 0) -
            hProfileId / 2
        )
        const centerY = PDF_VIEW_ENUMS.paddingTop + calcHeightOfCenterY

        pdf
          .setFontSize(fontSizeProfileId)
          .setTextColor('333645')
          .text(textProfileId, PDF_VIEW_ENUMS.paddingLeft, centerY, {
            align: 'left',
            maxWidth: maxWidthTextProfileId,
            baseline: 'top',
            lineHeightFactor: 0.3
          })
      }

      if (configHide?.logoEnabling) {
        const logoBase64 = resumeData?.logoBase64 || ''

        if (logoBase64) {
          const imageProps = pdf.getImageProperties(logoBase64 as string)
          const pdfHeight = LOGO_SIZE
          const pdfWidth = (imageProps.width * pdfHeight) / imageProps.height

          pdf.addImage({
            imageData: logoBase64 as string,
            format: imageProps.fileType,
            x: PDF_VIEW_ENUMS.widthCanDraw - LOGO_SIZE + (pdfHeight - pdfWidth),
            y: PDF_VIEW_ENUMS.paddingTop,
            width: pdfWidth,
            height: pdfHeight
          })
        } else {
          const shortName = getShortName(String(user?.currentTenant?.name))

          pdf
            .setLineWidth(1)
            .setDrawColor('d4dae0')
            .roundedRect(PDF_VIEW_ENUMS.widthCanDraw - LOGO_SIZE, PDF_VIEW_ENUMS.paddingTop, LOGO_SIZE, LOGO_SIZE, 4, 4)

          pdf.setFontSize(19)
          pdf.setFont(currentFont, 'medium')
          pdf.setTextColor('1D1E27')
          const { w } = pdf.getTextDimensions(shortName, {
            fontSize: 19
          })
          pdf.text(
            shortName,
            PDF_VIEW_ENUMS.widthCanDraw - LOGO_SIZE / 2 - w / 2,
            PDF_VIEW_ENUMS.paddingTop + LOGO_SIZE / 2 - 9,
            {
              align: 'left',
              maxWidth: LOGO_SIZE,
              baseline: 'top'
            }
          )
        }
      }

      if (configHide?.dateEnabling) {
        pdf
          .setFontSize(12)
          .setTextColor('333645')
          .text(textDate, PDF_VIEW_ENUMS.widthCanDraw, PDF_VIEW_ENUMS.paddingTop + paddingTopLogoEnabling, {
            align: 'right',
            maxWidth: AVATAR_SIZE,
            baseline: 'top'
          })
      }

      if (configHide?.avatarEnabling) {
        const avatarBase64 = resumeData?.avatarBase64 || ''
        if (avatarBase64) {
          const imageProps = pdf.getImageProperties(avatarBase64 as string)
          const pdfHeight = AVATAR_SIZE
          const pdfWidth = (imageProps.width * pdfHeight) / imageProps.height

          pdf.addImage({
            imageData: avatarBase64 as string,
            format: imageProps.fileType,
            x: PDF_VIEW_ENUMS.widthCanDraw - AVATAR_SIZE + (pdfHeight - pdfWidth),
            y: PDF_VIEW_ENUMS.paddingTop + paddingTopLogoEnabling + paddingTopDateEnabling,
            width: pdfWidth,
            height: pdfHeight
          })
        } else {
          const shortName = getShortName(
            String(resumeData?.permittedFields?.fullName?.value || resumeData?.permittedFields?.email?.value?.[0])
          )

          pdf
            .setLineWidth(1)
            .setDrawColor('d4dae0')
            .roundedRect(
              PDF_VIEW_ENUMS.widthCanDraw - AVATAR_SIZE,
              PDF_VIEW_ENUMS.paddingTop + paddingTopLogoEnabling + paddingTopDateEnabling,
              AVATAR_SIZE,
              AVATAR_SIZE,
              4,
              4
            )

          pdf.setFontSize(19)
          pdf.setFont(currentFont, 'medium')
          pdf.setTextColor('1D1E27')
          const { w } = pdf.getTextDimensions(shortName, {
            fontSize: 19
          })
          pdf.text(
            shortName,
            PDF_VIEW_ENUMS.widthCanDraw - AVATAR_SIZE / 2 - w / 2,
            PDF_VIEW_ENUMS.paddingTop + paddingTopLogoEnabling + paddingTopDateEnabling + AVATAR_SIZE / 2 - 9,
            {
              align: 'left',
              maxWidth: AVATAR_SIZE,
              baseline: 'top'
            }
          )
        }
      }

      position = PDF_VIEW_ENUMS.paddingTop + heightOfRightTemplate + PDF_VIEW_ENUMS.marginSection
    }
  }

  return { y: position }
}

const renderCustomSection = ({
  section,
  resumeData,
  percentScale,
  customFieldViewData,
  keys,
  extraData,
  pdf,
  pdfPosition,
  watermark,
  currentFontSize = 12,
  dateFormatContent,
  currentFont
}: {
  section: ISectionCustomFieldParamType
  resumeData?: IResume
  percentScale: number
  customFieldViewData?: CustomFieldViewType[]
  keys: IKeyTemplate
  extraData?: {
    [key: string]: ISelectOption[]
  }
  pdf: jsPDF
  pdfPosition: number
  watermark: string
  currentFontSize?: number
  dateFormatContent?: ISelectOption
  currentFont?: string
}) => {
  const mappingsField = (customFieldViewData || [])?.filter((item) => item.visibleToEmployeeProfile && item.visibility)

  let position = pdfPosition

  if (checkShowSectionName({ section, resumeData, mappingsField })) {
    const { y: yTitleSectionPosition } = renderTitleSection({
      title: section.name || '',
      pdf,
      pdfPosition,
      watermark,
      fontSize: PDF_SIZE_TITLE_ENUMS[currentFontSize],
      currentFont
    })

    position = yTitleSectionPosition
  }

  if (section.cvTemplateCustomFields?.length === 1) {
    const isShowCustomField =
      section.cvTemplateCustomFields?.[0]?.isCustom === true &&
      section.cvTemplateCustomFields?.[0]?.visibility &&
      section.cvTemplateCustomFields?.[0]?.visibleToEmployeeProfile
    const isShowDefaultField = section.cvTemplateCustomFields?.[0]?.isCustom === false

    if (isShowCustomField) {
      const { y } = renderFieldSection({
        currentFontSize,
        title: '',
        content: formatValueCustomField({
          value: !['select', 'multiple'].includes(
            mappingCustomFieldKind(section.cvTemplateCustomFields?.[0].fieldKind || 'text')
          )
            ? resumeData?.customFields?.find(
                (item) => String(item.customSettingId) === String(section.cvTemplateCustomFields?.[0].customSettingId)
              )?.value
            : resumeData?.customFields?.find(
                (item) => String(item.customSettingId) === String(section.cvTemplateCustomFields?.[0].customSettingId)
              )?.selectedOptionKeys,
          fieldKind: mappingCustomFieldKind(section.cvTemplateCustomFields?.[0].fieldKind || 'text'),
          selectOptions: mappingsField?.find(
            (item) => String(item.id) === String(section.cvTemplateCustomFields?.[0].customSettingId)
          )?.selectOptions,
          keys,
          dateFormatContent
        }),
        percentScale,
        hideTitle: true,
        pdf,
        pdfPosition: position,
        watermark,
        height: 12,
        line: 1,
        currentFont
      })

      position = y
    }

    if (isShowDefaultField) {
      const { y } = renderDataByKey({
        currentFontSize,
        key: section.cvTemplateCustomFields?.[0]?.key || '',
        resumeData,
        percentScale,
        hideTitle: true,
        keys,
        extraData,
        validateHidden: true,
        customRelatedFields: section.cvTemplateCustomFields?.[0]?.customRelatedFields,
        pdf,
        pdfPosition: position,
        watermark,
        dateFormatContent,
        currentFont
      })

      position = y
    }
  } else {
    if (section.cvTemplateCustomFields?.length) {
      for (let i = 0; i < section.cvTemplateCustomFields.length; i++) {
        const field = section.cvTemplateCustomFields[i]

        if (field.isCustom && field.visibility && field.visibleToEmployeeProfile) {
          const value = !['select', 'multiple'].includes(mappingCustomFieldKind(field.fieldKind || 'text'))
            ? resumeData?.customFields?.find((item) => String(item.customSettingId) === String(field.customSettingId))
                ?.value
            : resumeData?.customFields?.find((item) => String(item.customSettingId) === String(field.customSettingId))
                ?.selectedOptionKeys
          const findCustomView = mappingsField?.find((item) => String(item.id) === String(field.customSettingId))

          const { y } = renderFieldSection({
            currentFontSize,
            title: field?.fieldName || '',
            content: formatValueCustomField({
              value,
              fieldKind: mappingCustomFieldKind(field.fieldKind || 'text'),
              selectOptions: findCustomView?.selectOptions,
              keys,
              dateFormatContent
            }),
            percentScale,
            pdf,
            pdfPosition: position,
            watermark,
            height: 12,
            line: 1,
            currentFont
          })

          position = y
        } else {
          const { y } = renderDataByKey({
            currentFontSize,
            dateFormatContent,
            key: field?.key || '',
            resumeData,
            percentScale,
            keys,
            extraData,
            validateHidden: true,
            customRelatedFields: field?.customRelatedFields,
            pdf,
            pdfPosition: position,
            watermark,
            currentFont
          })

          position = y
        }
      }
    }
  }

  const MARGIN_SECTION = 0
  return { y: position + MARGIN_SECTION }
}

const renderSummary = ({
  section,
  resumeData,
  percentScale,
  keys,
  pdf,
  pdfPosition,
  watermark,
  currentFontSize = 12,
  currentFont
}: {
  section: ISectionCustomFieldParamType
  resumeData?: IResume
  percentScale: number
  keys: IKeyTemplate
  pdf: jsPDF
  pdfPosition: number
  watermark: string
  currentFontSize?: number
  currentFont?: string
}) => {
  if (!resumeData?.permittedFields?.summary) return { y: pdfPosition }

  let position = pdfPosition

  const { y: yTitleSectionPosition } = renderTitleSection({
    title: section.name || keys.summaryTitle,
    pdf,
    pdfPosition,
    watermark,
    fontSize: PDF_SIZE_TITLE_ENUMS[currentFontSize],
    currentFont
  })

  position = yTitleSectionPosition

  if (resumeData?.permittedFields?.summary?.value) {
    const { y } = renderFieldSection({
      currentFontSize,
      title: '',
      content: resumeData.permittedFields.summary.value,
      hideTitle: true,
      percentScale,
      pdf,
      pdfPosition: position,
      watermark,
      height: 12,
      line: 1,
      currentFont
    })

    position = y
  }

  const MARGIN_SECTION = 0
  return { y: position + MARGIN_SECTION }
}

const renderWorkExperiences = ({
  resumeData,
  percentScale,
  keys,
  section,
  pdf,
  pdfPosition,
  watermark,
  currentFontSize = 12,
  dateFormatContent,
  currentFont = DEFAULT_FONT
}: {
  resumeData?: IResume
  percentScale: number
  keys: IKeyTemplate
  section: ISectionCustomFieldParamType
  pdf: jsPDF
  pdfPosition: number
  watermark: string
  currentFontSize?: number
  dateFormatContent?: ISelectOption
  currentFont?: string
}) => {
  if (!resumeData?.permittedFields?.workExperiences) return { y: pdfPosition }

  let position = pdfPosition
  const MARGIN_SECTION = 0
  const MARGIN_ITEM = 4
  const LEFT_ITEM = [MINCHO_FONT, INTER_FONT].includes(currentFont)
    ? PDF_LEFT_SPACING_ENUMS[currentFontSize] + 16
    : PDF_LEFT_SPACING_ENUMS[currentFontSize] || 112

  const { y: yTitleSectionPosition } = renderTitleSection({
    title: section.name || keys.workExperiencesTitle,
    pdf,
    pdfPosition,
    watermark,
    fontSize: PDF_SIZE_TITLE_ENUMS[currentFontSize],
    currentFont
  })

  position = yTitleSectionPosition

  if (resumeData?.permittedFields?.workExperiences?.value?.length) {
    for (let i = 0; i < resumeData.permittedFields.workExperiences.value.length; i++) {
      const item = resumeData.permittedFields.workExperiences.value[i] as WorkExperiencesType
      const isShowDateTime =
        getWorkExpFieldFormatDate({
          type: 'from',
          attributes: item,
          isTemplate: true,
          dateFormatContent: dateFormatContent?.value
        }) && section.customRelatedFields?.datetime
      const isShowTitle = item.title && section.customRelatedFields?.title
      const isShowCompany = item.company && section.customRelatedFields?.company
      const isShowLocation = item.location && section.customRelatedFields?.location
      const isShowDescription = item.description && section.customRelatedFields?.description

      const isAddPage = position + 20 >= PDF_VIEW_ENUMS.heightCanDraw
      if (isAddPage) {
        addTemplatePage({
          isAdd: true,
          pdf,
          watermark,
          fontSize: PDF_SIZE_TITLE_ENUMS[currentFontSize],
          currentFont
        })
        position = PDF_VIEW_ENUMS.paddingTopForNewPage
      }

      let configShowDateTime: {
        fontSize: number
        content: string
        isChange: boolean
      } = {
        fontSize: currentFontSize,
        content: '',
        isChange: false
      }
      if (isShowDateTime) {
        const content = `${getWorkExpFieldFormatDate({
          type: 'from',
          attributes: item,
          isTemplate: true,
          dateFormatContent: dateFormatContent?.value,
          keys: {
            undefined: keys.undefined
          }
        })}${
          item?.currentWorking
            ? ` - ${keys.present}`
            : ` - ${getWorkExpFieldFormatDate({
                type: 'to',
                attributes: item,
                isTemplate: true,
                dateFormatContent: dateFormatContent?.value,
                keys: {
                  undefined: keys.undefined
                }
              })}`
        }`
        const fontSize = currentFontSize
        const maxWidth = PDF_VIEW_ENUMS.widthAfterRemovePadding - (isShowDateTime ? LEFT_ITEM : 0)

        pdf.setFont(currentFont, 'normal').setFontSize(fontSize).text(content, PDF_VIEW_ENUMS.paddingLeft, position, {
          align: 'left',
          maxWidth,
          baseline: 'top'
        })

        configShowDateTime = {
          fontSize,
          content,
          isChange: false
        }
      }

      if (isShowTitle || isShowCompany) {
        const fontSize = currentFontSize
        const maxWidth = PDF_VIEW_ENUMS.widthAfterRemovePadding - (isShowDateTime ? LEFT_ITEM : 0)
        const content = `${isShowTitle ? item.title : ''}${
          section.customRelatedFields?.company
            ? isShowTitle && item.company
              ? ` · ${item.company}`
              : item.company
            : ''
        }`

        pdf.setFont(currentFont, 'medium')
        pdf.setFontSize(fontSize)
        const { h } = pdf.getTextDimensions(content, {
          fontSize,
          maxWidth
        })

        pdf
          .setFont(currentFont, 'medium')
          .setFontSize(fontSize)
          .text(content, (isShowDateTime ? LEFT_ITEM : 0) + PDF_VIEW_ENUMS.paddingLeft, position, {
            align: 'left',
            maxWidth,
            baseline: 'top'
          })

        configShowDateTime.isChange = true
        position += h + (PDF_HEIGHT_ENUMS[currentFontSize] || 2)
      }

      if (isShowLocation) {
        const fontSize = currentFontSize
        const maxWidth = PDF_VIEW_ENUMS.widthAfterRemovePadding - (isShowDateTime ? LEFT_ITEM : 0)
        const formatLocation = item?.location as {
          id?: string
          value?: string
        }
        const content = String((formatLocation?.id ? formatLocation?.value : item.location) || '')

        pdf.setFont(currentFont, 'normal')
        pdf.setFontSize(fontSize)
        const { h } = pdf.getTextDimensions(content || '', {
          fontSize,
          maxWidth
        })

        pdf
          .setFont(currentFont, 'normal')
          .setFontSize(fontSize)
          .setTextColor('333645')
          .text(content, (isShowDateTime ? LEFT_ITEM : 0) + PDF_VIEW_ENUMS.paddingLeft, position, {
            align: 'left',
            maxWidth,
            baseline: 'top'
          })

        configShowDateTime.isChange = true
        position += h + (PDF_HEIGHT_ENUMS[currentFontSize] || 2)
      }

      if (configShowDateTime.isChange === false) {
        pdf.setFont(currentFont, 'normal')
        pdf.setFontSize(configShowDateTime.fontSize)
        const { h } = pdf.getTextDimensions(configShowDateTime.content, {
          fontSize: configShowDateTime.fontSize,
          maxWidth: LEFT_ITEM
        })

        position += h + (PDF_HEIGHT_ENUMS[currentFontSize] || 2)
      }

      if (isShowDescription) {
        const { y } = renderFieldSection({
          title: '',
          content: item.description,
          hideTitle: true,
          percentScale,
          pdf,
          pdfPosition: position + 4,
          watermark,
          height: 12,
          line: 1,
          currentFontSize,
          currentFont
        })

        position = y
      }

      position += MARGIN_ITEM
    }
  }

  return { y: position + MARGIN_SECTION }
}

const renderEducations = ({
  resumeData,
  percentScale,
  keys,
  extraData,
  section,
  pdf,
  pdfPosition,
  watermark,
  currentFontSize = 12,
  dateFormatContent,
  locale,
  currentFont = DEFAULT_FONT
}: {
  resumeData?: IResume
  percentScale: number
  keys: IKeyTemplate
  extraData?: ISelectOption[]
  section: ISectionCustomFieldParamType
  pdf: jsPDF
  pdfPosition: number
  watermark: string
  currentFontSize?: number
  dateFormatContent?: ISelectOption
  locale: string
  currentFont?: string
}) => {
  if (!resumeData?.permittedFields?.educations) return { y: pdfPosition }
  let position = pdfPosition
  const MARGIN_SECTION = 0
  const MARGIN_ITEM = 4
  const LEFT_ITEM = [MINCHO_FONT, INTER_FONT].includes(currentFont)
    ? PDF_LEFT_SPACING_ENUMS[currentFontSize] + 16
    : PDF_LEFT_SPACING_ENUMS[currentFontSize] || 112

  const { y: yTitleSectionPosition } = renderTitleSection({
    title: section.name || keys.educationTitle,
    pdf,
    pdfPosition,
    watermark,
    fontSize: PDF_SIZE_TITLE_ENUMS[currentFontSize],
    currentFont
  })

  position = yTitleSectionPosition

  if (resumeData?.permittedFields?.educations?.value?.length) {
    for (let i = 0; i < resumeData.permittedFields.educations.value.length; i++) {
      const item = resumeData.permittedFields.educations.value[i] as EducationsType
      const isShowDateTime =
        getEducationFieldFormatDate({
          type: 'from',
          attributes: item,
          dateFormatContent: dateFormatContent?.value || '',
          locale
        }) && section.customRelatedFields?.datetime
      const isShowDegree = item.degree && section.customRelatedFields?.degree
      const isShowDegreeSubject = item.degreeSubject && section.customRelatedFields?.major
      const isShowSchool = section.customRelatedFields?.school && item.schoolName
      const isShowDescription = item.description && section.customRelatedFields?.description

      const isAddPage = position + 20 >= PDF_VIEW_ENUMS.heightCanDraw
      if (isAddPage) {
        addTemplatePage({
          isAdd: true,
          pdf,
          watermark,
          fontSize: PDF_SIZE_TITLE_ENUMS[currentFontSize],
          currentFont
        })
        position = PDF_VIEW_ENUMS.paddingTopForNewPage
      }

      let configShowDateTime: {
        fontSize: number
        content: string
        isChange: boolean
      } = {
        fontSize: currentFontSize,
        content: '',
        isChange: false
      }
      if (isShowDateTime) {
        const content = `${getEducationFieldFormatDate({
          type: 'from',
          attributes: item,
          isTemplate: true,
          keys: {
            undefined: keys.undefined
          },
          dateFormatContent: dateFormatContent?.value || '',
          locale
        })} - ${getEducationFieldFormatDate({
          type: 'to',
          attributes: item,
          isTemplate: true,
          keys: {
            undefined: keys.undefined
          },
          dateFormatContent: dateFormatContent?.value || '',
          locale
        })}`
        const fontSize = currentFontSize
        const maxWidth = PDF_VIEW_ENUMS.widthAfterRemovePadding - (isShowDateTime ? LEFT_ITEM : 0)
        pdf.setFont(currentFont, 'normal').setFontSize(fontSize).text(content, PDF_VIEW_ENUMS.paddingLeft, position, {
          align: 'left',
          maxWidth,
          baseline: 'top'
        })

        configShowDateTime = {
          fontSize,
          content,
          isChange: false
        }
      }

      if (isShowDegree || isShowDegreeSubject || isShowSchool) {
        const fontSize = currentFontSize
        const maxWidth = PDF_VIEW_ENUMS.widthAfterRemovePadding - (isShowDateTime ? LEFT_ITEM : 0)
        const isShow = section.customRelatedFields?.degree || section.customRelatedFields?.major
        const content = `${isShowSchool ? item.schoolName : ''}${
          isShow
            ? isShowSchool && (item.degree || item.degreeSubject)
              ? ` · ${getFormatDegreeEducation({
                  currentLanguage: locale,
                  degree: section.customRelatedFields?.degree ? item.degree : undefined,
                  degreeSubject: section.customRelatedFields?.major ? item.degreeSubject : undefined,
                  listDegrees: extraData
                })}`
              : getFormatDegreeEducation({
                  currentLanguage: locale,
                  degree: section.customRelatedFields?.degree ? item.degree : undefined,
                  degreeSubject: section.customRelatedFields?.major ? item.degreeSubject : undefined,
                  listDegrees: extraData
                })
            : ''
        }`

        pdf.setFont(currentFont, 'medium')
        pdf.setFontSize(fontSize)
        const { h } = pdf.getTextDimensions(content, {
          fontSize,
          maxWidth
        })

        pdf
          .setFont(currentFont, 'medium')
          .setFontSize(fontSize)
          .text(content, (isShowDateTime ? LEFT_ITEM : 0) + PDF_VIEW_ENUMS.paddingLeft, position, {
            align: 'left',
            maxWidth,
            baseline: 'top'
          })

        configShowDateTime.isChange = true
        position += h + (PDF_HEIGHT_ENUMS[currentFontSize] || 2)
      }

      if (configShowDateTime.isChange === false) {
        pdf.setFont(currentFont, 'normal')
        pdf.setFontSize(configShowDateTime.fontSize)
        const { h } = pdf.getTextDimensions(configShowDateTime.content, {
          fontSize: configShowDateTime.fontSize,
          maxWidth: LEFT_ITEM
        })

        position += h + (PDF_HEIGHT_ENUMS[currentFontSize] || 2)
      }

      if (isShowDescription) {
        const { y } = renderFieldSection({
          title: '',
          content: item.description,
          hideTitle: true,
          percentScale,
          pdf,
          pdfPosition: position,
          watermark,
          height: 12,
          line: 1,
          currentFontSize,
          currentFont
        })

        position = y
      }

      position += MARGIN_ITEM
    }
  }

  return { y: position + MARGIN_SECTION }
}

const renderCertificates = ({
  resumeData,
  percentScale,
  keys,
  section,
  pdf,
  pdfPosition,
  watermark,
  currentFontSize = 12,
  dateFormatContent,
  currentFont = DEFAULT_FONT,
  locale
}: {
  resumeData?: IResume
  percentScale: number
  keys: IKeyTemplate
  section: ISectionCustomFieldParamType
  pdf: jsPDF
  pdfPosition: number
  watermark: string
  currentFontSize?: number
  dateFormatContent?: ISelectOption
  currentFont?: string
  locale?: string
}) => {
  if (!resumeData?.permittedFields?.certificates) return { y: pdfPosition }

  let position = pdfPosition
  const MARGIN_SECTION = 0
  const MARGIN_ITEM = 4
  const LEFT_ITEM = 64

  const { y: yTitleSectionPosition } = renderTitleSection({
    title: section.name || keys.certificatesTitle,
    pdf,
    pdfPosition,
    watermark,
    fontSize: PDF_SIZE_TITLE_ENUMS[currentFontSize],
    currentFont
  })

  position = yTitleSectionPosition

  if (resumeData?.permittedFields?.certificates?.value?.length) {
    for (let i = 0; i < resumeData.permittedFields.certificates.value.length; i++) {
      const item = resumeData.permittedFields.certificates.value[i] as CertificatesType
      const isShowDateTime = checkCertificateFieldFormatDate({
        attributes: item
      })
      const isShowContent = item.certificateName || item.institution

      const isAddPage = position + 20 >= PDF_VIEW_ENUMS.heightCanDraw
      if (isAddPage) {
        addTemplatePage({
          isAdd: true,
          pdf,
          watermark,
          fontSize: PDF_SIZE_TITLE_ENUMS[currentFontSize],
          currentFont
        })
        position = PDF_VIEW_ENUMS.paddingTopForNewPage
      }

      if (isShowDateTime) {
        const fontSize = currentFontSize
        const contentOfDateTime = getCertificateFieldFormatDate({
          attributes: item,
          isTemplate: true,
          dateFormatContent: dateFormatContent?.value,
          locale
        })
        const maxWidth = PDF_VIEW_ENUMS.widthAfterRemovePadding - (isShowDateTime ? LEFT_ITEM : 0)

        pdf
          .setFont(currentFont, 'normal')
          .setFontSize(fontSize)
          .text(String(contentOfDateTime), PDF_VIEW_ENUMS.paddingLeft, position, {
            align: 'left',
            maxWidth,
            baseline: 'top'
          })
      }

      if (isShowContent) {
        const fontSize = currentFontSize
        const maxWidth = PDF_VIEW_ENUMS.widthAfterRemovePadding - (isShowDateTime ? LEFT_ITEM : 0)

        const content = `${item.certificateName || keys.undefined}${item.institution ? ` · ${item.institution}` : ''}`

        pdf.setFont(currentFont, 'medium')
        pdf.setFontSize(fontSize)
        const { h } = pdf.getTextDimensions(content, {
          fontSize,
          maxWidth
        })

        pdf
          .setFont(currentFont, 'medium')
          .setFontSize(fontSize)
          .text(content, (isShowDateTime ? LEFT_ITEM : 0) + PDF_VIEW_ENUMS.paddingLeft, position, {
            align: 'left',
            maxWidth,
            baseline: 'top'
          })

        position += h + (PDF_HEIGHT_ENUMS[currentFontSize] || 2)
      }

      position += MARGIN_ITEM
    }
  }

  return { y: position + MARGIN_SECTION }
}

const renderReferences = ({
  resumeData,
  percentScale,
  keys,
  section,
  pdf,
  pdfPosition,
  watermark,
  currentFontSize = 12,
  currentFont = DEFAULT_FONT
}: {
  resumeData?: IResume
  percentScale: number
  keys: IKeyTemplate
  section: ISectionCustomFieldParamType
  pdf: jsPDF
  pdfPosition: number
  watermark: string
  currentFontSize?: number
  currentFont?: string
}) => {
  if (!resumeData?.permittedFields?.references) return { y: pdfPosition }

  let position = pdfPosition
  const MARGIN_SECTION = 0
  const MARGIN_ITEM = 4
  const LEFT_ITEM = 64

  const { y: yTitleSectionPosition } = renderTitleSection({
    title: section.name || keys.referencesTitle,
    pdf,
    pdfPosition,
    watermark,
    fontSize: PDF_SIZE_TITLE_ENUMS[currentFontSize],
    currentFont
  })

  position = yTitleSectionPosition

  if (resumeData?.permittedFields?.references?.value?.length) {
    for (let i = 0; i < resumeData.permittedFields.references.value.length; i++) {
      const item = resumeData.permittedFields.references.value[i] as ReferencesType
      const isShowContent = item.name || item.email

      const isAddPage = position + 20 >= PDF_VIEW_ENUMS.heightCanDraw
      if (isAddPage) {
        addTemplatePage({
          isAdd: true,
          pdf,
          watermark,
          fontSize: PDF_SIZE_TITLE_ENUMS[currentFontSize],
          currentFont
        })
        position = PDF_VIEW_ENUMS.paddingTopForNewPage
      }

      if (isShowContent) {
        const fontSize = currentFontSize
        const maxWidth = PDF_VIEW_ENUMS.widthAfterRemovePadding - LEFT_ITEM

        const content = `${item.name || keys.undefined}${item.email ? ` · ${item.email}` : ''}`

        pdf.setFont(currentFont, 'normal')
        pdf.setFontSize(fontSize)
        const { h } = pdf.getTextDimensions(content, {
          fontSize,
          maxWidth
        })

        pdf.setFont(currentFont, 'normal').setFontSize(fontSize).text(content, PDF_VIEW_ENUMS.paddingLeft, position, {
          align: 'left',
          maxWidth,
          baseline: 'top'
        })

        position += h + (PDF_HEIGHT_ENUMS[currentFontSize] || 2)
      }

      position += MARGIN_ITEM
    }
  }

  return { y: position + MARGIN_SECTION }
}

const mapSectionToPdf = (
  params: {
    section: ISectionCustomFieldParamType
    percentScale: number
    keys: IKeyTemplate
    pdfConfig: {
      pdf: jsPDF
      pdfPosition: number
      watermark: string
      dateFormatContent?: ISelectOption
    }
  },
  resumeData?: IResume,
  currentFontSize?: number,
  customFieldViewData?: CustomFieldViewType[],
  extraData?: {
    [type: string]: ISelectOption[]
  },
  locale?: string,
  currentFont?: string
) => {
  const mergedConfig = {
    keys: params.keys,
    resumeData,
    section: params.section,
    percentScale: params.percentScale,
    pdf: params.pdfConfig.pdf,
    pdfPosition: params.pdfConfig.pdfPosition,
    watermark: params.pdfConfig.watermark,
    currentFontSize,
    currentFont
  }

  if (params.section.key === LIST_SECTIONS_DEFAULT.Summary) {
    return renderSummary(mergedConfig)
  }

  if (params.section.key === LIST_SECTIONS_DEFAULT.WorkExperiences) {
    return renderWorkExperiences({
      ...mergedConfig,
      dateFormatContent: params.pdfConfig?.dateFormatContent
    })
  }

  if (params.section.key === LIST_SECTIONS_DEFAULT.Educations) {
    return renderEducations({
      ...mergedConfig,
      extraData: extraData?.educations,
      dateFormatContent: params.pdfConfig?.dateFormatContent,
      locale: locale || ''
    })
  }

  if (params.section.key === LIST_SECTIONS_DEFAULT.Certificates) {
    return renderCertificates({
      ...mergedConfig,
      dateFormatContent: params.pdfConfig?.dateFormatContent,
      locale
    })
  }

  if (params.section.key === LIST_SECTIONS_DEFAULT.References) {
    return renderReferences(mergedConfig)
  }

  return renderCustomSection({
    ...mergedConfig,
    customFieldViewData,
    extraData,
    dateFormatContent: params.pdfConfig.dateFormatContent
  })
}

const renderTemplateLinkedin = (props: {
  user?: IUserInformation
  resumeData?: IResume
  customFieldViewData?: CustomFieldViewType[]
  columns?: ISectionParamType
  locale: string
  keys: IKeyTemplate
  pdf: jsPDF
  pdfPosition: number
  percentScale: number
  listDegrees: ISelectOption[]
  profileLevel: ISelectOption[]
  companyName?: string
  currentFontSize?: number
  dateFormatContent?: ISelectOption
  currentFont?: string
}) => {
  const {
    user,
    pdf,
    pdfPosition: pdfPositionProps,
    locale,
    columns,
    resumeData,
    keys,
    percentScale,
    customFieldViewData,
    listDegrees,
    profileLevel,
    companyName,
    currentFontSize = 12,
    dateFormatContent,
    currentFont = DEFAULT_FONT
  } = props

  let pdfPosition = pdfPositionProps
  const watermark = columns?.watermarkEnabling && companyName ? companyName : ''

  addWatermark({
    pdf,
    watermark,
    fontSize: 19,
    currentFont
  })

  pdf.setFont(currentFont, 'normal')
  pdf.setTextColor('18191E')

  // Start rendered
  // Step 1 - Template Header
  const { y } = renderTemplateHeader({
    user,
    pdf,
    pdfPosition,
    locale,
    configHide: {
      isDefault: columns?.isDefault || false,
      templateNameEnabling: columns?.templateNameEnabling || false,
      dateEnabling: columns?.dateEnabling || false,
      profileIdEnabling: columns?.profileIdEnabling || false,
      fullnameEnabling: columns?.fullnameEnabling || false,
      avatarEnabling: columns?.avatarEnabling || false,
      logoEnabling: columns?.logoEnabling || false,
      watermarkEnabling: columns?.watermarkEnabling || false,
      emailEnabling: columns?.emailEnabling || false,
      phoneNumberEnabling: columns?.phoneNumberEnabling || false
    },
    templateName: columns?.templateName,
    resumeData,
    keys,
    currentFontSize,
    currentFont
  })

  pdfPosition = y

  // Step 2 - Dynamic field cvTemplateSections
  if (columns?.cvTemplateSections?.length) {
    for (let i = 0; i < columns.cvTemplateSections.length; i++) {
      const section = columns.cvTemplateSections[i]

      const { y: ySectionPosition } = mapSectionToPdf(
        {
          section,
          percentScale,
          keys,
          pdfConfig: { pdf, pdfPosition, watermark, dateFormatContent }
        },
        resumeData,
        currentFontSize,
        customFieldViewData,
        {
          educations: listDegrees,
          profileLevel
        },
        locale,
        currentFont
      )

      pdfPosition = ySectionPosition
    }
  }
}

export { renderTemplateLinkedin }
