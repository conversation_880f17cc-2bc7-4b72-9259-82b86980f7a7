export interface IKeyTemplate {
  publicId: string
  fullName: string
  email: string
  phoneNumber: string
  location: string
  links: string
  headline: string
  departments: string
  totalYearsOfExp: string
  skills: string
  languages: string
  currentSalary: string
  expectedSalary: string
  birthday: string
  age: string
  birthdayYearsOld: string
  profileLevel: string
  nationality: string
  willingToRelocate: string
  noticeToPeriodDays: string
  talentPools: string
  preferredWorkStates: string
  id: string
  yes: string
  no: string
  contactDetailTitle: string
  profileInformationTitle: string
  summaryTitle: string
  workExperiencesTitle: string
  educationTitle: string
  certificatesTitle: string
  referencesTitle: string
  present: string
  undefined: string
  typeOfSalaryMonthly: string
  typeOfSalaryAnnual: string
  yoeOptions: {
    [keyName: string]: string
  }
  of: string
}
export const KEYS_TEMPLATE: {
  [key: string]: IKeyTemplate
} = {
  en: {
    publicId: 'ID',
    fullName: 'Full name',
    email: 'Email',
    phoneNumber: 'Phone number',
    location: 'Location',
    links: 'Links',
    headline: 'Position',
    departments: 'Departments',
    totalYearsOfExp: 'Years of experience',
    skills: 'Skills',
    languages: 'Languages',
    currentSalary: 'Current salary',
    expectedSalary: 'Expected salary',
    typeOfSalaryMonthly: '(Monthly)',
    typeOfSalaryAnnual: '(Annual)',
    birthday: 'Birthday',
    age: 'Age',
    birthdayYearsOld: ' years old',
    profileLevel: 'Experience level',
    nationality: 'Nationality',
    willingToRelocate: 'Willing to relocate',
    noticeToPeriodDays: 'Notice of periods',
    talentPools: 'Talent pools',
    preferredWorkStates: 'Preferred locations',
    id: 'ID:',
    yes: 'Yes',
    no: 'No',
    contactDetailTitle: 'Contact details',
    profileInformationTitle: 'Profile information',
    summaryTitle: 'Summary',
    workExperiencesTitle: 'Work experience',
    educationTitle: 'Education',
    certificatesTitle: 'Certifications',
    referencesTitle: 'References',
    present: 'Present',
    undefined: 'Undefined',
    yoeOptions: {
      '999999': 'Less than one year',
      '1': '1 year',
      '2': '2 years',
      '3': '3 years',
      '4': '4 years',
      '5': '5 years',
      '6': '6 years',
      '7': '7 years',
      '8': '8 years',
      '9': '9 years',
      '10': '10 years',
      '11': '11 years',
      '12': '12 years',
      '13': '13 years',
      '14': '14 years',
      '15': '15 years',
      '16': '16 years',
      '17': '17 years',
      '18': '18 years',
      '19': '19 years',
      '20': '20 years',
      '21': '21 years',
      '22': '22 years',
      '23': '23 years',
      '24': '24 years',
      '25': '25 years',
      '26': '26 years',
      '27': '27 years',
      '28': '28 years',
      '29': '29 years',
      '30': '30 years',
      '31': '31 years',
      '32': '32 years',
      '33': '33 years',
      '34': '34 years',
      '35': '35 years',
      '36': '36 years',
      '37': '37 years',
      '38': '38 years',
      '39': '39 years',
      '40': '40 years',
      '41': '41 years',
      '42': '42 years',
      '43': '43 years',
      '44': '44 years',
      '45': '45 years',
      '46': '46 years',
      '47': '47 years',
      '48': '48 years',
      '49': '49 years',
      '50': '50 years',
      '51': 'Above 50 years'
    },
    of: 'of'
  },
  ja: {
    publicId: 'ID',
    fullName: '氏名',
    email: 'Email',
    phoneNumber: '電話番号',
    location: '場所',
    links: 'リンク',
    headline: 'ポジション',
    departments: '部門',
    totalYearsOfExp: '経験年数',
    skills: 'スキル',
    languages: '言語',
    currentSalary: '現給与',
    expectedSalary: '希望給与',
    typeOfSalaryMonthly: '(月給)',
    typeOfSalaryAnnual: '(年間)',
    birthday: '誕生日',
    age: '年齢',
    birthdayYearsOld: ' 歳',
    profileLevel: '経験の程度',
    nationality: '国籍',
    willingToRelocate: '転居可能',
    noticeToPeriodDays: '告知期間',
    talentPools: '希望職種（タレントプール）',
    preferredWorkStates: '希望勤務先一覧',
    id: 'ID:',
    yes: 'Yes',
    no: 'No',
    contactDetailTitle: '連絡先詳細',
    profileInformationTitle: 'プロフィール情報',
    summaryTitle: 'サマリー',
    workExperiencesTitle: '職務経験',
    educationTitle: '学歴',
    certificatesTitle: '免許・資格',
    referencesTitle: 'リファレンス',
    present: '現在',
    undefined: '未定義',
    yoeOptions: {
      '999999': '1年未満',
      '1': '1 年',
      '2': '2 年',
      '3': '3 年',
      '4': '4 年',
      '5': '5 年',
      '6': '6 年',
      '7': '7 年',
      '8': '8 年',
      '9': '9 年',
      '10': '10 年',
      '11': '11 年',
      '12': '12 年',
      '13': '13 年',
      '14': '14 年',
      '15': '15 年',
      '16': '16 年',
      '17': '17 年',
      '18': '18 年',
      '19': '19 年',
      '20': '20 年',
      '21': '21 年',
      '22': '22 年',
      '23': '23 年',
      '24': '24 年',
      '25': '25 年',
      '26': '26 年',
      '27': '27 年',
      '28': '28 年',
      '29': '29 年',
      '30': '30 年',
      '31': '31 年',
      '32': '32 年',
      '33': '33 年',
      '34': '34 年',
      '35': '35 年',
      '36': '36 年',
      '37': '37 年',
      '38': '38 年',
      '39': '39 年',
      '40': '40 年',
      '41': '41 年',
      '42': '42 年',
      '43': '43 年',
      '44': '44 年',
      '45': '45 年',
      '46': '46 年',
      '47': '47 年',
      '48': '48 年',
      '49': '49 年',
      '50': '50 年',
      '51': '50年以上'
    },
    of: 'の'
  }
}
