import type { ISelectOption } from '~/core/ui/Select'
import { formatDatePickerToDate } from '~/core/ui/SingleDateWithYearOnlyPicker'
import { customDefaultFormatDate } from '~/core/utilities/format-date'

import type { PreferredWorkStatesType, TalentPoolType } from '~/lib/features/candidates/types'
import { getYearOld } from '~/lib/features/candidates/utilities'
import { DEFAULT_CURRENCY } from '~/lib/features/candidates/utilities/enum'
import type { IResume } from '~/lib/features/settings/profiles/edit/types'
import { LIST_SECTIONS_FIELD_DEFAULT } from '~/lib/features/settings/profiles/edit/utilities/enum'

import type { IKeyTemplate } from '../../KeysTemplate/KeysTemplate'
import renderFieldSection from './fieldSection'

const renderDataByKey = ({
  key,
  resumeData,
  hideTitle = false,
  keys,
  extraData,
  validateHidden = false,
  customRelatedFields
}: {
  key: string
  resumeData?: IResume
  hideTitle?: boolean
  keys: IKeyTemplate
  extraData?: {
    [key: string]: ISelectOption[]
  }
  validateHidden?: boolean
  customRelatedFields?: {
    [key: string]: boolean
  }
}) => {
  if (key === LIST_SECTIONS_FIELD_DEFAULT.publicId) {
    if (validateHidden && !resumeData?.permittedFields?.publicId) return []
    return renderFieldSection({
      title: keys.publicId,
      content: resumeData?.permittedFields?.publicId?.value,
      hideTitle,
      height: 12,
      line: 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.fullName) {
    if (validateHidden && !resumeData?.permittedFields?.fullName) return []
    return renderFieldSection({
      title: keys.fullName,
      content: resumeData?.permittedFields?.fullName?.value,
      hideTitle,
      height: 12,
      line: 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.email) {
    if (validateHidden && !resumeData?.permittedFields?.email) return []
    return renderFieldSection({
      title: keys.email,
      content: resumeData?.permittedFields?.email?.value,
      hideTitle,
      height: 12,
      line: 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.phoneNumber) {
    if (validateHidden && !resumeData?.permittedFields?.phoneNumber) return []
    return renderFieldSection({
      title: keys.phoneNumber,
      content: resumeData?.permittedFields?.phoneNumber?.value,
      hideTitle,
      height: 12,
      line: 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.location) {
    if (validateHidden && !resumeData?.permittedFields?.location) return []
    return renderFieldSection({
      title: keys.location,
      content: resumeData?.permittedFields?.location?.value,
      hideTitle,
      height: 12,
      line: 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.links) {
    if (validateHidden && !resumeData?.permittedFields?.links) return []
    const content = []
    let line = 0
    for (let i = 0; i < Object.keys(resumeData?.permittedFields?.links?.value || {}).length; i++) {
      const item = Object.keys(resumeData?.permittedFields?.links?.value || {})[i]
      line += (resumeData?.permittedFields?.links?.value?.[item as string] || []).length

      for (let y = 0; y < (resumeData?.permittedFields?.links?.value?.[item as string] || []).length; y++) {
        const subItem = resumeData?.permittedFields?.links?.value?.[item as string]?.[y]
        content.push(String(subItem))
      }
    }

    return renderFieldSection({
      title: keys.links,
      content,
      hideTitle,
      height: 12,
      line: line || 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.headline) {
    if (validateHidden && !resumeData?.permittedFields?.headline) return []
    return renderFieldSection({
      title: keys.headline,
      content: resumeData?.permittedFields?.headline?.value,
      hideTitle,
      height: 12,
      line: 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.totalYearsOfExp) {
    if (validateHidden && !resumeData?.permittedFields?.totalYearsOfExp) return []
    return renderFieldSection({
      title: keys.totalYearsOfExp,
      content:
        String(resumeData?.permittedFields?.totalYearsOfExp?.value) !== 'null'
          ? `${
              keys.yoeOptions?.[
                String(resumeData?.permittedFields?.totalYearsOfExp?.value || 0) === '0'
                  ? 999999
                  : String(resumeData?.permittedFields?.totalYearsOfExp?.value || '')
              ]
            }`
          : undefined,
      hideTitle,
      height: 12,
      line: 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.skills) {
    if (validateHidden && !resumeData?.permittedFields?.skills) return []
    return renderFieldSection({
      title: keys.skills,
      content: (resumeData?.permittedFields?.skills?.value || []).map((item) => item).join(', '),
      hideTitle,
      height: 12,
      line: 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.languages) {
    if (validateHidden && !resumeData?.permittedFields?.languages) return []
    const content = []
    for (let i = 0; i < (resumeData?.permittedFields?.languages?.value || []).length; i++) {
      const item = resumeData?.permittedFields?.languages?.value?.[i]
      content.push(
        `${item?.languageDescription}${item?.proficiencyDescription ? ` - ${item?.proficiencyDescription}` : ''}`
      )
    }

    return renderFieldSection({
      title: keys.languages,
      content,
      hideTitle,
      height: 12,
      line: (resumeData?.permittedFields?.languages?.value || []).length || 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.currentSalary) {
    if (validateHidden && !resumeData?.permittedFields?.currentSalary) return []
    return renderFieldSection({
      title: keys.currentSalary,
      content:
        Number(resumeData?.permittedFields?.currentSalary?.value) > 0
          ? `${Number(resumeData?.permittedFields?.currentSalary?.value)
              .toString()
              .replace(
                /\B(?=(\d{3})+(?!\d))/g,
                ','
              )} ${resumeData?.permittedFields?.currentSalaryCurrency?.value || DEFAULT_CURRENCY} ${
              resumeData?.permittedFields?.typeOfCurrentSalary?.value === 'monthly'
                ? keys.typeOfSalaryMonthly
                : keys.typeOfSalaryAnnual
            }`
          : undefined,
      hideTitle,
      height: 12,
      line: 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.expectedSalary) {
    if (validateHidden && !resumeData?.permittedFields?.expectedSalary) return []
    return renderFieldSection({
      title: keys.expectedSalary,
      content:
        Number(resumeData?.permittedFields?.expectedSalary?.value) > 0
          ? `${Number(resumeData?.permittedFields?.expectedSalary?.value)
              .toString()
              .replace(
                /\B(?=(\d{3})+(?!\d))/g,
                ','
              )} ${resumeData?.permittedFields?.expectedSalaryCurrency?.value || DEFAULT_CURRENCY} ${
              resumeData?.permittedFields?.typeOfExpectedSalary?.value === 'monthly'
                ? keys.typeOfSalaryMonthly
                : keys.typeOfSalaryAnnual
            }`
          : undefined,
      hideTitle,
      height: 12,
      line: 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.birthday) {
    if (validateHidden && !resumeData?.permittedFields?.birthday) return []
    let content
    const formatYearOld = `${getYearOld(
      formatDatePickerToDate({
        year: resumeData?.permittedFields?.birthday?.value?.year,
        month: resumeData?.permittedFields?.birthday?.value?.month,
        date: resumeData?.permittedFields?.birthday?.value?.date
      })
    )}${keys.birthdayYearsOld}`

    if (resumeData?.permittedFields?.birthday?.value?.year) {
      if (customRelatedFields?.age) {
        content = formatYearOld
      } else {
        if (
          !!resumeData?.permittedFields?.birthday?.value?.year &&
          !resumeData?.permittedFields?.birthday?.value?.month &&
          !resumeData?.permittedFields?.birthday?.value?.date
        ) {
          content = `${resumeData?.permittedFields?.birthday?.value.year} (${formatYearOld})`
        } else {
          content = `${customDefaultFormatDate(
            formatDatePickerToDate({
              year: resumeData?.permittedFields?.birthday?.value?.year,
              month: resumeData?.permittedFields?.birthday?.value?.month,
              date: resumeData?.permittedFields?.birthday?.value?.date
            })
          )} (${formatYearOld})`
        }
      }
    }

    return renderFieldSection({
      title: customRelatedFields?.age ? keys.age : keys.birthday,
      content,
      hideTitle,
      height: 12,
      line: 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.profileLevel) {
    if (validateHidden && !resumeData?.permittedFields?.profileLevel) return []
    return renderFieldSection({
      title: keys.profileLevel,
      content: resumeData?.permittedFields?.profileLevel?.value
        ? (extraData?.profileLevel || [])?.find(
            (item: ISelectOption) => String(item.value) === String(resumeData?.permittedFields?.profileLevel?.value)
          )?.supportingObj?.name
        : undefined,
      hideTitle,
      height: 12,
      line: 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.nationality) {
    if (validateHidden && !resumeData?.permittedFields?.nationality) return []
    return renderFieldSection({
      title: keys.nationality,
      content: resumeData?.permittedFields?.nationality?.value,
      hideTitle,
      height: 12,
      line: 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.willingToRelocate) {
    if (validateHidden && !resumeData?.permittedFields?.willingToRelocate) return
    return renderFieldSection({
      title: keys.willingToRelocate,
      content: resumeData?.permittedFields?.willingToRelocate?.value ? keys.yes : keys.no,
      hideTitle,
      height: 12,
      line: 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.preferredWorkStates) {
    if (validateHidden && !resumeData?.permittedFields?.preferredWorkStates) return []
    return renderFieldSection({
      title: keys.preferredWorkStates,
      content: (resumeData?.permittedFields?.preferredWorkStates?.value || [])
        .map((item: PreferredWorkStatesType) => item.full_name)
        .join(', '),
      hideTitle,
      height: 12,
      line: (resumeData?.permittedFields?.preferredWorkStates?.value || []).length || 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.noticeToPeriodDays) {
    if (validateHidden && !resumeData?.permittedFields?.noticeToPeriodDays) return
    return renderFieldSection({
      title: keys.noticeToPeriodDays,
      content: resumeData?.permittedFields?.noticeToPeriodDays?.value,
      hideTitle,
      height: 12,
      line: 1
    })
  }

  if (key === LIST_SECTIONS_FIELD_DEFAULT.talentPools) {
    if (validateHidden && !resumeData?.permittedFields?.talentPools) return []
    return renderFieldSection({
      title: keys.talentPools,
      content: (resumeData?.permittedFields?.talentPools?.value || [])
        .map((item: TalentPoolType) => item.name)
        .join(', '),
      hideTitle,
      height: 12,
      line: (resumeData?.permittedFields?.talentPools?.value || []).length || 1
    })
  }

  return []
}

export default renderDataByKey
