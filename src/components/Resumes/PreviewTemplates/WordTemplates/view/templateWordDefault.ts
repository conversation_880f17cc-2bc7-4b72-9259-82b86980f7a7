import {
  AlignmentT<PERSON>,
  HeightRule,
  ImageRun,
  Paragraph,
  Table,
  TableBorders,
  TableCell,
  TableRow,
  TextRun,
  VerticalAlign,
  WidthType
} from 'docx'

import type { IUserInformation } from '~/core/@types/global'
import { getShortName } from '~/core/ui/Avatar'
import type { ISelectOption } from '~/core/ui/Select'
import { monthYearTemplateFormatDate } from '~/core/utilities/format-date'

import type {
  CertificatesType,
  EducationsType,
  ReferencesType,
  WorkExperiencesType
} from '~/lib/features/candidates/types'
import {
  checkCertificateFieldFormatDate,
  getCertificateFieldFormatDate,
  getEducationFieldFormatDate,
  getWorkExpFieldFormatDate
} from '~/lib/features/candidates/utilities/format'
import { mappingCustomFieldKind } from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'
import type { CustomFieldViewType } from '~/lib/features/settings/profile-fields/types/custom-field'
import type {
  IResume,
  ISectionCustomFieldParamType,
  ISectionParamType
} from '~/lib/features/settings/profiles/edit/types'
import { checkShowSectionName, getFormatDegreeEducation } from '~/lib/features/settings/profiles/edit/utilities'
import {
  LIST_SECTIONS_DEFAULT,
  LIST_SECTIONS_FIELD_DEFAULT
} from '~/lib/features/settings/profiles/edit/utilities/enum'

import type { IKeyTemplate } from '../../KeysTemplate/KeysTemplate'
import formatValueCustomField from '../../Templates/utilities/formatValueCustomField'
import renderDataByKey from '../utilities/dataByKey'
import renderFieldSection from '../utilities/fieldSection'
import renderTitleSection from '../utilities/titleSection'

const renderTemplateHeader = ({
  user,
  locale,
  configHide,
  templateName,
  resumeData,
  keys
}: {
  user?: IUserInformation
  locale: string
  configHide?: {
    isDefault: boolean // param only apply for FE
    templateNameEnabling: boolean
    dateEnabling: boolean
    profileIdEnabling: boolean
    fullnameEnabling: boolean
    avatarEnabling: boolean
    logoEnabling: boolean
    emailEnabling: boolean
    phoneNumberEnabling: boolean
  }
  templateName?: string
  resumeData?: IResume
  keys: IKeyTemplate
}) => {
  const AVATAR_SIZE = 113
  const LOGO_SIZE = 72

  if (configHide?.isDefault) {
    const fullNameCell = []
    const fullName = resumeData?.permittedFields?.fullName?.value
    if (fullName) {
      fullNameCell.push(
        new Paragraph({
          wordWrap: false,
          children: [
            new TextRun({
              text: String(fullName),
              size: 28
            })
          ]
        })
      )
    }

    let avatarBase64Cell = []
    if (resumeData?.avatarBase64) {
      avatarBase64Cell = [
        new Paragraph({
          children: [
            new ImageRun({
              type: 'jpg',
              data: Buffer.from(resumeData.avatarBase64.replace(/^data:image\/[a-z]+;base64,/, ''), 'base64'),
              transformation: {
                width: AVATAR_SIZE,
                height: AVATAR_SIZE
              }
            })
          ]
        })
      ]
    } else {
      const shortName = getShortName(
        String(resumeData?.permittedFields?.fullName?.value || resumeData?.permittedFields?.email?.value?.[0])
      )

      avatarBase64Cell = [
        new Table({
          columnWidths: [1695, 0],
          rows: [
            new TableRow({
              height: {
                rule: HeightRule.EXACT,
                value: 1695
              },
              children: [
                new TableCell({
                  width: {
                    size: 1695,
                    type: WidthType.DXA
                  },
                  verticalAlign: VerticalAlign.CENTER,
                  children: [
                    new Paragraph({
                      wordWrap: false,
                      alignment: AlignmentType.CENTER,
                      children: [
                        new TextRun({
                          text: String(shortName),
                          size: 31
                        })
                      ]
                    })
                  ]
                })
              ]
            })
          ]
        })
      ]
    }

    return new Table({
      borders: TableBorders.NONE,
      alignment: AlignmentType.LEFT,
      columnWidths: [7943, 1695],
      rows: [
        new TableRow({
          children: [
            new TableCell({
              width: {
                size: 7943,
                type: WidthType.DXA
              },
              verticalAlign: VerticalAlign.CENTER,
              children: fullNameCell
            }),
            new TableCell({
              width: {
                size: 1695,
                type: WidthType.DXA
              },
              children: avatarBase64Cell
            })
          ]
        })
      ]
    })
  }

  const leftCell = []
  const rightCell = []

  const textDate = `${monthYearTemplateFormatDate(new Date(), locale)}${locale === 'ja' ? ` ${keys.present}` : ''}`
  const fullName = resumeData?.permittedFields?.fullName?.value
  const textProfileId = `${keys.id} ${resumeData?.permittedFields?.publicId.value}`

  if (templateName) {
    leftCell.push(
      new Paragraph({
        wordWrap: false,
        children: [
          new TextRun({
            text: String(templateName),
            size: 36
          })
        ]
      })
    )
  }

  if (fullName && configHide?.fullnameEnabling) {
    leftCell.push(
      new Paragraph({
        wordWrap: false,
        children: [
          new TextRun({
            text: String(fullName),
            size: 28
          })
        ]
      })
    )
  }

  if (configHide?.profileIdEnabling) {
    leftCell.push(
      new Paragraph({
        wordWrap: false,
        children: [
          new TextRun({
            text: String(textProfileId)
          })
        ]
      })
    )
  }

  if (configHide?.logoEnabling) {
    let logoBase64Cell = []
    if (resumeData?.logoBase64) {
      logoBase64Cell = [
        new Table({
          borders: TableBorders.NONE,
          alignment: AlignmentType.RIGHT,
          columnWidths: [612, 1083],
          rows: [
            new TableRow({
              height: {
                rule: HeightRule.EXACT,
                value: 1083
              },
              children: [
                new TableCell({
                  width: {
                    size: 612,
                    type: WidthType.DXA
                  },
                  verticalAlign: VerticalAlign.CENTER,
                  children: []
                }),
                new TableCell({
                  width: {
                    size: 1083,
                    type: WidthType.DXA
                  },
                  verticalAlign: VerticalAlign.CENTER,
                  children: [
                    new Paragraph({
                      children: [
                        new ImageRun({
                          type: 'jpg',
                          data: Buffer.from(resumeData.logoBase64.replace(/^data:image\/[a-z]+;base64,/, ''), 'base64'),
                          transformation: {
                            width: LOGO_SIZE,
                            height: LOGO_SIZE
                          }
                        })
                      ]
                    })
                  ]
                })
              ]
            })
          ]
        })
      ]
    } else {
      const shortName = getShortName(String(user?.currentTenant?.name))

      logoBase64Cell = [
        new Table({
          borders: TableBorders.NONE,
          alignment: AlignmentType.RIGHT,
          columnWidths: [612, 1083],
          rows: [
            new TableRow({
              height: {
                rule: HeightRule.EXACT,
                value: 1083
              },
              children: [
                new TableCell({
                  width: {
                    size: 612,
                    type: WidthType.DXA
                  },
                  verticalAlign: VerticalAlign.CENTER,
                  children: []
                }),
                new TableCell({
                  width: {
                    size: 1083,
                    type: WidthType.DXA
                  },
                  verticalAlign: VerticalAlign.CENTER,
                  children: [
                    new Table({
                      columnWidths: [1083, 0],
                      rows: [
                        new TableRow({
                          height: {
                            rule: HeightRule.EXACT,
                            value: 1083
                          },
                          children: [
                            new TableCell({
                              width: {
                                size: 1083,
                                type: WidthType.DXA
                              },
                              verticalAlign: VerticalAlign.CENTER,
                              children: [
                                new Paragraph({
                                  wordWrap: false,
                                  alignment: AlignmentType.CENTER,
                                  children: [
                                    new TextRun({
                                      text: String(shortName),
                                      size: 31
                                    })
                                  ]
                                })
                              ]
                            })
                          ]
                        })
                      ]
                    })
                  ]
                })
              ]
            })
          ]
        })
      ]
    }

    rightCell.push(...logoBase64Cell)
  }

  if (configHide?.dateEnabling) {
    rightCell.push(
      new Paragraph({
        wordWrap: false,
        alignment: AlignmentType.RIGHT,
        spacing: {
          before: 150,
          after: 50
        },
        children: [
          new TextRun({
            text: String(textDate)
          })
        ]
      })
    )
  }

  if (configHide?.avatarEnabling) {
    let avatarBase64Cell = []
    if (resumeData?.avatarBase64) {
      avatarBase64Cell = [
        new Paragraph({
          children: [
            new ImageRun({
              type: 'jpg',
              data: Buffer.from(resumeData.avatarBase64.replace(/^data:image\/[a-z]+;base64,/, ''), 'base64'),
              transformation: {
                width: AVATAR_SIZE,
                height: AVATAR_SIZE
              }
            })
          ]
        })
      ]
    } else {
      const shortName = getShortName(
        String(resumeData?.permittedFields?.fullName?.value || resumeData?.permittedFields?.email?.value?.[0])
      )

      avatarBase64Cell = [
        new Table({
          columnWidths: [1695, 0],
          rows: [
            new TableRow({
              height: {
                rule: HeightRule.EXACT,
                value: 1695
              },
              children: [
                new TableCell({
                  width: {
                    size: 1695,
                    type: WidthType.DXA
                  },
                  verticalAlign: VerticalAlign.CENTER,
                  children: [
                    new Paragraph({
                      wordWrap: false,
                      alignment: AlignmentType.CENTER,
                      children: [
                        new TextRun({
                          text: String(shortName),
                          size: 31
                        })
                      ]
                    })
                  ]
                })
              ]
            })
          ]
        })
      ]
    }

    rightCell.push(...avatarBase64Cell)
  }

  return new Table({
    borders: TableBorders.NONE,
    alignment: AlignmentType.LEFT,
    columnWidths: [7943, 1695],
    rows: [
      new TableRow({
        children: [
          new TableCell({
            width: {
              size: 7943,
              type: WidthType.DXA
            },
            verticalAlign: VerticalAlign.CENTER,
            children: leftCell
          }),
          new TableCell({
            width: {
              size: 1695,
              type: WidthType.DXA
            },
            children: rightCell
          })
        ]
      })
    ]
  })
}

const renderCustomSection = ({
  section,
  resumeData,
  customFieldViewData,
  keys,
  extraData
}: {
  section: ISectionCustomFieldParamType
  resumeData?: IResume
  customFieldViewData?: CustomFieldViewType[]
  keys: IKeyTemplate
  extraData?: {
    [key: string]: ISelectOption[]
  }
}) => {
  const rendered = []
  const mappingsField = (customFieldViewData || [])?.filter((item) => item.visibleToEmployeeProfile && item.visibility)

  if (checkShowSectionName({ section, resumeData, mappingsField })) {
    rendered.push(
      renderTitleSection({
        title: section.name || ''
      })
    )
  }

  if (section.cvTemplateCustomFields?.length === 1) {
    const isShowCustomField =
      section.cvTemplateCustomFields?.[0]?.isCustom === true &&
      section.cvTemplateCustomFields?.[0]?.visibility &&
      section.cvTemplateCustomFields?.[0]?.visibleToEmployeeProfile
    const isShowDefaultField = section.cvTemplateCustomFields?.[0]?.isCustom === false

    if (isShowCustomField) {
      rendered.push(
        renderFieldSection({
          title: '',
          content: formatValueCustomField({
            value: !['select', 'multiple'].includes(
              mappingCustomFieldKind(section.cvTemplateCustomFields?.[0]?.fieldKind || 'text')
            )
              ? resumeData?.customFields?.find(
                  (item) =>
                    String(item.customSettingId) === String(section.cvTemplateCustomFields?.[0]?.customSettingId)
                )?.value
              : resumeData?.customFields?.find(
                  (item) =>
                    String(item.customSettingId) === String(section.cvTemplateCustomFields?.[0]?.customSettingId)
                )?.selectedOptionKeys,
            fieldKind: mappingCustomFieldKind(section.cvTemplateCustomFields?.[0]?.fieldKind || 'text'),
            selectOptions: mappingsField?.find(
              (item) => String(item.id) === String(section.cvTemplateCustomFields?.[0]?.customSettingId)
            )?.selectOptions,
            keys
          }),
          hideTitle: true,
          height: 12,
          line: 1
        })
      )
    }

    if (isShowDefaultField) {
      rendered.push(
        renderDataByKey({
          key: section.cvTemplateCustomFields?.[0]?.key || '',
          resumeData,
          hideTitle: true,
          keys,
          extraData,
          validateHidden: true,
          customRelatedFields: section.cvTemplateCustomFields?.[0]?.customRelatedFields
        })
      )
    }
  } else {
    if (section.cvTemplateCustomFields?.length) {
      for (let i = 0; i < section.cvTemplateCustomFields.length; i++) {
        const field = section.cvTemplateCustomFields[i]

        if (field?.isCustom && field?.visibility && field?.visibleToEmployeeProfile) {
          const value = !['select', 'multiple'].includes(mappingCustomFieldKind(field?.fieldKind || 'text'))
            ? resumeData?.customFields?.find((item) => String(item.customSettingId) === String(field?.customSettingId))
                ?.value
            : resumeData?.customFields?.find((item) => String(item.customSettingId) === String(field?.customSettingId))
                ?.selectedOptionKeys
          const findCustomView = mappingsField?.find((item) => String(item.id) === String(field?.customSettingId))

          rendered.push(
            renderFieldSection({
              title: field?.fieldName || '',
              content: formatValueCustomField({
                value,
                fieldKind: mappingCustomFieldKind(field?.fieldKind || 'text'),
                selectOptions: findCustomView?.selectOptions,
                keys
              }),
              height: 12,
              line: 1
            })
          )
        } else {
          rendered.push(
            renderDataByKey({
              key: field?.key || '',
              resumeData,
              keys,
              extraData,
              validateHidden: true,
              customRelatedFields: field?.customRelatedFields
            })
          )
        }
      }
    }
  }

  return rendered.filter((item) => item)
}

const renderContactDetails = ({
  section,
  resumeData,
  keys
}: {
  section: ISectionCustomFieldParamType
  resumeData?: IResume
  keys: IKeyTemplate
}) => {
  const rendered = []

  rendered.push(
    renderTitleSection({
      title: section.name || keys.contactDetailTitle
    })
  )

  if (section.cvTemplateCustomFields?.length) {
    for (let i = 0; i < section.cvTemplateCustomFields.length; i++) {
      const field = section.cvTemplateCustomFields[i]

      if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.fullName && resumeData?.permittedFields?.fullName) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.fullName,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }

      if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.email && resumeData?.permittedFields?.email) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.email,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }

      if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.phoneNumber && resumeData?.permittedFields?.phoneNumber) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.phoneNumber,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }

      if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.location && resumeData?.permittedFields?.location) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.location,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }

      if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.links && resumeData?.permittedFields?.links) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.links,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }
    }
  }

  return rendered.filter((item) => item)
}

const renderProfileInformation = ({
  section,
  resumeData,
  customFieldViewData,
  keys,
  extraData
}: {
  section: ISectionCustomFieldParamType
  resumeData?: IResume
  customFieldViewData?: CustomFieldViewType[]
  keys: IKeyTemplate
  extraData?: {
    [key: string]: ISelectOption[]
  }
}) => {
  const rendered = []
  const mappingsField = (customFieldViewData || [])?.filter((item) => item.visibleToEmployeeProfile && item.visibility)

  if (checkShowSectionName({ section, resumeData, mappingsField })) {
    rendered.push(
      renderTitleSection({
        title: keys.profileInformationTitle
      })
    )
  }

  if (section.cvTemplateCustomFields?.length) {
    for (let i = 0; i < section.cvTemplateCustomFields.length; i++) {
      const field = section.cvTemplateCustomFields[i]

      if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.publicId && resumeData?.permittedFields?.publicId) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.publicId,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }

      if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.headline && resumeData?.permittedFields?.headline) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.headline,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }

      if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.talentPools && resumeData?.permittedFields?.talentPools) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.talentPools,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }

      if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.profileLevel && resumeData?.permittedFields?.profileLevel) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.profileLevel,
            resumeData,
            hideTitle: false,
            keys,
            extraData
          })
        )
      }

      if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.totalYearsOfExp && resumeData?.permittedFields?.totalYearsOfExp) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.totalYearsOfExp,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }

      if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.skills && resumeData?.permittedFields?.skills) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.skills,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }

      if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.languages && resumeData?.permittedFields?.languages) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.languages,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }

      if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.currentSalary && resumeData?.permittedFields?.currentSalary) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.currentSalary,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }

      if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.expectedSalary && resumeData?.permittedFields?.expectedSalary) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.expectedSalary,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }

      if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.birthday && resumeData?.permittedFields?.birthday) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.birthday,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }

      if (field?.key === LIST_SECTIONS_FIELD_DEFAULT.nationality && resumeData?.permittedFields?.nationality) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.nationality,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }

      if (
        field?.key === LIST_SECTIONS_FIELD_DEFAULT.willingToRelocate &&
        resumeData?.permittedFields?.willingToRelocate
      ) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.willingToRelocate,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }

      if (
        field?.key === LIST_SECTIONS_FIELD_DEFAULT.preferredWorkStates &&
        resumeData?.permittedFields?.preferredWorkStates
      ) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.preferredWorkStates,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }

      if (
        field?.key === LIST_SECTIONS_FIELD_DEFAULT.noticeToPeriodDays &&
        resumeData?.permittedFields?.noticeToPeriodDays
      ) {
        rendered.push(
          renderDataByKey({
            key: LIST_SECTIONS_FIELD_DEFAULT.noticeToPeriodDays,
            resumeData,
            hideTitle: false,
            keys
          })
        )
      }
    }
  }

  if (mappingsField?.length) {
    for (let i = 0; i < mappingsField?.length; i++) {
      const field = mappingsField[i]

      const value = !['select', 'multiple'].includes(String(field?.type))
        ? resumeData?.customFields?.find((item) => String(item.customSettingId) === String(field?.id))?.value
        : resumeData?.customFields?.find((item) => String(item.customSettingId) === String(field?.id))
            ?.selectedOptionKeys
      const findCustomView = mappingsField?.find((item) => String(item.id) === String(field?.id))

      rendered.push(
        renderFieldSection({
          title: field?.label || '',
          content: formatValueCustomField({
            value,
            fieldKind: String(field?.type) as
              | 'text'
              | 'number'
              | 'paragraph'
              | 'toggle'
              | 'select'
              | 'date'
              | 'multiple',
            selectOptions: findCustomView?.selectOptions,
            keys
          }),
          height: 12,
          line: 1
        })
      )
    }
  }

  return rendered.filter((item) => item)
}

const renderSummary = ({
  section,
  resumeData,
  keys
}: {
  section: ISectionCustomFieldParamType
  resumeData?: IResume
  keys: IKeyTemplate
}) => {
  if (!resumeData?.permittedFields?.summary) return
  const rendered = []

  rendered.push(
    renderTitleSection({
      title: section.name || keys.summaryTitle
    })
  )

  if (resumeData?.permittedFields?.summary?.value) {
    rendered.push(
      renderFieldSection({
        title: '',
        content: resumeData.permittedFields.summary.value,
        hideTitle: true,
        height: 12,
        line: 1
      })
    )
  }

  return rendered.filter((item) => item)
}

const renderWorkExperiences = ({
  resumeData,
  keys,
  section
}: {
  resumeData?: IResume
  keys: IKeyTemplate
  section: ISectionCustomFieldParamType
}) => {
  if (!resumeData?.permittedFields?.workExperiences) return
  const rendered = []

  const LEFT_ITEM = 112

  rendered.push(
    renderTitleSection({
      title: section.name || keys.workExperiencesTitle
    })
  )

  if (resumeData?.permittedFields?.workExperiences?.value?.length) {
    for (let i = 0; i < resumeData.permittedFields.workExperiences.value.length; i++) {
      const item = resumeData.permittedFields.workExperiences.value[i] as WorkExperiencesType
      const isShowDateTime =
        getWorkExpFieldFormatDate({
          type: 'from',
          attributes: item,
          isTemplate: true
        }) && section.customRelatedFields?.datetime
      const isShowTitle = item.title && section.customRelatedFields?.title
      const isShowCompany = item.company && section.customRelatedFields?.company
      const isShowLocation = item.location && section.customRelatedFields?.location
      const isShowDescription = item.description && section.customRelatedFields?.description
      const leftCell = []
      const rightCell = []

      if (isShowDateTime) {
        const content = `${getWorkExpFieldFormatDate({
          type: 'from',
          attributes: item,
          isTemplate: true,
          keys: {
            undefined: keys.undefined
          }
        })}${
          item?.currentWorking
            ? ` - ${keys.present}`
            : ` - ${getWorkExpFieldFormatDate({
                type: 'to',
                attributes: item,
                isTemplate: true,
                keys: {
                  undefined: keys.undefined
                }
              })}`
        }`

        leftCell.push(
          new Paragraph({
            wordWrap: false,
            children: [
              new TextRun({
                text: String(content)
              })
            ]
          })
        )
      }

      if (isShowTitle || isShowCompany) {
        const fontSize = 12
        const content = `${isShowTitle ? item.title : ''}${
          section.customRelatedFields?.company
            ? isShowTitle && item.company
              ? ` · ${item.company}`
              : item.company
            : ''
        }`

        rightCell.push(
          new Paragraph({
            wordWrap: false,
            children: [
              new TextRun({
                text: String(content)
              })
            ]
          })
        )
      }

      if (isShowLocation) {
        const fontSize = 12
        const formatLocation = item?.location as {
          id?: string
          value?: string
        }
        const content = String((formatLocation?.id ? formatLocation?.value : item.location) || '')

        rightCell.push(
          new Paragraph({
            wordWrap: false,
            children: [
              new TextRun({
                text: content
              })
            ]
          })
        )
      }

      rendered.push(
        new Table({
          borders: TableBorders.NONE,
          alignment: AlignmentType.LEFT,
          columnWidths: isShowDateTime ? [2100, 7538] : [9630, 8],
          rows: [
            new TableRow({
              children: isShowDateTime
                ? [
                    new TableCell({
                      children: leftCell,
                      width: {
                        size: 2100,
                        type: WidthType.DXA
                      }
                    }),
                    new TableCell({
                      children: rightCell,
                      width: {
                        size: 7538,
                        type: WidthType.DXA
                      }
                    })
                  ]
                : [
                    new TableCell({
                      children: rightCell,
                      width: {
                        size: 9638,
                        type: WidthType.DXA
                      }
                    })
                  ]
            })
          ]
        })
      )

      if (isShowDescription) {
        rendered.push(
          renderFieldSection({
            title: '',
            content: item.description,
            hideTitle: true,
            height: 12,
            line: 1
          })
        )
      }
    }
  }

  return rendered.filter((item) => item)
}

const renderEducations = ({
  resumeData,
  keys,
  extraData,
  section,
  locale
}: {
  resumeData?: IResume
  keys: IKeyTemplate
  extraData?: ISelectOption[]
  section: ISectionCustomFieldParamType
  locale?: string
}) => {
  if (!resumeData?.permittedFields?.educations) return
  let rendered = []
  const LEFT_ITEM = 112

  rendered.push(
    renderTitleSection({
      title: section.name || keys.educationTitle
    })
  )

  if (resumeData?.permittedFields?.educations?.value?.length) {
    for (let i = 0; i < resumeData.permittedFields.educations.value.length; i++) {
      const item = resumeData.permittedFields.educations.value[i] as EducationsType
      const isShowDateTime =
        getEducationFieldFormatDate({
          type: 'from',
          attributes: item,
          locale
        }) && section.customRelatedFields?.datetime
      const isShowDegree = item.degree && section.customRelatedFields?.degree
      const isShowDegreeSubject = item.degreeSubject && section.customRelatedFields?.major
      const isShowSchool = section.customRelatedFields?.school && item.schoolName
      const isShowDescription = item.description && section.customRelatedFields?.description
      const leftCell = []
      const rightCell = []

      if (isShowDateTime) {
        const content = `${getEducationFieldFormatDate({
          type: 'from',
          attributes: item,
          isTemplate: true,
          keys: {
            undefined: keys.undefined
          },
          locale
        })} - ${getEducationFieldFormatDate({
          type: 'to',
          attributes: item,
          isTemplate: true,
          keys: {
            undefined: keys.undefined
          },
          locale
        })}`
        const fontSize = 12

        leftCell.push(
          new Paragraph({
            wordWrap: false,
            children: [
              new TextRun({
                text: String(content)
              })
            ]
          })
        )
      }

      if (isShowDegree || isShowDegreeSubject || isShowSchool) {
        const fontSize = 12
        const isShow = section.customRelatedFields?.degree || section.customRelatedFields?.major
        const content = `${isShowSchool ? item.schoolName : ''}${
          isShow
            ? isShowSchool && (item.degree || item.degreeSubject)
              ? ` · ${getFormatDegreeEducation({
                  degree: section.customRelatedFields?.degree ? item.degree : undefined,
                  degreeSubject: section.customRelatedFields?.major ? item.degreeSubject : undefined,
                  listDegrees: extraData
                })}`
              : getFormatDegreeEducation({
                  degree: section.customRelatedFields?.degree ? item.degree : undefined,
                  degreeSubject: section.customRelatedFields?.major ? item.degreeSubject : undefined,
                  listDegrees: extraData
                })
            : ''
        }`

        rightCell.push(
          new Paragraph({
            wordWrap: false,
            children: [
              new TextRun({
                text: String(content)
              })
            ]
          })
        )
      }

      rendered.push(
        new Table({
          borders: TableBorders.NONE,
          alignment: AlignmentType.LEFT,
          columnWidths: isShowDateTime ? [2300, 7338] : [9630, 8],
          rows: [
            new TableRow({
              children: isShowDateTime
                ? [
                    new TableCell({
                      children: leftCell,
                      width: {
                        size: 2300,
                        type: WidthType.DXA
                      }
                    }),
                    new TableCell({
                      children: rightCell,
                      width: {
                        size: 7338,
                        type: WidthType.DXA
                      }
                    })
                  ]
                : [
                    new TableCell({
                      children: rightCell,
                      width: {
                        size: 9638,
                        type: WidthType.DXA
                      }
                    })
                  ]
            })
          ]
        })
      )

      if (isShowDescription) {
        rendered.push(
          renderFieldSection({
            title: '',
            content: item.description,
            hideTitle: true,
            height: 12,
            line: 1
          })
        )
      }
    }
  }

  return rendered.filter((item) => item)
}

const renderCertificates = ({
  resumeData,
  keys,
  section,
  locale
}: {
  resumeData?: IResume
  keys: IKeyTemplate
  section: ISectionCustomFieldParamType
  locale?: string
}) => {
  if (!resumeData?.permittedFields?.certificates) return
  let rendered = []
  const LEFT_ITEM = 64

  rendered.push(
    renderTitleSection({
      title: section.name || keys.certificatesTitle
    })
  )

  if (resumeData?.permittedFields?.certificates?.value?.length) {
    for (let i = 0; i < resumeData.permittedFields.certificates.value.length; i++) {
      const item = resumeData.permittedFields.certificates.value[i] as CertificatesType
      const isShowDateTime = checkCertificateFieldFormatDate({
        attributes: item
      })
      const isShowContent = item.certificateName || item.institution
      const leftCell = []
      const rightCell = []

      if (isShowDateTime) {
        const fontSize = 12

        leftCell.push(
          new Paragraph({
            wordWrap: false,
            children: [
              new TextRun({
                text: String(
                  `${getCertificateFieldFormatDate({
                    attributes: item,
                    isTemplate: true,
                    locale
                  })}`
                )
              })
            ]
          })
        )
      }

      if (isShowContent) {
        const fontSize = 12

        const content = `${item.certificateName || keys.undefined}${item.institution ? ` · ${item.institution}` : ''}`

        rightCell.push(
          new Paragraph({
            wordWrap: false,
            children: [
              new TextRun({
                text: String(content)
              })
            ]
          })
        )
      }

      rendered.push(
        new Table({
          borders: TableBorders.NONE,
          alignment: AlignmentType.LEFT,
          columnWidths: isShowDateTime ? [1100, 8538] : [9630, 8],
          rows: [
            new TableRow({
              children: isShowDateTime
                ? [
                    new TableCell({
                      children: leftCell,
                      width: {
                        size: 1100,
                        type: WidthType.DXA
                      }
                    }),
                    new TableCell({
                      children: rightCell,
                      width: {
                        size: 8538,
                        type: WidthType.DXA
                      }
                    })
                  ]
                : [
                    new TableCell({
                      children: rightCell,
                      width: {
                        size: 9638,
                        type: WidthType.DXA
                      }
                    })
                  ]
            })
          ]
        })
      )
    }
  }

  return rendered.filter((item) => item)
}

const renderReferences = ({
  resumeData,
  keys,
  section
}: {
  resumeData?: IResume
  keys: IKeyTemplate
  section: ISectionCustomFieldParamType
}) => {
  if (!resumeData?.permittedFields?.references) return
  const rendered = []

  const LEFT_ITEM = 64

  rendered.push(
    renderTitleSection({
      title: section.name || keys.referencesTitle
    })
  )

  if (resumeData?.permittedFields?.references?.value?.length) {
    for (let i = 0; i < resumeData.permittedFields.references.value.length; i++) {
      const item = resumeData.permittedFields.references.value[i] as ReferencesType
      const isShowContent = item.name || item.email

      if (isShowContent) {
        const fontSize = 12

        const content = `${item.name || keys.undefined}${item.email ? ` · ${item.email}` : ''}`

        rendered.push(
          new Paragraph({
            wordWrap: false,
            children: [
              new TextRun({
                text: String(content)
              })
            ]
          })
        )
      }
    }
  }

  return rendered.filter((item) => item)
}

const mapSectionToWord = (
  params: {
    section: ISectionCustomFieldParamType
    keys: IKeyTemplate
  },
  resumeData?: IResume,
  customFieldViewData?: CustomFieldViewType[],
  extraData?: {
    [type: string]: ISelectOption[]
  },
  locale?: string
) => {
  const mergedConfig = {
    keys: params.keys,
    resumeData,
    section: params.section,
    locale
  }

  if (params.section.key === LIST_SECTIONS_DEFAULT.ContactDetails) {
    return renderContactDetails(mergedConfig)
  }

  if (params.section.key === LIST_SECTIONS_DEFAULT.ProfileInformation) {
    return renderProfileInformation({
      ...mergedConfig,
      customFieldViewData,
      extraData
    })
  }

  if (params.section.key === LIST_SECTIONS_DEFAULT.Summary) {
    return renderSummary(mergedConfig)
  }

  if (params.section.key === LIST_SECTIONS_DEFAULT.WorkExperiences) {
    return renderWorkExperiences(mergedConfig)
  }

  if (params.section.key === LIST_SECTIONS_DEFAULT.Educations) {
    return renderEducations({
      ...mergedConfig,
      extraData: extraData?.educations
    })
  }

  if (params.section.key === LIST_SECTIONS_DEFAULT.Certificates) {
    return renderCertificates(mergedConfig)
  }

  if (params.section.key === LIST_SECTIONS_DEFAULT.References) {
    return renderReferences(mergedConfig)
  }

  return renderCustomSection({
    ...mergedConfig,
    customFieldViewData,
    extraData
  })
}

const templateWordDefault = (props: {
  user?: IUserInformation
  resumeData?: IResume
  customFieldViewData?: CustomFieldViewType[]
  columns?: ISectionParamType
  locale: string
  keys: IKeyTemplate
  listDegrees: ISelectOption[]
  profileLevel: ISelectOption[]
  companyName?: string
}) => {
  const { user, locale, columns, resumeData, keys, customFieldViewData, listDegrees, profileLevel, companyName } = props
  return [
    // Start rendered
    // Step 1 - Template Header
    renderTemplateHeader({
      user,
      locale,
      configHide: {
        isDefault: columns?.isDefault || false,
        templateNameEnabling: columns?.templateNameEnabling || false,
        dateEnabling: columns?.dateEnabling || false,
        profileIdEnabling: columns?.profileIdEnabling || false,
        fullnameEnabling: columns?.fullnameEnabling || false,
        avatarEnabling: columns?.avatarEnabling || false,
        logoEnabling: columns?.logoEnabling || false,
        emailEnabling: columns?.emailEnabling || false,
        phoneNumberEnabling: columns?.phoneNumberEnabling || false
      },
      templateName: columns?.templateName,
      resumeData,
      keys
    }),
    // Step 2 - Dynamic field cvTemplateSections
    ...(columns?.cvTemplateSections || [])
      .map((section) => {
        const result = mapSectionToWord(
          {
            section,
            keys
          },
          resumeData,
          customFieldViewData,
          {
            educations: listDegrees,
            profileLevel
          },
          locale
        )

        if (result?.length) {
          return result.flat()
        }

        return []
      })
      .flat()
  ]
}

export { templateWordDefault }
