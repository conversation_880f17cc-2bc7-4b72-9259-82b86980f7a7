import { use<PERSON>allback, useContext, useLayoutEffect, useRef, useState } from 'react'
import type { FieldError } from 'react-hook-form'
import { Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import useEnumsData from 'src/hooks/data/use-enums-data'
import { AGENCY_TENANT } from '~/core/constants/enum'
import { AsyncMultipleSearchWithSelect } from '~/core/ui/AsyncMultipleSearchWithSelect'
import { AsyncSingleSearchWithSelect } from '~/core/ui/AsyncSingleSearchWithSelect'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import { FormControlItem } from '~/core/ui/FormControlItem'
import { TypographyH5 } from '~/core/ui/Heading'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import { Input } from '~/core/ui/Input'
import { InputRightElement } from '~/core/ui/InputElement'
import { InputGroup } from '~/core/ui/InputGroup'
import { NativeSelect } from '~/core/ui/NativeSelect'
import { PhoneInput } from '~/core/ui/PhoneInput'
import type { ISelectOption } from '~/core/ui/Select'
import { SingleDateWithYearOnlyPicker } from '~/core/ui/SingleDateWithYearOnlyPicker'
import { SuggestionInlineChips } from '~/core/ui/SuggestionChips'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { Toggle } from '~/core/ui/Toggle'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn } from '~/core/ui/utils'
import { difference } from '~/core/utilities/common'

import { useDetectCountryCodeFromTimeZone } from '~/lib/countries-mapping/hooks/use-detect-country-code-from-time-zone'
import useCandidateProfile from '~/lib/features/candidates/hooks/use-query-candidate'
import { schemaUpdateProfile } from '~/lib/features/candidates/schema/validation-update-profile'
import type { PreferredWorkStatesType, TalentPoolType } from '~/lib/features/candidates/types'
import { ListSuggestNoticeOfPeriod, totalYoeOptions } from '~/lib/features/candidates/utilities/enum'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import {
  formatInitialValueCustomField,
  formatSubmitCustomFieldData
} from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'
import type {
  CustomFieldFormType,
  CustomFieldViewType
} from '~/lib/features/settings/profile-fields/types/custom-field'
import useResumes from '~/lib/features/settings/profiles/edit/hooks/use-resumes'
import { mappingCustomFieldGroup } from '~/lib/features/settings/profiles/edit/mapping'
import { useResumeStore } from '~/lib/features/settings/profiles/edit/store'
import type { ISectionCustomFieldParamType } from '~/lib/features/settings/profiles/edit/types'
import { checkShowSectionName } from '~/lib/features/settings/profiles/edit/utilities'
import {
  LIST_SECTIONS_DEFAULT,
  LIST_SECTIONS_FIELD_DEFAULT
} from '~/lib/features/settings/profiles/edit/utilities/enum'
import useSkillSettingsStore from '~/lib/features/settings/skills/store'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import useBoundStore from '~/lib/store'

import type { CustomFieldComponentType } from '~/components/CustomField'
import CustomField from '~/components/CustomField'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'

import { EditCVFormContext } from '../ResumesEditorContentView'
import LanguageGroupView from './LanguageGroupView'
import LinksGroupView from './LinksGroupView'

const CustomFieldGroupView = ({
  customFieldViewData = [],
  section
}: {
  customFieldViewData?: CustomFieldViewType[]
  section: ISectionCustomFieldParamType
}) => {
  const { t, i18n } = useTranslation()
  const { resumeData } = useResumeStore()
  const { user, currentRole } = useBoundStore()
  const { promiseSkillsOptions, promiseTalentPoolOptions, promiseCountryStateOptions, currencySalary, profileLevel } =
    useCandidateProfile({})
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const { profileId, employeeId, handleFormChange, debouncedFormChange, noDebouncedFormChange, profileUpdating } =
    useContext(EditCVFormContext)
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })
  const { triggerUpdateProfile } = useResumes({ isCompanyKind })
  const ProfileTypeOfExpectedSalaryEnums = useEnumsData({
    enumType: 'ProfileTypeOfExpectedSalary',
    locale: i18n.language
  })

  const ProfileTypeOfCurrentSalaryEnums = useEnumsData({
    enumType: 'ProfileTypeOfCurrentSalary',
    locale: i18n.language
  })
  const { allowAddNewSkill } = useSkillSettingsStore()

  const { countryCode } = useDetectCountryCodeFromTimeZone()

  const mappingsField = (customFieldViewData || [])?.filter((item) => item.visibleToEmployeeProfile && item.visibility)

  const isShowEquivalentsSkills =
    isFeatureEnabled(PLAN_FEATURE_KEYS.skill_management) && isUnLockFeature(PLAN_FEATURE_KEYS.skill_management)

  const [aiSuggestsSkills, setAISuggestsSkills] = useState<Array<{ label: string }>>()

  const [isGettingSuggestSkills, setIsGettingSuggestSkills] = useState<boolean>(false)
  const [showMoreSkills, setShowMoreSkills] = useState<boolean>(false)
  const [isShowMoreClicked, setIsShowMoreClicked] = useState<boolean>(false)
  const skillsContainerRef = useRef<HTMLDivElement | null>(null)

  const getSkillsByProfile = useCallback(async () => {
    if (profileId) {
      setIsGettingSuggestSkills(true)
      const skills = await promiseSkillsOptions({
        search: '',
        page: 1,
        limit: 1000,
        profileId: Number(profileId),
        profileHeadline: resumeData?.permittedFields?.headline?.value || ''
      })
      setAISuggestsSkills(skills.collection.map((item: any) => ({ label: item.value })))
      setIsGettingSuggestSkills(false)
    }
  }, [profileId, resumeData.permittedFields?.headline, promiseSkillsOptions])

  useLayoutEffect(() => {
    if (isShowMoreClicked) return
    setShowMoreSkills(false)
    if (skillsContainerRef.current && skillsContainerRef.current.clientHeight >= 85) {
      skillsContainerRef.current.style.maxHeight = '85px'
      setShowMoreSkills(true)
    }
  }, [skillsContainerRef, aiSuggestsSkills, resumeData?.permittedFields?.skills, isShowMoreClicked])

  return (
    <div>
      {checkShowSectionName({ section, resumeData, mappingsField }) ? (
        <>
          {[LIST_SECTIONS_DEFAULT.ContactDetails, LIST_SECTIONS_DEFAULT.ProfileInformation].includes(
            section.key || ''
          ) ? (
            <>
              {section.key === LIST_SECTIONS_DEFAULT.ContactDetails ? (
                <TypographyH5 className="font-medium text-gray-900">
                  {t('candidates:tabs:candidateOverview:contactDetails:heading')}
                </TypographyH5>
              ) : null}
              {section.key === LIST_SECTIONS_DEFAULT.ProfileInformation ? (
                <TypographyH5 className="font-medium text-gray-900">
                  {t('candidates:tabs:candidateOverview:profileInformation:heading')}
                </TypographyH5>
              ) : null}
            </>
          ) : (
            <TypographyH5 className="font-medium text-gray-900">{section.name}</TypographyH5>
          )}
        </>
      ) : null}

      <div className="mt-4 space-y-4">
        <DynamicImportForm
          isShowDebug={false}
          id="custom-field-form"
          className="w-full"
          schema={schemaUpdateProfile(t)}
          defaultValue={mappingCustomFieldGroup(resumeData, user)}
          noUseSubmit>
          {({ formState, control, trigger: triggerValidate, setError, clearErrors, getValues }) => {
            const SelectCurrentCurrency = () => {
              return (
                <div>
                  <Controller
                    control={control}
                    name="currentSalaryCurrency"
                    render={({ field: { onChange, value } }) => {
                      const findSalary = currencySalary.find(
                        (item: ISelectOption) => item.value === (value || user?.currentTenant?.currency)
                      )
                      const formatCurrentSalaryCurrency = findSalary
                        ? {
                            value: String(value || user?.currentTenant?.currency),
                            supportingObj: {
                              name: findSalary?.supportingObj?.name || ''
                            }
                          }
                        : undefined

                      return (
                        <FormControlItem>
                          <NativeSelect
                            size="sm"
                            isClearable={false}
                            isSearchable={false}
                            onChange={(newValue) => {
                              handleFormChange({
                                fieldName: 'currentSalaryCurrency',
                                value: (newValue as ISelectOption).value,
                                onChange
                              })
                              return noDebouncedFormChange({
                                fieldName: 'currentSalaryCurrency',
                                value: (newValue as ISelectOption).value,
                                triggerValidate
                              })
                            }}
                            value={formatCurrentSalaryCurrency}
                            options={currencySalary}
                            classNameOverride={{
                              bordered: 'none',
                              loadingMessage: `${t('label:loading')}`,
                              noOptionsMessage: `${t('label:noOptions')}`
                            }}
                            menuPosition="fixed"
                          />
                        </FormControlItem>
                      )
                    }}
                  />
                </div>
              )
            }

            const SelectExpertCurrency = () => {
              return (
                <div>
                  <Controller
                    control={control}
                    name="expectedSalaryCurrency"
                    render={({ field: { onChange, value } }) => {
                      const findSalary = currencySalary.find(
                        (item: ISelectOption) => item.value === (value || user?.currentTenant?.currency)
                      )
                      const formatExpectedSalaryCurrency = findSalary
                        ? {
                            value: String(value || user?.currentTenant?.currency),
                            supportingObj: {
                              name: findSalary?.supportingObj?.name || ''
                            }
                          }
                        : undefined

                      return (
                        <FormControlItem>
                          <NativeSelect
                            size="sm"
                            isClearable={false}
                            isSearchable={false}
                            onChange={(newValue) => {
                              handleFormChange({
                                fieldName: 'expectedSalaryCurrency',
                                value: (newValue as ISelectOption).value,
                                onChange
                              })
                              return noDebouncedFormChange({
                                fieldName: 'expectedSalaryCurrency',
                                value: (newValue as ISelectOption).value,
                                triggerValidate
                              })
                            }}
                            value={formatExpectedSalaryCurrency}
                            options={currencySalary}
                            classNameOverride={{
                              bordered: 'none',
                              loadingMessage: `${t('label:loading')}`,
                              noOptionsMessage: `${t('label:noOptions')}`
                            }}
                            menuPosition="fixed"
                          />
                        </FormControlItem>
                      )
                    }}
                  />
                </div>
              )
            }

            return (
              <div className="space-y-4">
                {section.cvTemplateCustomFields?.map((field, index) => {
                  if (field.key === LIST_SECTIONS_FIELD_DEFAULT.customField && field.isCustom) {
                    return (
                      <div key={index} className="space-y-4">
                        <Controller
                          control={control}
                          name="customFields"
                          render={({ field: { onChange } }) => {
                            return (
                              <>
                                {mappingsField.map((customField, customFieldIndex) => (
                                  <div key={customFieldIndex}>
                                    <CustomField
                                      type={customField.type as CustomFieldComponentType['type']}
                                      display="default"
                                      classNameConfig={{
                                        paragraph: 'w-full max-w-full',
                                        paragraphWrapper: 'max-h-[75vh] overflow-y-auto max-w-full'
                                      }}
                                      name={customField.name}
                                      label={customField.label}
                                      error={formState.errors.customFields}
                                      value={
                                        Object.values(
                                          (formatInitialValueCustomField(resumeData.customFields, {
                                            isNotTrimText: true
                                          }) || {}) as CustomFieldFormType
                                        ).find((item) => String(item.id) === String(customField.id))?.value || ''
                                      }
                                      onChange={(fieldValue) => {
                                        const cloneData = formatInitialValueCustomField(resumeData.customFields, {
                                          isNotTrimText: true
                                        })
                                        const newValue = {
                                          ...cloneData,
                                          [customField.name]: {
                                            ...customField,
                                            value: fieldValue
                                          }
                                        }

                                        handleFormChange({
                                          fieldName: 'customFields',
                                          value: formatSubmitCustomFieldData(newValue, {
                                            isNotTrimText: true
                                          }),
                                          onChange: undefined,
                                          isUpdatePermittedFields: false
                                        })
                                        onChange(newValue)
                                        return debouncedFormChange({
                                          fieldName: 'customFields',
                                          value: formatSubmitCustomFieldData(newValue),
                                          triggerValidate
                                        })
                                      }}
                                      extraProps={{
                                        options: customField.selectOptions
                                      }}
                                    />
                                  </div>
                                ))}
                              </>
                            )
                          }}
                        />
                      </div>
                    )
                  }

                  if (field.isCustom) {
                    if (customFieldViewData?.length > 0) {
                      const customField = mappingsField.find(
                        (item) => String(item.id) === String(field.customSettingId)
                      )

                      if (!customField) return null

                      return (
                        <div key={index}>
                          <Controller
                            control={control}
                            name="customFields"
                            render={({ field: { onChange } }) => {
                              return (
                                <CustomField
                                  type={customField.type as CustomFieldComponentType['type']}
                                  display="default"
                                  classNameConfig={{
                                    paragraph: 'w-full max-w-full',
                                    paragraphWrapper: 'max-h-[75vh] overflow-y-auto max-w-full'
                                  }}
                                  name={customField.name}
                                  label={customField.label}
                                  error={formState.errors.customFields}
                                  value={
                                    Object.values(
                                      (formatInitialValueCustomField(resumeData.customFields, {
                                        isNotTrimText: true
                                      }) || {}) as CustomFieldFormType
                                    ).find((item) => String(item.id) === String(customField.id))?.value || ''
                                  }
                                  onChange={(fieldValue) => {
                                    const cloneData = formatInitialValueCustomField(resumeData.customFields, {
                                      isNotTrimText: true
                                    })
                                    const newValue = {
                                      ...cloneData,
                                      [customField.name]: {
                                        ...customField,
                                        value: fieldValue
                                      }
                                    }

                                    handleFormChange({
                                      fieldName: 'customFields',
                                      value: formatSubmitCustomFieldData(newValue, {
                                        isNotTrimText: true
                                      }),
                                      onChange: undefined,
                                      isUpdatePermittedFields: false
                                    })
                                    onChange(newValue)
                                    return debouncedFormChange({
                                      fieldName: 'customFields',
                                      value: formatSubmitCustomFieldData(newValue),
                                      triggerValidate
                                    })
                                  }}
                                  extraProps={{
                                    options: customField.selectOptions
                                  }}
                                />
                              )
                            }}
                          />
                        </div>
                      )
                    }

                    return null
                  }

                  const permittedFields = resumeData?.permittedFields
                  return (
                    <div key={index}>
                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.publicId && permittedFields?.publicId ? (
                        <Controller
                          control={control}
                          name="publicId"
                          render={({ field: { onChange, value } }) => {
                            return (
                              <FormControlItem
                                label={`${t('candidates:candidateTable:publicId')}`}
                                destructive={formState.errors && !!formState.errors?.publicId}
                                destructiveText={formState.errors && (formState.errors?.publicId?.message as string)}>
                                <Input
                                  isDisabled
                                  placeholder={`${t('candidates:placeholder:clickToEnterValue')}`}
                                  size="sm"
                                  value={value}
                                  destructive={formState.errors && !!formState.errors?.publicId}
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      ) : null}
                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.fullName && permittedFields?.fullName ? (
                        <Controller
                          control={control}
                          name="fullName"
                          render={({ field: { onChange, value } }) => {
                            return (
                              <FormControlItem
                                label={`${t('candidates:candidateTable:fullName')}`}
                                destructive={formState.errors && !!formState.errors?.fullName}
                                destructiveText={formState.errors && (formState.errors?.fullName?.message as string)}>
                                <Input
                                  placeholder={`${t('candidates:placeholder:clickToEnterValue')}`}
                                  size="sm"
                                  onChange={(newValue) => {
                                    handleFormChange({
                                      fieldName: 'fullName',
                                      value: newValue,
                                      onChange
                                    })
                                    return debouncedFormChange({
                                      fieldName: 'fullName',
                                      value: newValue,
                                      triggerValidate
                                    })
                                  }}
                                  value={value}
                                  destructive={formState.errors && !!formState.errors?.fullName}
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      ) : null}
                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.email && permittedFields?.email ? (
                        <Controller
                          control={control}
                          name="email"
                          render={({ field: { onChange, value } }) => {
                            return (
                              <FormControlItem
                                label={`${t('candidates:tabs:candidateOverview:contactDetails:email')}`}
                                destructive={formState.errors && !!formState.errors?.email}
                                destructiveText={formState.errors && (formState.errors?.email?.message as string)}>
                                <Input
                                  isDisabled={!!resumeData?.employeeId}
                                  placeholder={t('candidates:tabs:candidateOverview:contactDetails:addEmail') as string}
                                  size="sm"
                                  onChange={(newValue) => {
                                    handleFormChange({
                                      fieldName: 'email',
                                      value: newValue ? [newValue] : [],
                                      onChange
                                    })
                                    return debouncedFormChange({
                                      fieldName: 'email',
                                      value: newValue ? [newValue] : [],
                                      triggerValidate
                                    })
                                  }}
                                  value={value && value[0]}
                                  destructive={formState.errors && !!formState.errors?.email}
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      ) : null}
                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.phoneNumber && permittedFields?.phoneNumber ? (
                        <Controller
                          control={control}
                          name="phoneNumber"
                          render={({ field: { onChange, value } }) => {
                            return (
                              <FormControlItem
                                label={`${t('candidates:tabs:candidateOverview:contactDetails:phoneNumber')}`}
                                destructive={formState.errors && !!formState.errors?.phoneNumber}
                                destructiveText={
                                  formState.errors &&
                                  (formState.errors?.phoneNumber as unknown as FieldError[])?.[0]?.message
                                }>
                                <PhoneInput
                                  countryCodeEditable
                                  placeholder={
                                    t('candidates:tabs:candidateOverview:contactDetails:addPhoneNumber') as string
                                  }
                                  country={countryCode}
                                  onChange={(newValue) => {
                                    handleFormChange({
                                      fieldName: 'phoneNumber',
                                      value: newValue ? [newValue] : [],
                                      onChange
                                    })
                                    return debouncedFormChange({
                                      fieldName: 'phoneNumber',
                                      value: newValue ? [newValue] : [],
                                      triggerValidate
                                    })
                                  }}
                                  value={value && value[0]}
                                  size="sm"
                                  destructive={formState.errors && !!formState.errors.phoneNumber}
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      ) : null}
                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.location && permittedFields?.location ? (
                        <Controller
                          control={control}
                          name="locationWithStateID"
                          render={({ field: { onChange, value } }) => {
                            return (
                              <FormControlItem
                                label={`${t('candidates:tabs:candidateOverview:contactDetails:location')}`}
                                destructive={formState.errors && !!formState.errors?.locationWithStateID}
                                destructiveText={
                                  formState.errors && (formState.errors?.locationWithStateID?.message as string)
                                }>
                                <AsyncSingleSearchWithSelect
                                  loadAsyncWhenOpen={false}
                                  promiseOptions={promiseCountryStateOptions}
                                  size="sm"
                                  onChange={(data) => {
                                    const newValue = data as ISelectOption
                                    const obj = {
                                      id: profileId,
                                      employeeId,
                                      location: newValue?.value || null,
                                      countryStateId: Number(newValue?.id),
                                      locationWithStateID: undefined
                                    }
                                    handleFormChange({
                                      fieldName: 'location',
                                      value: newValue?.value,
                                      onChange: undefined
                                    })
                                    onChange(newValue)
                                    return debouncedFormChange({
                                      fieldName: 'location',
                                      triggerValidate,
                                      triggerValidateCallback: undefined,
                                      callback: () => {
                                        triggerUpdateProfile(obj)
                                      }
                                    })
                                  }}
                                  placeholder={`${t('label:placeholder:select')}`}
                                  value={value as ISelectOption}
                                  destructive={formState.errors && !!formState.errors?.locationWithStateID}
                                  classNameOverride={{
                                    input: 'text-gray-900',
                                    loadingMessage: `${t('label:loading')}`,
                                    noOptionsMessage: `${t('label:noOptions')}`
                                  }}
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      ) : null}
                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.headline && permittedFields?.headline ? (
                        <Controller
                          control={control}
                          name="headline"
                          render={({ field: { onChange, value } }) => {
                            return (
                              <FormControlItem
                                label={`${t('candidates:tabs:candidateOverview:profileInformation:position')}`}
                                destructive={formState.errors && !!formState.errors?.headline}
                                destructiveText={formState.errors && (formState.errors?.headline?.message as string)}>
                                <Input
                                  placeholder={`${t('candidates:tabs:candidateOverview:profileInformation:add_position')}`}
                                  size="sm"
                                  onChange={(newValue) => {
                                    handleFormChange({
                                      fieldName: 'headline',
                                      value: newValue,
                                      onChange
                                    })
                                    return debouncedFormChange({
                                      fieldName: 'headline',
                                      value: newValue,
                                      triggerValidate
                                    })
                                  }}
                                  value={value}
                                  destructive={formState.errors && !!formState.errors?.headline}
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      ) : null}

                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.departments && permittedFields?.departments ? (
                        <>
                          <Controller
                            control={control}
                            name="departments"
                            defaultValue={resumeData?.permittedFields?.departments?.value?.departments || []}
                            render={({ field: { onChange, value } }) => {
                              const departments: string[] = value || []
                              const allDepartments = resumeData.permittedFields?.departments?.value?.all_departments

                              const allDepartmentsLabel = isCompanyKind
                                ? t('settings:team:allTeams')
                                : t('settings:departments:allDepartments')

                              const suggestionChipDepartments: ISelectOption[] = allDepartments
                                ? [
                                    {
                                      value: allDepartmentsLabel || '',
                                      supportingObj: {
                                        name: allDepartmentsLabel || ''
                                      },
                                      maxLength: 30
                                    } as ISelectOption
                                  ]
                                : departments.map((dep) => ({
                                    value: dep || '',
                                    supportingObj: { name: dep || '' },
                                    maxLength: 30
                                  }))

                              return (
                                <>
                                  <FormControlItem
                                    label={`${t('candidates:tabs:candidateOverview:profileInformation:department')}`}
                                    destructive={formState.errors && !!formState.errors?.departments}
                                    destructiveText={
                                      formState.errors && (formState.errors?.departments?.message as string)
                                    }>
                                    <AsyncMultipleSearchWithSelect
                                      size="sm"
                                      isClearable
                                      isDisabled
                                      onChange={() => {}}
                                      value={suggestionChipDepartments}
                                      placeholder={
                                        t('candidates:tabs:candidateOverview:profileInformation:department') as string
                                      }
                                      classNameOverride={{
                                        loadingMessage: `${t('label:loading')}`,
                                        noOptionsMessage: `${t('label:noOptions')}`
                                      }}
                                      destructive={formState.errors && !!formState.errors?.departments}
                                    />
                                  </FormControlItem>
                                </>
                              )
                            }}
                          />
                        </>
                      ) : null}

                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.totalYearsOfExp && permittedFields?.totalYearsOfExp ? (
                        <Controller
                          control={control}
                          name="totalYearsOfExp"
                          render={({ field: { onChange, value } }) => {
                            const checked = ['number', 'string'].includes(typeof value)
                            const formatTotalYearOfExp = checked
                              ? {
                                  value: String(value),
                                  supportingObj: {
                                    name:
                                      totalYoeOptions
                                        .map((item) => ({
                                          ...item,
                                          supportingObj: {
                                            name: `${t(`candidates:yoeOptions:${item.value}`)}`
                                          }
                                        }))
                                        .find((item: ISelectOption) => item.value === String(value))?.supportingObj
                                        ?.name || ''
                                  }
                                }
                              : undefined

                            return (
                              <FormControlItem
                                label={`${t('candidates:tabs:candidateOverview:profileInformation:totalYoe')}`}
                                destructive={formState.errors && !!formState.errors?.totalYearsOfExp}
                                destructiveText={
                                  formState.errors && (formState.errors?.totalYearsOfExp?.message as string)
                                }>
                                <NativeSelect
                                  size="sm"
                                  isClearable
                                  isSearchable
                                  onChange={(data) => {
                                    const newValue = (data as ISelectOption)?.value
                                    handleFormChange({
                                      fieldName: 'totalYearsOfExp',
                                      value: newValue !== undefined ? Number(newValue) : null,
                                      onChange
                                    })
                                    return noDebouncedFormChange({
                                      fieldName: 'totalYearsOfExp',
                                      value: newValue !== undefined ? Number(newValue) : null,
                                      triggerValidate
                                    })
                                  }}
                                  value={formatTotalYearOfExp}
                                  options={totalYoeOptions.map((item) => ({
                                    ...item,
                                    supportingObj: {
                                      name: `${t(`candidates:yoeOptions:${item.value}`)}`
                                    }
                                  }))}
                                  placeholder={
                                    t(
                                      'candidates:tabs:candidateOverview:profileInformation:selectTotalYearOfExp'
                                    ) as string
                                  }
                                  classNameOverride={{
                                    loadingMessage: `${t('label:loading')}`,
                                    noOptionsMessage: `${t('label:noOptions')}`
                                  }}
                                  destructive={formState.errors && !!formState.errors?.totalYearsOfExp}
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      ) : null}
                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.currentSalary && permittedFields?.currentSalary ? (
                        <div className="flex flex-row space-x-2">
                          <div className="flex-1">
                            <Controller
                              control={control}
                              name="typeOfCurrentSalary"
                              render={({ field: { onChange, value } }) => {
                                return (
                                  <FormControlItem
                                    label={`${t('candidates:tabs:candidateOverview:profileInformation:currentSalary')}`}
                                    destructive={formState.errors && !!formState.errors?.typeOfCurrentSalary}
                                    destructiveText={
                                      formState.errors && (formState.errors?.typeOfCurrentSalary?.message as string)
                                    }>
                                    <NativeSelect
                                      size="sm"
                                      isClearable={false}
                                      isSearchable={false}
                                      value={
                                        value
                                          ? ProfileTypeOfCurrentSalaryEnums.filter(
                                              (type: ISelectOption) => type.value === value
                                            )
                                          : ProfileTypeOfCurrentSalaryEnums[0]
                                      }
                                      onChange={(newValue) => {
                                        handleFormChange({
                                          fieldName: 'typeOfCurrentSalary',
                                          value: (newValue as ISelectOption).value,
                                          onChange
                                        })
                                        return noDebouncedFormChange({
                                          fieldName: 'typeOfCurrentSalary',
                                          value: (newValue as ISelectOption).value,
                                          triggerValidate
                                        })
                                      }}
                                      options={ProfileTypeOfCurrentSalaryEnums}
                                      classNameOverride={{
                                        menu: 'min-w-full',
                                        loadingMessage: `${t('label:loading')}`,
                                        noOptionsMessage: `${t('label:noOptions')}`
                                      }}
                                      menuPosition="fixed"
                                    />
                                  </FormControlItem>
                                )
                              }}
                            />
                          </div>
                          <div
                            className="flex-1"
                            style={{
                              zIndex: (section.cvTemplateCustomFields || [])?.length + 11 - index
                            }}>
                            <Controller
                              control={control}
                              name="currentSalary"
                              render={({ field: { onChange, value } }) => {
                                return (
                                  <FormControlItem
                                    label={<div className="h-5" />}
                                    destructive={formState.errors && !!formState.errors?.currentSalary}
                                    destructiveText={
                                      formState.errors && (formState.errors?.currentSalary?.message as string)
                                    }>
                                    <InputGroup size="sm">
                                      <Input
                                        placeholder={'0.00'}
                                        value={Number(value) > 0 ? value : ''}
                                        onChange={(newValue) => {
                                          handleFormChange({
                                            fieldName: 'currentSalary',
                                            value: Number(newValue),
                                            onChange,
                                            extrasResumePermittedFields: {
                                              currentSalaryCurrency: {
                                                value: getValues('currentSalaryCurrency')
                                              },
                                              typeOfCurrentSalary: {
                                                value:
                                                  getValues('typeOfCurrentSalary') ||
                                                  ProfileTypeOfCurrentSalaryEnums[0].value
                                              }
                                            }
                                          })
                                          return debouncedFormChange({
                                            fieldName: 'currentSalary',
                                            value: Number(newValue),
                                            triggerValidate,
                                            extrasUpdatingParams: {
                                              currentSalaryCurrency: getValues('currentSalaryCurrency'),
                                              typeOfCurrentSalary:
                                                getValues('typeOfCurrentSalary') ||
                                                ProfileTypeOfCurrentSalaryEnums[0].value
                                            }
                                          })
                                        }}
                                        destructive={formState.errors && !!formState.errors?.currentSalary}
                                      />
                                      <InputRightElement>{SelectCurrentCurrency()}</InputRightElement>
                                    </InputGroup>
                                  </FormControlItem>
                                )
                              }}
                            />
                          </div>
                        </div>
                      ) : null}
                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.expectedSalary && permittedFields?.expectedSalary ? (
                        <div className="flex flex-row space-x-2">
                          <div className="flex-1">
                            <Controller
                              control={control}
                              name="typeOfExpectedSalary"
                              render={({ field: { onChange, value } }) => {
                                return (
                                  <FormControlItem
                                    label={`${t('candidates:tabs:candidateOverview:profileInformation:expectedSalary')}`}
                                    destructive={formState.errors && !!formState.errors?.typeOfExpectedSalary}
                                    destructiveText={
                                      formState.errors && (formState.errors?.typeOfExpectedSalary?.message as string)
                                    }>
                                    <NativeSelect
                                      size="sm"
                                      isClearable={false}
                                      isSearchable={false}
                                      value={
                                        value
                                          ? ProfileTypeOfExpectedSalaryEnums.filter(
                                              (type: ISelectOption) => type.value === value
                                            )
                                          : ProfileTypeOfExpectedSalaryEnums[0]
                                      }
                                      onChange={(newValue) => {
                                        handleFormChange({
                                          fieldName: 'typeOfExpectedSalary',
                                          value: (newValue as ISelectOption).value,
                                          onChange
                                        })
                                        return noDebouncedFormChange({
                                          fieldName: 'typeOfExpectedSalary',
                                          value: (newValue as ISelectOption).value,
                                          triggerValidate
                                        })
                                      }}
                                      options={ProfileTypeOfExpectedSalaryEnums}
                                      classNameOverride={{
                                        menu: 'min-w-full',
                                        loadingMessage: `${t('label:loading')}`,
                                        noOptionsMessage: `${t('label:noOptions')}`
                                      }}
                                      menuPosition="fixed"
                                    />
                                  </FormControlItem>
                                )
                              }}
                            />
                          </div>
                          <div
                            className="flex-1"
                            style={{
                              zIndex: (section.cvTemplateCustomFields || [])?.length + 11 - index
                            }}>
                            <Controller
                              control={control}
                              name="expectedSalary"
                              render={({ field: { onChange, value } }) => {
                                return (
                                  <FormControlItem
                                    label={<div className="h-5" />}
                                    destructive={formState.errors && !!formState.errors?.expectedSalary}
                                    destructiveText={
                                      formState.errors && (formState.errors?.expectedSalary?.message as string)
                                    }>
                                    <InputGroup size="sm">
                                      <Input
                                        placeholder={'0.00'}
                                        value={Number(value) > 0 ? value : ''}
                                        onChange={(newValue) => {
                                          handleFormChange({
                                            fieldName: 'expectedSalary',
                                            value: Number(newValue),
                                            onChange,
                                            extrasResumePermittedFields: {
                                              expectedSalaryCurrency: {
                                                value: getValues('expectedSalaryCurrency')
                                              },
                                              typeOfExpectedSalary: {
                                                value:
                                                  getValues('typeOfExpectedSalary') ||
                                                  ProfileTypeOfExpectedSalaryEnums[0].value
                                              }
                                            }
                                          })
                                          return debouncedFormChange({
                                            fieldName: 'expectedSalary',
                                            value: Number(newValue),
                                            triggerValidate,
                                            extrasUpdatingParams: {
                                              expectedSalaryCurrency: getValues('expectedSalaryCurrency'),
                                              typeOfExpectedSalary:
                                                getValues('typeOfExpectedSalary') ||
                                                ProfileTypeOfExpectedSalaryEnums[0].value
                                            }
                                          })
                                        }}
                                        destructive={formState.errors && !!formState.errors?.expectedSalary}
                                      />
                                      <InputRightElement>{SelectExpertCurrency()}</InputRightElement>
                                    </InputGroup>
                                  </FormControlItem>
                                )
                              }}
                            />
                          </div>
                        </div>
                      ) : null}
                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.birthday && permittedFields?.birthday ? (
                        <Controller
                          control={control}
                          name="birthday"
                          render={({ field: { onChange, value } }) => {
                            return (
                              <FormControlItem
                                label={`${t('candidates:tabs:candidateOverview:profileInformation:birthday')}`}
                                destructive={formState.errors && !!formState.errors?.birthday}
                                destructiveText={formState.errors && (formState.errors?.birthday?.message as string)}>
                                <SingleDateWithYearOnlyPicker
                                  type="birthday"
                                  config={{
                                    onChange: (newValue) => {
                                      handleFormChange({
                                        fieldName: 'birthday',
                                        value: newValue,
                                        onChange
                                      })
                                      return debouncedFormChange({
                                        fieldName: 'birthday',
                                        value: {
                                          birth_year: newValue?.year,
                                          birth_month: newValue?.month,
                                          birth_date: newValue?.date
                                        },
                                        triggerValidate
                                      })
                                    },
                                    value: value,
                                    showClearIndicator: true
                                    // onClear: () => {
                                    //   onChange({
                                    //     date: null,
                                    //     month: null,
                                    //     year: null
                                    //   })
                                    // }
                                  }}
                                  defaultTab={!value?.month && !value?.date && !!value?.year ? 'year' : 'date'}
                                  placeholder={
                                    t(
                                      'candidates:tabs:candidateOverview:profileInformation:selectTotalYearOfExp'
                                    ) as string
                                  }
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      ) : null}
                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.profileLevel && permittedFields?.profileLevel ? (
                        <Controller
                          control={control}
                          name="profileLevel"
                          render={({ field: { onChange, value } }) => {
                            const findProfileLevel = profileLevel.find(
                              (item: ISelectOption) => String(item.value) === String(value)
                            )
                            const formatProfileLevel =
                              findProfileLevel && value
                                ? {
                                    value: value,
                                    supportingObj: {
                                      name: findProfileLevel?.supportingObj?.name || ''
                                    }
                                  }
                                : undefined

                            return (
                              <FormControlItem
                                label={`${t('candidates:tabs:candidateOverview:profileInformation:experienceLevel')}`}
                                destructive={formState.errors && !!formState.errors?.profileLevel}
                                destructiveText={
                                  formState.errors && (formState.errors?.profileLevel?.message as string)
                                }>
                                <NativeSelect
                                  size="sm"
                                  isClearable
                                  isSearchable
                                  onChange={(data) => {
                                    const newValue = data as ISelectOption
                                    handleFormChange({
                                      fieldName: 'profileLevel',
                                      value: newValue?.value || null,
                                      onChange
                                    })
                                    return noDebouncedFormChange({
                                      fieldName: 'profileLevel',
                                      value: newValue?.value || null,
                                      triggerValidate
                                    })
                                  }}
                                  value={formatProfileLevel}
                                  options={profileLevel}
                                  placeholder={
                                    t(
                                      'candidates:tabs:candidateOverview:profileInformation:selectExperienceLevel'
                                    ) as string
                                  }
                                  classNameOverride={{
                                    loadingMessage: `${t('label:loading')}`,
                                    noOptionsMessage: `${t('label:noOptions')}`
                                  }}
                                  destructive={formState.errors && !!formState.errors?.profileLevel}
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      ) : null}

                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.nationality && permittedFields?.nationality ? (
                        <Controller
                          control={control}
                          name="nationality"
                          render={({ field: { onChange, value } }) => {
                            return (
                              <FormControlItem
                                label={`${t('candidates:tabs:candidateOverview:profileInformation:nationality')}`}
                                destructive={formState.errors && !!formState.errors?.nationality}
                                destructiveText={
                                  formState.errors && (formState.errors?.nationality?.message as string)
                                }>
                                <Input
                                  placeholder={
                                    t('candidates:tabs:candidateOverview:profileInformation:inputCountry') as string
                                  }
                                  size="sm"
                                  onChange={(newValue) => {
                                    handleFormChange({
                                      fieldName: 'nationality',
                                      value: newValue,
                                      onChange
                                    })
                                    return debouncedFormChange({
                                      fieldName: 'nationality',
                                      value: newValue,
                                      triggerValidate
                                    })
                                  }}
                                  value={value}
                                  destructive={formState.errors && !!formState.errors?.nationality}
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      ) : null}
                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.willingToRelocate &&
                      permittedFields?.willingToRelocate ? (
                        <Controller
                          control={control}
                          name="willingToRelocate"
                          render={({ field: { onChange, value } }) => {
                            return (
                              <FormControlItem
                                mode="horizontal"
                                label={`${t('candidates:tabs:candidateOverview:profileInformation:willingToRelocate')}`}
                                destructive={formState.errors && !!formState.errors?.willingToRelocate}
                                destructiveText={
                                  formState.errors && (formState.errors?.willingToRelocate?.message as string)
                                }
                                widthLabel="min-w-auto">
                                <div className="mt-2">
                                  <Toggle
                                    name="willingToRelocate"
                                    isChecked={value}
                                    onCheckedChange={(checked) => {
                                      handleFormChange({
                                        fieldName: 'willingToRelocate',
                                        value: checked,
                                        onChange
                                      })
                                      return noDebouncedFormChange({
                                        fieldName: 'willingToRelocate',
                                        value: checked,
                                        triggerValidate
                                      })
                                    }}
                                    size="sm"
                                  />
                                </div>
                              </FormControlItem>
                            )
                          }}
                        />
                      ) : null}
                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.noticeToPeriodDays &&
                      permittedFields?.noticeToPeriodDays ? (
                        <Controller
                          control={control}
                          name="noticeToPeriodDays"
                          render={({ field: { onChange, value } }) => {
                            const formatNoticeOfPeriods = value
                              ? {
                                  value: value,
                                  supportingObj: {
                                    name: value
                                  }
                                }
                              : undefined
                            return (
                              <FormControlItem
                                label={`${t('candidates:tabs:candidateOverview:profileInformation:noticeOfPeriods')}`}
                                destructive={formState.errors && !!formState.errors?.noticeToPeriodDays}
                                destructiveText={
                                  formState.errors && (formState.errors?.noticeToPeriodDays?.message as string)
                                }>
                                <NativeSelect
                                  creatable
                                  size="sm"
                                  isClearable
                                  onChange={(data) => {
                                    const newValue = (data as ISelectOption)?.value
                                    handleFormChange({
                                      fieldName: 'noticeToPeriodDays',
                                      value: newValue || '',
                                      onChange
                                    })
                                    return noDebouncedFormChange({
                                      fieldName: 'noticeToPeriodDays',
                                      value: newValue || '',
                                      triggerValidate
                                    })
                                  }}
                                  value={formatNoticeOfPeriods}
                                  options={ListSuggestNoticeOfPeriod.map((item) => ({
                                    value: `${item.value} ${t('label:days')}`,
                                    supportingObj: {
                                      name: `${item.value} ${t('label:days')}`
                                    }
                                  }))}
                                  placeholder={
                                    t(
                                      'candidates:tabs:candidateOverview:profileInformation:selectTotalYearOfExp'
                                    ) as string
                                  }
                                  classNameOverride={{
                                    loadingMessage: `${t('label:loading')}`,
                                    noOptionsMessage: `${t('label:noOptions')}`
                                  }}
                                  destructive={formState.errors && !!formState.errors?.noticeToPeriodDays}
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      ) : null}
                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.skills && permittedFields?.skills ? (
                        <>
                          <Controller
                            control={control}
                            name="skills"
                            render={({ field: { onChange, value } }) => {
                              const skillString = JSON.parse(
                                JSON.stringify(resumeData?.permittedFields?.skills?.value || [])
                              )
                              const formatSkills = skillString.map((item: string) => {
                                return {
                                  value: item,
                                  supportingObj: {
                                    name: item
                                  }
                                }
                              })

                              const suggestionChipSkill = skillString.map((item: string) => {
                                return {
                                  label: item
                                }
                              })

                              return (
                                <>
                                  <FormControlItem
                                    label={`${t('candidates:tabs:candidateOverview:profileInformation:skills')}`}
                                    destructive={formState.errors && !!formState.errors?.skills}
                                    destructiveText={formState.errors && (formState.errors?.skills?.message as string)}
                                    helpIcon={
                                      isShowEquivalentsSkills ? (
                                        <Tooltip
                                          mode="icon"
                                          align="start"
                                          content={`${t('tooltip:equivalentSkills')}`}
                                          classNameAsChild="ml-1">
                                          <IconWrapper size={16} name="HelpCircle" className="text-gray-400" />
                                        </Tooltip>
                                      ) : null
                                    }>
                                    <Tooltip
                                      open={!!formState.errors?.skills?.message}
                                      content={formState.errors?.skills?.message}
                                      position="top"
                                      align="end"
                                      classNameConfig={{
                                        content: formState.errors?.skills?.message ? '' : 'hidden'
                                      }}>
                                      <AsyncMultipleSearchWithSelect
                                        creatable={allowAddNewSkill}
                                        isClearable={false}
                                        onInputChange={(inputChange) => {
                                          if ((inputChange || '').length > 0)
                                            if ((inputChange || '').length > 30) {
                                              setError('skills', {
                                                type: 'invalid_input_search',
                                                message: `${t('form:field_max_number_required', { number: 30 })}`
                                              })
                                            } else {
                                              clearErrors()
                                            }
                                        }}
                                        isValidNewOption={(inputChange, options) =>
                                          (inputChange || '').length <= 30 && (options || []).length == 0
                                        }
                                        callbackClearSearchData={() => {
                                          clearErrors()
                                        }}
                                        destructive={!!formState.errors?.skills?.message}
                                        promiseOptions={(params) => {
                                          return promiseSkillsOptions({
                                            ...params
                                          })
                                        }}
                                        size="sm"
                                        onChange={(value) => {
                                          const newValue = JSON.parse(JSON.stringify(value)) as ISelectOption[]
                                          const filterValue = newValue.filter(
                                            (item: ISelectOption) =>
                                              !item.__isNew__ || (item.__isNew__ && item.value.length < 30)
                                          )

                                          const skillsList =
                                            filterValue.length > 0
                                              ? filterValue.map((item) =>
                                                  item?.__isNew__ ? item.label : item.supportingObj?.name
                                                )
                                              : []

                                          handleFormChange({
                                            fieldName: 'skills',
                                            value: skillsList,
                                            onChange
                                          })
                                          return debouncedFormChange({
                                            fieldName: 'skills',
                                            value: skillsList,
                                            triggerValidate
                                          })
                                        }}
                                        placeholder={
                                          t('candidates:tabs:candidateOverview:profileInformation:addSkills') as string
                                        }
                                        value={formatSkills}
                                        configSelectOption={
                                          isShowEquivalentsSkills
                                            ? {
                                                option: 'checkbox',
                                                supportingText: ['description']
                                              }
                                            : { option: 'checkbox' }
                                        }
                                        classNameOverride={{
                                          loadingMessage: `${t('label:loading')}`,
                                          noOptionsMessage: `${t('label:noOptions')}`
                                        }}
                                      />
                                    </Tooltip>
                                  </FormControlItem>
                                  <TypographyText
                                    className={cn(
                                      'mt-1 text-sm text-gray-600',
                                      difference(aiSuggestsSkills || [], suggestionChipSkill, 'label').length > 0
                                        ? 'mb-1'
                                        : 'mb-3'
                                    )}>
                                    {`${t('candidates:aiSuggestsSkills')} `}
                                    <TextButton
                                      underline={false}
                                      type="primary"
                                      size="md"
                                      label={t('common:aiSuggests')}
                                      onClick={getSkillsByProfile}
                                      className="inline"
                                      isDisabled={profileUpdating}
                                    />
                                  </TypographyText>
                                  <If
                                    condition={!isGettingSuggestSkills}
                                    fallback={
                                      <IconWrapper
                                        className="animate-spin text-gray-500 dark:text-gray-400"
                                        name="Loader2"
                                        size={16}
                                      />
                                    }>
                                    {aiSuggestsSkills && (
                                      <If
                                        condition={
                                          difference(aiSuggestsSkills || [], suggestionChipSkill, 'label').length > 0
                                        }
                                        fallback={
                                          <TypographyText className="text-sm text-gray-600">
                                            {t('common:noSkillSuggests')}
                                          </TypographyText>
                                        }>
                                        <div ref={skillsContainerRef} className="relative mb-2 overflow-hidden">
                                          <div className={cn('flex flex-wrap overflow-hidden', 'max-h-full p-px')}>
                                            <SuggestionInlineChips
                                              size="sm"
                                              className="rounded-xl border-gray-100 bg-gray-100 hover:bg-gray-300"
                                              source={difference(
                                                aiSuggestsSkills || [],
                                                suggestionChipSkill,
                                                'label'
                                              ).map((skill) => ({
                                                label: String(skill.label),
                                                onClick: () => {
                                                  const newValues = [...(value || []), skill.label]
                                                  handleFormChange({
                                                    fieldName: 'skills',
                                                    value: newValues,
                                                    onChange
                                                  })
                                                  return debouncedFormChange({
                                                    fieldName: 'skills',
                                                    value: newValues,
                                                    triggerValidate
                                                  })
                                                }
                                              }))}
                                              type="default"
                                              startIconMenus="Plus"
                                              classNameChip="rounded-xl bg-transparent border-gray-100 hover:bg-gray-300"
                                              startIconMenusClassName="text-gray-500"
                                              classNameChipText="text-gray-600 font-medium text-sm rounded-none"
                                            />
                                          </div>
                                        </div>
                                        <If condition={showMoreSkills}>
                                          <div className="mx-1 flex-row">
                                            <TextButton
                                              underline={false}
                                              type="primary"
                                              size="md"
                                              label={t('common:show_more')}
                                              onClick={() => {
                                                if (skillsContainerRef.current) {
                                                  setShowMoreSkills(false)
                                                  setIsShowMoreClicked(true)
                                                  skillsContainerRef.current.style.maxHeight = 'unset'
                                                }
                                              }}
                                            />
                                          </div>
                                        </If>
                                      </If>
                                    )}
                                  </If>
                                </>
                              )
                            }}
                          />
                        </>
                      ) : null}

                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.talentPools && permittedFields?.talentPools ? (
                        <Controller
                          control={control}
                          name="profileTalentPoolIds"
                          render={({ field: { onChange, value } }) => {
                            const formatTalentPools = (resumeData?.permittedFields?.talentPools?.value || [])?.map(
                              (item: TalentPoolType) => {
                                return {
                                  value: String(item.id),
                                  supportingObj: {
                                    name: item.name
                                  }
                                }
                              }
                            )

                            return (
                              <FormControlItem
                                label={`${t('candidates:tabs:candidateOverview:profileInformation:talentPoolsNew')}`}
                                destructive={formState.errors && !!formState.errors?.profileTalentPoolIds}
                                destructiveText={
                                  formState.errors && (formState.errors?.profileTalentPoolIds?.message as string)
                                }>
                                <AsyncMultipleSearchWithSelect
                                  isClearable={false}
                                  promiseOptions={(params) => {
                                    return promiseTalentPoolOptions({
                                      ...params,
                                      employeeId: Number(employeeId)
                                    })
                                  }}
                                  size="sm"
                                  onChange={(data) => {
                                    const newValue = data as ISelectOption[]
                                    const talentPoolsList =
                                      newValue.length > 0 ? newValue.map((item) => Number(item?.value)) : []

                                    handleFormChange({
                                      fieldName: 'talentPools',
                                      value: newValue.map((item) => ({
                                        id: item.value,
                                        name: item?.supportingObj?.name
                                      })),
                                      onChange: undefined
                                    })
                                    onChange(newValue.length > 0 ? newValue.map((item) => String(item?.value)) : [])
                                    return debouncedFormChange({
                                      fieldName: 'profileTalentPoolIds',
                                      value: talentPoolsList,
                                      triggerValidate
                                    })
                                  }}
                                  placeholder={`${t('candidates:tabs:candidateOverview:profileInformation:addTalentPoolNew')}`}
                                  value={formatTalentPools}
                                  configSelectOption={{
                                    option: 'checkbox',
                                    supportingText: ['name']
                                  }}
                                  classNameOverride={{
                                    loadingMessage: `${t('label:loading')}`,
                                    noOptionsMessage: `${t('label:noOptions')}`
                                  }}
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      ) : null}

                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.preferredWorkStates &&
                      permittedFields?.preferredWorkStates ? (
                        <Controller
                          control={control}
                          name="preferredWorkStateIds"
                          render={({ field: { onChange, value } }) => {
                            const formatPreferredWorkStateIds = (
                              resumeData?.permittedFields?.preferredWorkStates?.value || []
                            )?.map((item: PreferredWorkStatesType) => {
                              return {
                                id: String(item.id),
                                value: String(item.full_name),
                                supportingObj: {
                                  name: item.full_name
                                }
                              } as ISelectOption
                            })

                            return (
                              <FormControlItem
                                label={`${t(
                                  'candidates:tabs:candidateOverview:profileInformation:preferredWorkStates'
                                )}`}
                                destructive={formState.errors && !!formState.errors?.preferredWorkStateIds}
                                destructiveText={
                                  formState.errors && (formState.errors?.preferredWorkStateIds?.message as string)
                                }>
                                <AsyncMultipleSearchWithSelect
                                  isClearable={false}
                                  promiseOptions={(params) => {
                                    return promiseCountryStateOptions({
                                      ...params
                                    })
                                  }}
                                  size="sm"
                                  onChange={(data) => {
                                    const newValue = data as ISelectOption[]
                                    const preferredWorkStatesList =
                                      newValue.length > 0 ? newValue.map((item) => Number(item?.id)) : []

                                    handleFormChange({
                                      fieldName: 'preferredWorkStates',
                                      value: newValue.map((item) => ({
                                        id: item.id,
                                        value: item.id,
                                        full_name: item?.supportingObj?.name
                                      })),
                                      onChange: undefined
                                    })
                                    onChange(newValue.length > 0 ? newValue.map((item) => String(item?.value)) : [])
                                    return debouncedFormChange({
                                      fieldName: 'preferredWorkStateIds',
                                      value: preferredWorkStatesList,
                                      triggerValidate
                                    })
                                  }}
                                  placeholder={`${t(
                                    'candidates:tabs:candidateOverview:profileInformation:addPreferredWorkStates'
                                  )}`}
                                  value={formatPreferredWorkStateIds}
                                  configSelectOption={{
                                    option: 'checkbox',
                                    supportingText: ['name']
                                  }}
                                  classNameOverride={{
                                    loadingMessage: `${t('label:loading')}`,
                                    noOptionsMessage: `${t('label:noOptions')}`
                                  }}
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      ) : null}

                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.links && permittedFields?.links ? (
                        <Controller
                          control={control}
                          name="editorLinks"
                          render={({ field: { onChange, value } }) => {
                            return (
                              <LinksGroupView
                                value={value}
                                onChange={onChange}
                                formState={
                                  formState?.errors?.editorLinks as unknown as Array<{
                                    link?: { message?: string }
                                  }>
                                }
                                control={control}
                                triggerValidate={triggerValidate}
                              />
                            )
                          }}
                        />
                      ) : null}
                      {field.key === LIST_SECTIONS_FIELD_DEFAULT.languages && permittedFields?.languages ? (
                        <Controller
                          control={control}
                          name="editorLanguages"
                          render={({ field: { onChange, value } }) => {
                            return (
                              <LanguageGroupView
                                value={value}
                                onChange={onChange}
                                formState={
                                  formState?.errors?.editorLanguages as Array<{
                                    language?: { message?: string }
                                    proficiency?: { message?: string }
                                  }>
                                }
                                control={control}
                                triggerValidate={triggerValidate}
                              />
                            )
                          }}
                        />
                      ) : null}
                    </div>
                  )
                })}
              </div>
            )
          }}
        </DynamicImportForm>
      </div>
    </div>
  )
}

export default CustomFieldGroupView
