import type { DropResult } from '@hello-pangea/dnd'
import { DragDropContext, Draggable, Droppable } from '@hello-pangea/dnd'
import type { Dispatch, SetStateAction } from 'react'
import { useContext } from 'react'
import { Trans, useTranslation } from 'react-i18next'

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '~/core/ui/Accordion'
import { openAlert } from '~/core/ui/AlertDialog'
import { Button } from '~/core/ui/Button'
import { AlertCircleFill } from '~/core/ui/FillIcons'
import { TypographyH5 } from '~/core/ui/Heading'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn } from '~/core/ui/utils'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { reorder } from '~/core/utilities/common'

import type { WorkExperiencesType } from '~/lib/features/candidates/types'
import { getWorkExpFieldFormatDate } from '~/lib/features/candidates/utilities/format'
import { mappingReorderListPosition, mappingWorkExpForm } from '~/lib/features/settings/profiles/edit/mapping'
import { useResumeStore } from '~/lib/features/settings/profiles/edit/store'
import type {
  ISectionCustomFieldParamType,
  IWorkExperienceParamType
} from '~/lib/features/settings/profiles/edit/types'
import useToastStore from '~/lib/store/toast'

import WorkExperienceFormView from '../Form/WorkExperienceFormView'
import { EditCVFormContext } from '../ResumesEditorContentView'

interface IWorkExperiences extends WorkExperiencesType {
  validate?: {
    [key: string]: boolean
  }
}

const WorkExperienceGroupView = ({
  section,
  value,
  onValueChange
}: {
  section: ISectionCustomFieldParamType
  value: string
  onValueChange: Dispatch<SetStateAction<string>>
}) => {
  const { t, i18n } = useTranslation()
  const { setToast } = useToastStore()
  const { resumeData } = useResumeStore()
  const { handleFormChange, noDebouncedFormChange } = useContext(EditCVFormContext)

  // ---------- FUNCTION ---------- //

  const onDragEnd = (result: DropResult) => {
    if (!result.destination) {
      return
    }

    let newList = mappingReorderListPosition(
      reorder(resumeData?.permittedFields?.workExperiences?.value || [], result.source.index, result.destination.index)
    )

    handleFormChange({
      fieldName: 'workExperiences',
      value: newList
    })

    const cloneData = JSON.parse(JSON.stringify(newList))
    const filteredListCanCallAPI = mappingReorderListPosition(
      cloneData.filter((item: IWorkExperienceParamType) => {
        return (
          item.title &&
          item.company &&
          item.fromMonth &&
          item.fromYear &&
          ((item.toMonth && item.toYear) || item.currentWorking)
        )
      })
    )

    noDebouncedFormChange({
      fieldName: 'workExperiences',
      value: filteredListCanCallAPI.map((item: IWorkExperienceParamType) => ({
        id: item?.id ? item.id : undefined,
        title: item.title,
        company: item.company,
        fromMonth: item.fromMonth,
        fromYear: item.fromYear,
        toMonth: item.toMonth,
        toYear: item.toYear,
        location: item?.location?.value,
        countryStateId: Number(item?.location?.id),
        description: item.description,
        currentWorking: item.currentWorking,
        position: item.position,
        _destroy: false
      }))
    })
  }

  const handleAdd = () => {
    const cloneList = JSON.parse(JSON.stringify(resumeData?.permittedFields?.workExperiences?.value || []))
    const getMaxIndexValue =
      cloneList?.length === 0 ? -1 : Math.max(...cloneList.map((o: { position?: number }) => o.position || 0))

    handleFormChange({
      fieldName: 'workExperiences',
      value: [
        ...(resumeData?.permittedFields?.workExperiences?.value || []),
        {
          id: 0,
          position: getMaxIndexValue + 1
        }
      ]
    })

    onValueChange(`item-work-exp-${getMaxIndexValue + 1}`)
  }

  const handleDelete = ({ item }: { item: WorkExperiencesType }) => {
    if (item.id) {
      openAlert({
        isPreventAutoFocusDialog: false,
        className: 'w-[480px]',
        title: `${t('candidates:tabs:candidateOverview:workExperiences:deleteTitle')}`,
        description: (
          <Trans
            i18nKey="candidates:tabs:candidateOverview:workExperiences:deleteDescription"
            values={{
              title: item.title
            }}>
            <span className="font-medium text-gray-900" />
          </Trans>
        ),
        actions: [
          {
            label: `${t('button:cancel')}`,
            type: 'secondary',
            size: 'sm'
          },
          {
            isCallAPI: true,
            label: `${t('button:remove')}`,
            type: 'destructive',
            size: 'sm',
            onClick: async () => {
              const cloneList = JSON.parse(JSON.stringify(resumeData?.permittedFields?.workExperiences?.value || []))

              await noDebouncedFormChange({
                fieldName: 'workExperiences',
                value: [
                  {
                    ...item,
                    _destroy: true
                  }
                ],
                triggerValidate: undefined,
                triggerValidateCallback: undefined,
                callback: (result: { error: { graphQLErrors: Array<object> } }) => {
                  if (result.error) {
                    catchErrorFromGraphQL({
                      error: result.error,
                      setToast
                    })

                    return true
                  }

                  setToast({
                    open: true,
                    type: 'success',
                    title: `${t('notification:experience_deleted')}`
                  })
                  handleFormChange({
                    fieldName: 'workExperiences',
                    value: cloneList.filter((i: { id: string }) => String(i.id) !== String(item.id))
                  })

                  return
                }
              })
            }
          }
        ]
      })

      return
    }

    const cloneList = JSON.parse(JSON.stringify(resumeData?.permittedFields?.workExperiences?.value || []))

    handleFormChange({
      fieldName: 'workExperiences',
      value: cloneList.filter((i: { position: number }) => String(i.position) !== String(item.position))
    })
  }

  const onReorderWorkExp = () => {
    onValueChange('')
    noDebouncedFormChange({
      fieldName: 'workExsSorting',
      value: 'desc',
      callback: (val?: any) => {
        if (val) {
          const worksExp = val?.data?.profilesUpdate?.profile?.permittedFields?.workExperiences?.value
          handleFormChange({
            fieldName: 'workExperiences',
            value: [...worksExp]
          })
        }
      }
    })
  }

  // ---------- RENDER ---------- //

  return (
    <div>
      <div className="flex items-center justify-between">
        <TypographyH5 className="font-medium text-gray-900">
          {section.name || t('candidates:tabs:candidateOverview:workExperiences:heading')}
        </TypographyH5>
        <If condition={(resumeData?.permittedFields?.workExperiences?.value?.length || 0) > 1}>
          <TextButton
            size="sm"
            type="secondary"
            iconMenus="ArrowUpDown"
            label={`${t('candidates:tabs:candidateOverview:workExperiences:sortNewestFirst')}`}
            underline={false}
            onClick={onReorderWorkExp}
          />
        </If>
      </div>

      <div className="mt-2 space-y-2 pb-2">
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable isDropDisabled={false} droppableId="droppable">
            {(provided) => (
              <div {...provided.droppableProps} ref={provided.innerRef}>
                <Accordion type="single" collapsible value={value} onValueChange={onValueChange}>
                  {resumeData?.permittedFields?.workExperiences?.value?.map((item: IWorkExperiences, index: number) => {
                    const getItem = `item-work-exp-${item.position}`
                    const isSelected = value === getItem

                    const checkValidation = () => {
                      if (Object.values(item.validate || {}).includes(false)) {
                        if (item?.validate?.toYear === false && item?.currentWorking === true) {
                          return false
                        }
                        return true
                      }

                      return false
                    }

                    return (
                      <Draggable isDragDisabled={isSelected} key={index} draggableId={String(index)} index={index}>
                        {(provided) => (
                          <div
                            className="group/edit relative"
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}>
                            <div className="absolute top-2.5 -left-4 w-4 opacity-0 group-hover/edit:opacity-100">
                              <Tooltip content={t('tooltip:dragToReorder')}>
                                <IconWrapper name="GripVertical" size={16} className="text-gray-400" />
                              </Tooltip>
                            </div>

                            <>
                              <div className="py-2">
                                <AccordionItem value={getItem}>
                                  <AccordionTrigger>
                                    <div className="flex space-x-1 pr-7">
                                      <div className="min-h-[14px] min-w-[14px] py-[3px]">
                                        <IconWrapper
                                          name="ChevronRight"
                                          size={14}
                                          className={cn(
                                            'transition-all',
                                            isSelected ? 'text-primary-400 rotate-90' : 'text-gray-600'
                                          )}
                                        />
                                      </div>
                                      <div className="flex flex-1 flex-col space-y-0.5">
                                        {!item.title && !item.company ? (
                                          <TypographyText
                                            className={cn(
                                              'group-hover/edit:text-primary-400 text-sm font-medium',
                                              isSelected ? 'text-primary-400' : 'text-gray-900'
                                            )}>
                                            {t('label:undefined')}
                                          </TypographyText>
                                        ) : (
                                          <>
                                            <TypographyText
                                              className={cn(
                                                'group-hover/edit:text-primary-400 text-sm font-medium',
                                                isSelected ? 'text-primary-400' : 'text-gray-900'
                                              )}>
                                              {item.title}
                                              {item.title && item.company ? (
                                                <span className="mx-2 my-1 inline-block h-0.5 min-h-[2px] w-0.5 min-w-[2px] rounded bg-gray-400" />
                                              ) : null}
                                              {item.company ? <>{item.company}</> : null}
                                            </TypographyText>
                                          </>
                                        )}
                                        <div>
                                          <TypographyText className="text-sm text-gray-600">
                                            {getWorkExpFieldFormatDate({
                                              type: 'from',
                                              attributes: item,
                                              locale: i18n?.language
                                            }) && (
                                              <>
                                                {getWorkExpFieldFormatDate({
                                                  type: 'from',
                                                  attributes: item,
                                                  keys: {
                                                    undefined: t('label:undefined')
                                                  },
                                                  locale: i18n?.language
                                                })}
                                                {item?.currentWorking ? (
                                                  <> - {t('label:present')}</>
                                                ) : (
                                                  <>
                                                    {` - ${getWorkExpFieldFormatDate({
                                                      type: 'to',
                                                      attributes: item,
                                                      keys: {
                                                        undefined: t('label:undefined')
                                                      },
                                                      locale: i18n?.language
                                                    })}`}
                                                  </>
                                                )}
                                              </>
                                            )}
                                          </TypographyText>
                                        </div>

                                        {checkValidation() && !isSelected ? (
                                          <div className="flex items-center">
                                            <div className="min-w-4">
                                              <AlertCircleFill />
                                            </div>
                                            <div className="ml-1">
                                              <p className="text-sm font-normal text-red-500 dark:text-red-500">
                                                {t('form:ohSnappError')}
                                              </p>
                                            </div>
                                          </div>
                                        ) : null}
                                      </div>
                                    </div>
                                  </AccordionTrigger>
                                  <AccordionContent>
                                    <div className="mt-2 bg-white px-4 pb-2">
                                      <WorkExperienceFormView index={index} defaultValue={mappingWorkExpForm(item)} />
                                    </div>
                                  </AccordionContent>
                                </AccordionItem>
                              </div>
                              <div className="h-px w-full bg-gray-100" />
                            </>

                            <div className="absolute top-1.5 right-0 opacity-0 group-hover/edit:opacity-100">
                              <Tooltip content={t('tooltip:delete')}>
                                <Button
                                  configurations="ghost"
                                  iconMenus="Trash2"
                                  size="sm"
                                  type="secondary-destructive"
                                  onClick={() => handleDelete({ item: item })}
                                />
                              </Tooltip>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    )
                  })}
                  {provided.placeholder as string}
                </Accordion>
              </div>
            )}
          </Droppable>
        </DragDropContext>
        <TextButton
          underline={false}
          size="md"
          type="primary"
          icon="leading"
          iconMenus="Plus"
          label={`${t('button:addPosition')}`}
          onClick={handleAdd}
        />
      </div>
    </div>
  )
}

export default WorkExperienceGroupView
