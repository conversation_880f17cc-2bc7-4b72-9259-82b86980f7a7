import type { DropResult } from '@hello-pangea/dnd'
import { DragDropContext, Draggable, Droppable } from '@hello-pangea/dnd'
import type { Dispatch, SetStateAction } from 'react'
import { useContext } from 'react'
import { Trans, useTranslation } from 'react-i18next'

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '~/core/ui/Accordion'
import { openAlert } from '~/core/ui/AlertDialog'
import { Button } from '~/core/ui/Button'
import { TypographyH5 } from '~/core/ui/Heading'
import IconWrapper from '~/core/ui/IconWrapper'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn } from '~/core/ui/utils'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { reorder } from '~/core/utilities/common'

import type { CertificatesType } from '~/lib/features/candidates/types'
import {
  checkCertificateFieldFormatDate,
  getCertificateFieldFormatDate
} from '~/lib/features/candidates/utilities/format'
import { mappingCertificateForm, mappingReorderListPosition } from '~/lib/features/settings/profiles/edit/mapping'
import { useResumeStore } from '~/lib/features/settings/profiles/edit/store'
import type { ICertificateParamType, ISectionCustomFieldParamType } from '~/lib/features/settings/profiles/edit/types'
import useToastStore from '~/lib/store/toast'

import CertificateFormView from '../Form/CertificateFormView'
import { EditCVFormContext } from '../ResumesEditorContentView'

interface ICertificate extends CertificatesType {
  validate?: {
    [key: string]: boolean
  }
}

const CertificateGroupView = ({
  section,
  value,
  onValueChange
}: {
  section: ISectionCustomFieldParamType
  value: string
  onValueChange: Dispatch<SetStateAction<string>>
}) => {
  const { t, i18n } = useTranslation()
  const { setToast } = useToastStore()
  const { resumeData } = useResumeStore()
  const { handleFormChange, noDebouncedFormChange } = useContext(EditCVFormContext)

  // ---------- FUNCTION ---------- //

  const onDragEnd = (result: DropResult) => {
    if (!result.destination) {
      return
    }

    let newList = mappingReorderListPosition(
      reorder(resumeData?.permittedFields?.certificates?.value || [], result.source.index, result.destination.index)
    )

    handleFormChange({
      fieldName: 'certificates',
      value: newList
    })

    const cloneData = JSON.parse(JSON.stringify(newList))
    const filteredListCanCallAPI = mappingReorderListPosition(
      cloneData.filter((item: ICertificateParamType) => {
        return item.certificateName
      })
    )

    noDebouncedFormChange({
      fieldName: 'certificates',
      value: filteredListCanCallAPI.map((item: ICertificateParamType) => ({
        id: item?.id ? item.id : undefined,
        certificateName: item.certificateName,
        institution: item.institution,
        issueMonth: item.issueMonth,
        issueYear: item.issueYear,
        position: item?.position,
        _destroy: false
      }))
    })
  }

  const handleAdd = () => {
    const cloneList = JSON.parse(JSON.stringify(resumeData?.permittedFields?.certificates?.value || []))
    const getMaxIndexValue =
      cloneList?.length === 0 ? -1 : Math.max(...cloneList.map((o: { position?: number }) => o.position || 0))

    handleFormChange({
      fieldName: 'certificates',
      value: [
        ...(resumeData?.permittedFields?.certificates?.value || []),
        {
          id: 0,
          position: getMaxIndexValue + 1
        }
      ]
    })

    onValueChange(`item-certificate-${getMaxIndexValue + 1}`)
  }

  const handleDelete = ({ item }: { item: CertificatesType }) => {
    if (item.id) {
      openAlert({
        isPreventAutoFocusDialog: false,
        className: 'w-[480px]',
        title: `${t('candidates:tabs:candidateOverview:certificates:deleteTitle')}`,
        description: (
          <Trans
            i18nKey="candidates:tabs:candidateOverview:certificates:deleteDescription"
            values={{
              title: item.certificateName
            }}>
            <span className="font-medium text-gray-900" />
          </Trans>
        ),
        actions: [
          {
            label: `${t('button:cancel')}`,
            type: 'secondary',
            size: 'sm'
          },
          {
            isCallAPI: true,
            label: `${t('button:remove')}`,
            type: 'destructive',
            size: 'sm',
            onClick: async () => {
              const cloneList = JSON.parse(JSON.stringify(resumeData?.permittedFields?.certificates?.value || []))

              await noDebouncedFormChange({
                fieldName: 'certificates',
                value: [
                  {
                    ...item,
                    _destroy: true
                  }
                ],
                triggerValidate: undefined,
                triggerValidateCallback: undefined,
                callback: (result: { error: { graphQLErrors: Array<object> } }) => {
                  if (result.error) {
                    catchErrorFromGraphQL({
                      error: result.error,
                      setToast
                    })

                    return true
                  }

                  setToast({
                    open: true,
                    type: 'success',
                    title: `${t('notification:certificate_deleted')}`
                  })
                  handleFormChange({
                    fieldName: 'certificates',
                    value: cloneList.filter((i: { id: string }) => String(i.id) !== String(item.id))
                  })

                  return
                }
              })
            }
          }
        ]
      })

      return
    }

    const cloneList = JSON.parse(JSON.stringify(resumeData?.permittedFields?.certificates?.value || []))

    handleFormChange({
      fieldName: 'certificates',
      value: cloneList.filter((i: { position: number }) => String(i.position) !== String(item.position))
    })
  }

  // ---------- RENDER ---------- //

  return (
    <div>
      <TypographyH5 className="font-medium text-gray-900">
        {section.name || t('candidates:tabs:candidateOverview:certificates:heading')}
      </TypographyH5>

      <div className="mt-2 space-y-2 pb-2">
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable isDropDisabled={false} droppableId="droppable">
            {(provided) => (
              <div {...provided.droppableProps} ref={provided.innerRef}>
                <Accordion type="single" collapsible value={value} onValueChange={onValueChange}>
                  {resumeData?.permittedFields?.certificates?.value?.map((item: ICertificate, index: number) => {
                    const getItem = `item-certificate-${item.position}`
                    const isSelected = value === getItem

                    return (
                      <Draggable isDragDisabled={isSelected} key={index} draggableId={String(index)} index={index}>
                        {(provided) => (
                          <div
                            className="group/edit relative"
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}>
                            <div className="absolute top-2.5 -left-4 w-4 opacity-0 group-hover/edit:opacity-100">
                              <Tooltip content={t('tooltip:dragToReorder')}>
                                <IconWrapper name="GripVertical" size={16} className="text-gray-400" />
                              </Tooltip>
                            </div>

                            <>
                              <div className="py-2">
                                <AccordionItem value={getItem}>
                                  <AccordionTrigger>
                                    <div className="flex space-x-1 pr-7">
                                      <div className="min-h-[14px] min-w-[14px] py-[3px]">
                                        <IconWrapper
                                          name="ChevronRight"
                                          size={14}
                                          className={cn(
                                            'transition-all',
                                            isSelected ? 'text-primary-400 rotate-90' : 'text-gray-600'
                                          )}
                                        />
                                      </div>
                                      <div className="flex flex-1 flex-col space-y-0.5">
                                        <TypographyText
                                          className={cn(
                                            'group-hover/edit:text-primary-400 text-sm font-medium',
                                            isSelected ? 'text-primary-400' : 'text-gray-900'
                                          )}>
                                          {item.certificateName || `${t('label:undefined')}`}
                                          {item.certificateName && item.institution ? (
                                            <span className="mx-2 my-1 inline-block h-0.5 min-h-[2px] w-0.5 min-w-[2px] rounded bg-gray-400" />
                                          ) : null}
                                          {item.institution ? <>{item.institution}</> : null}
                                        </TypographyText>

                                        {checkCertificateFieldFormatDate({
                                          attributes: item
                                        }) ? (
                                          <div>
                                            <TypographyText className="text-sm text-gray-600">
                                              {getCertificateFieldFormatDate({
                                                attributes: item,
                                                locale: i18n.language
                                              })}
                                            </TypographyText>
                                          </div>
                                        ) : null}
                                      </div>
                                    </div>
                                  </AccordionTrigger>
                                  <AccordionContent>
                                    <div className="mt-2 bg-white px-4 pb-2">
                                      <CertificateFormView index={index} defaultValue={mappingCertificateForm(item)} />
                                    </div>
                                  </AccordionContent>
                                </AccordionItem>
                              </div>
                              <div className="h-px w-full bg-gray-100" />
                            </>

                            <div className="absolute top-1.5 right-0 opacity-0 group-hover/edit:opacity-100">
                              <Tooltip content={t('tooltip:delete')}>
                                <Button
                                  configurations="ghost"
                                  iconMenus="Trash2"
                                  size="sm"
                                  type="secondary-destructive"
                                  onClick={() => handleDelete({ item })}
                                />
                              </Tooltip>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    )
                  })}
                  {provided.placeholder as string}
                </Accordion>
              </div>
            )}
          </Droppable>
        </DragDropContext>

        <TextButton
          underline={false}
          size="md"
          type="primary"
          icon="leading"
          iconMenus="Plus"
          label={`${t('button:addCertificate')}`}
          onClick={handleAdd}
        />
      </div>
    </div>
  )
}

export default CertificateGroupView
