import type { DropResult } from '@hello-pangea/dnd'
import { DragDropContext, Draggable, Droppable } from '@hello-pangea/dnd'
import type { Dispatch, SetStateAction } from 'react'
import { useContext } from 'react'
import { Trans, useTranslation } from 'react-i18next'

import useStaticData from 'src/hooks/data/use-static-data'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '~/core/ui/Accordion'
import { openAlert } from '~/core/ui/AlertDialog'
import { Button } from '~/core/ui/Button'
import { AlertCircleFill } from '~/core/ui/FillIcons'
import { TypographyH5 } from '~/core/ui/Heading'
import IconWrapper from '~/core/ui/IconWrapper'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn } from '~/core/ui/utils'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { reorder } from '~/core/utilities/common'

import { convertTimezone } from '~/lib/features/calendar/utilities/helper-schedule-interview'
import type { EducationsType } from '~/lib/features/candidates/types'
import { getEducationFieldFormatDate } from '~/lib/features/candidates/utilities/format'
import { mappingEducationForm, mappingReorderListPosition } from '~/lib/features/settings/profiles/edit/mapping'
import { useResumeStore } from '~/lib/features/settings/profiles/edit/store'
import type { IEducationParamType, ISectionCustomFieldParamType } from '~/lib/features/settings/profiles/edit/types'
import { getFormatDegreeEducation } from '~/lib/features/settings/profiles/edit/utilities'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import EducationFormView from '../Form/EducationFormView'
import { EditCVFormContext } from '../ResumesEditorContentView'

interface IEducation extends EducationsType {
  validate?: {
    [key: string]: boolean
  }
}

const EducationGroupView = ({
  section,
  value,
  onValueChange
}: {
  section: ISectionCustomFieldParamType
  value: string
  onValueChange: Dispatch<SetStateAction<string>>
}) => {
  const { t, i18n } = useTranslation()
  const { user } = useBoundStore()
  const { setToast } = useToastStore()
  const { resumeData } = useResumeStore()
  const { handleFormChange, noDebouncedFormChange } = useContext(EditCVFormContext)

  const degrees = useStaticData({
    keyName: 'degrees',
    locale: i18n.language
  })
  const listDegrees = degrees.map((item: { name: string; description: string }) => {
    return {
      value: item.name,
      supportingObj: {
        name: item.description
      }
    }
  })

  // ---------- FUNCTION ---------- //

  const onDragEnd = (result: DropResult) => {
    if (!result.destination) {
      return
    }

    let newList = mappingReorderListPosition(
      reorder(resumeData?.permittedFields?.educations?.value || [], result.source.index, result.destination.index)
    )

    handleFormChange({
      fieldName: 'educations',
      value: newList
    })

    const cloneData = JSON.parse(JSON.stringify(newList))
    const filteredListCanCallAPI = mappingReorderListPosition(
      cloneData.filter((item: IEducationParamType) => {
        const isValueFrom = typeof item?.from === 'string' ? true : item?.from?.year && item?.from?.month
        const isValueTo = typeof item?.to === 'string' ? true : item?.to?.year && item?.to?.month

        return item.schoolName && isValueFrom && isValueTo
      })
    )

    noDebouncedFormChange({
      fieldName: 'educations',
      value: filteredListCanCallAPI.map((item: IEducationParamType) => ({
        id: item?.id ? item.id : undefined,
        schoolName: item.schoolName,
        degree: item.degree,
        from:
          typeof item?.from === 'string'
            ? item?.from
            : `${item?.from?.year}-${item?.from?.month}-01T00:00:00${convertTimezone(String(user?.timezone))}`,
        to:
          typeof item?.to === 'string'
            ? item.to
            : `${item?.to?.year}-${item?.to?.month}-01T00:00:00${convertTimezone(String(user?.timezone))}`,
        degreeSubject: item.degreeSubject,
        description: item.description,
        position: item?.position,
        _destroy: false
      }))
    })
  }

  const handleAdd = () => {
    const cloneList = JSON.parse(JSON.stringify(resumeData?.permittedFields?.educations?.value || []))
    const getMaxIndexValue =
      cloneList?.length === 0 ? -1 : Math.max(...cloneList.map((o: { position?: number }) => o.position || 0))

    handleFormChange({
      fieldName: 'educations',
      value: [
        ...(resumeData?.permittedFields?.educations?.value || []),
        {
          id: 0,
          position: getMaxIndexValue + 1
        }
      ]
    })

    onValueChange(`item-education-${getMaxIndexValue + 1}`)
  }

  const handleDelete = ({ item }: { item: EducationsType }) => {
    if (item.id) {
      openAlert({
        isPreventAutoFocusDialog: false,
        className: 'w-[480px]',
        title: `${t('candidates:tabs:candidateOverview:education:deleteTitle')}`,
        description: (
          <Trans
            i18nKey="candidates:tabs:candidateOverview:education:deleteDescription"
            values={{
              title: item.degreeSubject
            }}>
            <span className="font-medium text-gray-900" />
          </Trans>
        ),
        actions: [
          {
            label: `${t('button:cancel')}`,
            type: 'secondary',
            size: 'sm'
          },
          {
            isCallAPI: true,
            label: `${t('button:remove')}`,
            type: 'destructive',
            size: 'sm',
            onClick: async () => {
              const cloneList = JSON.parse(JSON.stringify(resumeData?.permittedFields?.educations?.value || []))

              await noDebouncedFormChange({
                fieldName: 'educations',
                value: [
                  {
                    ...item,
                    _destroy: true
                  }
                ],
                triggerValidate: undefined,
                triggerValidateCallback: undefined,
                callback: (result: { error: { graphQLErrors: Array<object> } }) => {
                  if (result.error) {
                    catchErrorFromGraphQL({
                      error: result.error,
                      setToast
                    })

                    return true
                  }

                  setToast({
                    open: true,
                    type: 'success',
                    title: `${t('notification:education_deleted')}`
                  })
                  handleFormChange({
                    fieldName: 'educations',
                    value: cloneList.filter((i: { id: string }) => String(i.id) !== String(item.id))
                  })

                  return
                }
              })
            }
          }
        ]
      })

      return
    }

    const cloneList = JSON.parse(JSON.stringify(resumeData?.permittedFields?.educations?.value || []))

    handleFormChange({
      fieldName: 'educations',
      value: cloneList.filter((i: { position: number }) => String(i.position) !== String(item.position))
    })
  }

  // ---------- RENDER ---------- //

  return (
    <div>
      <TypographyH5 className="font-medium text-gray-900">
        {section.name || t('candidates:tabs:candidateOverview:education:heading')}
      </TypographyH5>

      <div className="mt-2 space-y-2 pb-2">
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable isDropDisabled={false} droppableId="droppable">
            {(provided) => (
              <div {...provided.droppableProps} ref={provided.innerRef}>
                <Accordion type="single" collapsible value={value} onValueChange={onValueChange}>
                  {resumeData?.permittedFields?.educations?.value?.map((eduItem: IEducation, index: number) => {
                    const getItem = `item-education-${eduItem.position}`
                    const isSelected = value === getItem

                    const checkValidation = () => {
                      if (Object.values(eduItem.validate || {}).includes(false)) {
                        if (eduItem?.validate?.toYear === false) {
                          return false
                        }
                        return true
                      }

                      return false
                    }

                    return (
                      <Draggable isDragDisabled={isSelected} key={index} draggableId={String(index)} index={index}>
                        {(provided) => (
                          <div
                            className="group/edit relative"
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}>
                            <div className="absolute top-2.5 -left-4 w-4 opacity-0 group-hover/edit:opacity-100">
                              <Tooltip content={t('tooltip:dragToReorder')}>
                                <IconWrapper name="GripVertical" size={16} className="text-gray-400" />
                              </Tooltip>
                            </div>

                            <>
                              <div className="py-2">
                                <AccordionItem value={getItem}>
                                  <AccordionTrigger>
                                    <div className="flex space-x-1 pr-7">
                                      <div className="min-h-[14px] min-w-[14px] py-[3px]">
                                        <IconWrapper
                                          name="ChevronRight"
                                          size={14}
                                          className={cn(
                                            'transition-all',
                                            isSelected ? 'text-primary-400 rotate-90' : 'text-gray-600'
                                          )}
                                        />
                                      </div>
                                      <div className="flex flex-1 flex-col space-y-0.5">
                                        <div className="flex space-x-2">
                                          {!eduItem.degree && !eduItem.degreeSubject && !eduItem.schoolName ? (
                                            <TypographyText
                                              className={cn(
                                                'group-hover/edit:text-primary-400 text-sm font-medium',
                                                isSelected ? 'text-primary-400' : 'text-gray-900'
                                              )}>
                                              {t('label:undefined')}
                                            </TypographyText>
                                          ) : (
                                            <>
                                              <TypographyText
                                                className={cn(
                                                  'group-hover/edit:text-primary-400 text-sm font-medium',
                                                  isSelected ? 'text-primary-400' : 'text-gray-900'
                                                )}>
                                                {getFormatDegreeEducation({
                                                  degree: eduItem.degree,
                                                  degreeSubject: eduItem.degreeSubject,
                                                  listDegrees
                                                })}
                                                {(eduItem.degree || eduItem.degreeSubject) && eduItem.schoolName ? (
                                                  <span className="mx-2 my-1 inline-block h-0.5 min-h-[2px] w-0.5 min-w-[2px] rounded bg-gray-400" />
                                                ) : null}
                                                {eduItem.schoolName ? <>{eduItem.schoolName}</> : null}
                                              </TypographyText>
                                            </>
                                          )}
                                        </div>
                                        <div>
                                          <TypographyText className="mt-2-px text-sm text-gray-600">
                                            {getEducationFieldFormatDate({
                                              type: 'from',
                                              attributes: eduItem,
                                              locale: i18n.language
                                            }) && (
                                              <>
                                                {getEducationFieldFormatDate({
                                                  type: 'from',
                                                  attributes: eduItem,
                                                  keys: {
                                                    undefined: t('label:undefined')
                                                  },
                                                  locale: i18n.language
                                                })}
                                                {` - ${getEducationFieldFormatDate({
                                                  type: 'to',
                                                  attributes: eduItem,
                                                  keys: {
                                                    undefined: t('label:undefined')
                                                  },
                                                  locale: i18n.language
                                                })}`}
                                              </>
                                            )}
                                          </TypographyText>
                                        </div>

                                        {checkValidation() && !isSelected ? (
                                          <div className="flex items-center">
                                            <div className="min-w-4">
                                              <AlertCircleFill />
                                            </div>
                                            <div className="ml-1">
                                              <p className="text-sm font-normal text-red-500 dark:text-red-500">
                                                {t('form:ohSnappError')}
                                              </p>
                                            </div>
                                          </div>
                                        ) : null}
                                      </div>
                                    </div>
                                  </AccordionTrigger>
                                  <AccordionContent>
                                    <div className="mt-2 bg-white px-4 pb-2">
                                      <EducationFormView index={index} defaultValue={mappingEducationForm(eduItem)} />
                                    </div>
                                  </AccordionContent>
                                </AccordionItem>
                              </div>
                              <div className="h-px w-full bg-gray-100" />
                            </>

                            <div className="absolute top-1.5 right-0 opacity-0 group-hover/edit:opacity-100">
                              <Tooltip content={t('tooltip:delete')}>
                                <Button
                                  configurations="ghost"
                                  iconMenus="Trash2"
                                  size="sm"
                                  type="secondary-destructive"
                                  onClick={() => handleDelete({ item: eduItem })}
                                />
                              </Tooltip>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    )
                  })}
                  {provided.placeholder as string}
                </Accordion>
              </div>
            )}
          </Droppable>
        </DragDropContext>

        <TextButton
          underline={false}
          size="md"
          type="primary"
          icon="leading"
          iconMenus="Plus"
          label={`${t('button:addEducation')}`}
          onClick={handleAdd}
        />
      </div>
    </div>
  )
}

export default EducationGroupView
