import { useTranslation } from 'react-i18next'
import type { FC } from 'react'

import type { LucideIconName } from '~/core/ui/IconWrapper'
import IconWrapper from '~/core/ui/IconWrapper'
import { TypographyText } from '~/core/ui/Text'
import { cn } from '~/core/ui/utils'

import useBoundStore from '~/lib/store'

const SwitchTabs: FC<{
  tabs?: Array<{
    value: string
    label?: string
    iconMenus?: LucideIconName
    totalCount?: number
  }>
  currentTab?: string
  onTabChange?: (tab: string) => void
}> = ({ tabs = [], currentTab, onTabChange }) => {
  const { resetBulkValues } = useBoundStore()
  const { t } = useTranslation()
  return (
    <div className="flex flex-row space-x-2 border-b bg-white p-4">
      {tabs.map((item, index) => (
        <button
          type="button"
          key={item.value}
          className={cn(
            'relative flex h-8 flex-1 items-center justify-center space-x-2 rounded-md px-3 py-2 text-sm font-medium',
            'border hover:bg-white',
            item.value === currentTab
              ? 'border-primary-400 text-primary-400 bg-white shadow-sm'
              : 'border-gray-100 bg-white text-gray-900'
          )}
          onClick={() => {
            onTabChange && onTabChange(item.value)
            resetBulkValues()
          }}>
          {item?.iconMenus && (
            <IconWrapper
              className={cn(item.value === currentTab ? 'text-primary-400' : 'text-gray-500')}
              size={16}
              name={item.iconMenus}
            />
          )}
          {item?.label && (
            <TypographyText
              className={`text-sm font-normal ${item.value === currentTab ? 'text-primary-400' : 'text-gray-700'} `}>
              {t(`companiesListing:switchTab:${item.label.toLowerCase()}`)}
            </TypographyText>
          )}
        </button>
      ))}
    </div>
  )
}

export default SwitchTabs
