import type { FC, SetStateAction } from 'react'
import { useEffect, useRef, useState } from 'react'

import type { IPagePagination } from '~/core/ui/TablePagination'
import { adminAndMemberCanAction } from '~/core/utilities/permission'

import QueryCompaniesList from '~/lib/features/agency/companies/graphql/query-companies-list'
import type { ICompaniesFilter, ISortingCompanies } from '~/lib/features/agency/companies/types'
import { ASC_SORTING } from '~/lib/features/candidates/utilities/enum'
import useCustomFieldSettingByUser from '~/lib/features/settings/profile-fields/hooks/use-custom-field-setting-by-user'
import usePaginationGraphPage from '~/lib/hooks/use-pagination-graph-page'
import useBoundStore from '~/lib/store'

import type { FieldSettingType } from '~/components/DisplayConfig'
import useGetDefaultTime from '~/components/Reports/team-productivity/hooks/use-get-default-time'

import BulkActions from './BulkActions'
import CompaniesKanban from './CompaniesKanban'
import CompaniesListingTable from './CompaniesListingTable'

export type CompaniesViewProps = {
  setOpenCreateCompany: (value: boolean) => void
  count?: number
  setCount: (value: number) => void
  filter?: ICompaniesFilter | undefined
  changeFilter: (value: SetStateAction<ICompaniesFilter | undefined>) => void
  actions?: {
    configSwitchLayout: {
      path: Array<string>
      redirectUrls: Array<string>
    }
    setConfigSwitchLayout: (param: { path: Array<string>; redirectUrls: Array<string> }) => void
  }
  configUserDisplay?: FieldSettingType[]
}

const CompaniesView: FC<{ view: string } & CompaniesViewProps> = (props) => {
  const { view, configUserDisplay, ...rest } = props
  const { refetchMyList, setRefetchMyList, currentRole } = useBoundStore()
  const [sorting, setSorting] = useState<ISortingCompanies>({
    name: ASC_SORTING
  })
  const tableRef = useRef<any>(null)
  const getDefaultTime = useGetDefaultTime()
  const { data: customFieldProfileViewData } = useCustomFieldSettingByUser({
    objectKind: 'company'
  })
  const mappingsFilterProfileField = (customFieldProfileViewData || [])?.filter((item) => item.visibility)
  const valueLocation = rest?.filter?.location?.value?.split('*') || []
  const { data, refetch, fetchPagination, forceChangeCurrentPage, isFetching } = usePaginationGraphPage({
    queryDocumentNode: QueryCompaniesList,
    queryKey: 'my-companies-management',
    filter: {
      ...rest?.filter,
      sorting,
      jobStatistic: rest?.filter?.jobStatistic?.value !== undefined ? rest?.filter.jobStatistic.value : undefined,
      ownerIds: rest?.filter?.ownerIds?.map((item) => parseInt(item.value)),
      departmentIds: rest?.filter?.departmentIds?.map((item) => parseInt(item.value)),
      industryIds: rest?.filter?.industryIds?.map((item) => parseInt(item.value)),
      ...(rest?.filter?.dateRange?.from && rest?.filter?.dateRange?.to
        ? getDefaultTime({
            fromDatetime: rest?.filter.dateRange.from,
            toDatetime: rest?.filter.dateRange.to
          })
        : {}),
      country: valueLocation.length ? valueLocation[1] : undefined,
      state: valueLocation.length ? valueLocation[0] : undefined,
      ...(rest?.filter?.location?.id ? { countryStateId: Number(rest?.filter.location.id) } : undefined),
      companyStatusIds: rest?.filter?.companyStatusIds
        ? rest?.filter.companyStatusIds?.map((status) => Number(status?.value))
        : undefined
    }
  })
  useEffect(() => {
    if (refetchMyList) {
      refetch()
      setRefetchMyList(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refetchMyList])

  useEffect(() => {
    rest?.setCount(data?.meta?.totalRowCount || 0)
  }, [data])

  return (
    <>
      {view === 'listing' ? (
        <CompaniesListingTable
          {...rest}
          tableRef={(tableEditor: any) => {
            return (tableRef.current = tableEditor)
          }}
          setOpenCreateCompany={props.setOpenCreateCompany}
          data={data}
          refetch={refetch}
          fetchPagination={fetchPagination}
          forceChangeCurrentPage={forceChangeCurrentPage}
          isFetching={isFetching}
          sorting={sorting}
          setSorting={setSorting}
          configUserDisplay={configUserDisplay}
          mappingsFilterProfileField={mappingsFilterProfileField}
          enableRowSelection={adminAndMemberCanAction(currentRole?.code)}
        />
      ) : (
        <CompaniesKanban {...rest} />
      )}
      <BulkActions tableRef={tableRef} companies={data as IPagePagination} />
    </>
  )
}

export default CompaniesView
