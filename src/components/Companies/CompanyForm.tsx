import { useRouter } from 'next/navigation'
import type { FC } from 'react'
import { useCallback } from 'react'
import { Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import pathConfiguration from 'src/configuration/path'
import configuration from '~/configuration'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { Button } from '~/core/ui/Button'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import type { IFormAction } from '~/core/ui/Form'
import { FormControlItem } from '~/core/ui/FormControlItem'
import { Input } from '~/core/ui/Input'
import { TextButton } from '~/core/ui/TextButton'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { convertStringArrayToArray } from '~/core/utilities/common'

import MutationCreateCompany from '~/lib/features/agency/companies/graphql/mutation-add-company'
import QueryCompanyShow from '~/lib/features/agency/companies/graphql/query-company-show'
import schemaCreateCompany from '~/lib/features/agency/companies/schema/create-company-schema'
import type { CompanyDetailResponseType } from '~/lib/features/agency/companies/types/company-detail'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useToastStore from '~/lib/store/toast'

export type companyFormType = { name?: string; domain: string }

const CompanyForm: FC<{
  defaultValue?: companyFormType
  reload?: () => void
  onClickCancelButton?: () => void
}> = ({ defaultValue, reload, onClickCancelButton }) => {
  const { t } = useTranslation()
  const { clientGraphQL } = useContextGraphQL()
  const router = useRouter()

  const { trigger: createCompany, isLoading } = useSubmitCommon(MutationCreateCompany)

  const { setToast } = useToastStore()

  const fetchCompanyDetail = useCallback((domain: string, formAction: IFormAction) => {
    return clientGraphQL
      .query(QueryCompanyShow, {
        domain
      })
      .toPromise()
      .then(
        (result: { error: { graphQLErrors: Array<object> }; data: { companiesShow: CompanyDetailResponseType } }) => {
          if (result.error) {
            return catchErrorFromGraphQL({
              error: result.error,
              setToast
            })
          }

          const { companiesShow } = result.data
          if (companiesShow?.id) {
            formAction.setError('domain', {
              type: 'custom',
              message: `[${companiesShow?.id}]`
            })
            return
          }

          return
        }
      )
  }, [])

  const onFinishCallback = useCallback(
    async (data: companyFormType, formAction: IFormAction) => {
      if (isLoading) {
        return
      }

      createCompany({
        name: data.name,
        domain: data.domain
      }).then((result) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: pathConfiguration.agency.companies,
            formAction,
            setToast,
            callbackHandleStatusError422: (keys) => {
              keys.forEach((session) => {
                if (session.field === 'domain' && session.message.includes(`${t('notification:domainExisting')}`)) {
                  if (data?.domain) {
                    fetchCompanyDetail(data.domain, formAction)
                  }
                } else {
                  if (session.field && formAction.control._fields[session.field]) {
                    formAction.setError(session.field, {
                      type: 'custom',
                      message: String(session.message)
                    })
                  }
                }
              })
            }
          })
        }

        const { companiesCreate } = result.data
        if (companiesCreate?.company?.id) {
          setToast({
            open: true,
            type: 'success',
            title: `${t('companiesListing:addNewSuccess')}`
          })
          router.push(`${configuration.path.agency.companyDetail(companiesCreate?.company?.id)}`)
        }
        return
      })
    },
    [isLoading, createCompany, onClickCancelButton, setToast, t, reload]
  )

  return (
    <DynamicImportForm
      mode="onSubmit"
      id="company-form"
      className="w-full"
      schema={schemaCreateCompany(t)}
      defaultValue={defaultValue}
      onSubmit={onFinishCallback}>
      {({ formState, control, trigger, getValues, submit }) => {
        return (
          <div>
            <div className="grid gap-y-4">
              <div>
                <Controller
                  control={control}
                  name="name"
                  defaultValue={defaultValue?.name || ''}
                  render={({ field: { onChange, value } }) => (
                    <FormControlItem
                      labelRequired
                      label={`${t('companiesListing:companyForm:name')}`}
                      destructive={formState.errors && !!formState.errors?.name}
                      destructiveText={formState.errors && (formState.errors?.name?.message as string)}>
                      <Input
                        autoFocus
                        placeholder={`${t('companiesListing:companyForm:namePlaceHolder', {
                          domain: PUBLIC_APP_NAME
                        })}`}
                        size="sm"
                        onChange={onChange}
                        value={value}
                        destructive={formState.errors && !!formState.errors?.name}
                      />
                    </FormControlItem>
                  )}
                />
              </div>
              <div>
                <Controller
                  control={control}
                  name="domain"
                  defaultValue={defaultValue?.domain || ''}
                  render={({ field: { onChange, value } }) => {
                    const parseJSON = convertStringArrayToArray(formState.errors?.domain?.message as string)
                    return (
                      <FormControlItem
                        label={`${t('companiesListing:companyForm:domain')}`}
                        destructive={formState.errors && !!formState.errors?.domain}
                        destructiveText={
                          parseJSON?.length ? (
                            <div className="flex items-center">
                              {`${t('companiesListing:companyForm:domainExisting')} `}
                              <TextButton
                                onClick={() =>
                                  window.open(configuration.path.agency.companyDetail(parseJSON?.[0]), '_blank')
                                }
                                label={`${t('button:viewCompany')}`}
                                size="md"
                                underline={false}
                                className="ml-1"
                              />
                            </div>
                          ) : (
                            formState.errors?.domain?.message
                          )
                        }>
                        <Input
                          placeholder={`${t('companiesListing:companyForm:domainPlaceholder')}`}
                          size="sm"
                          onChange={onChange}
                          value={value}
                          destructive={formState.errors && !!formState.errors?.domain}
                        />
                      </FormControlItem>
                    )
                  }}
                />
              </div>
            </div>
            <div className="flex items-center justify-end space-x-3 pt-6">
              <Button
                label={`${t('companiesListing:companyForm:cancel')}`}
                type="secondary"
                size="sm"
                onClick={onClickCancelButton}
              />
              <Button
                isDisabled={isLoading}
                isLoading={isLoading}
                label={`${t('companiesListing:companyForm:buttonAddNew')}`}
                htmlType="submit"
                size="sm"
              />
            </div>
          </div>
        )
      }}
    </DynamicImportForm>
  )
}

export default CompanyForm
