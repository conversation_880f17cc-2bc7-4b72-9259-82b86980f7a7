import type { FC } from 'react'
import { useState } from 'react'
import { Trans, useTranslation } from 'react-i18next'
import type { UseQueryExecute } from 'urql'

import { openAlert } from '~/core/ui/AlertDialog'
import { IconButton } from '~/core/ui/IconButton'
import If from '~/core/ui/If'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'

import type { CompanyDetailType, LocationCompanyDetailType } from '~/lib/features/agency/companies/types/company-detail'
import type { LocationForm } from '~/lib/features/settings/locations/types'
import useToastStore from '~/lib/store/toast'

import LocationModal from './LocationModal'

const LocationView: FC<{
  companyId: number
  data: LocationCompanyDetailType[]
  onUpdateProfile: (data: CompanyDetailType) => Promise<boolean | void>
  callbackSubmitLocation?: UseQueryExecute | (() => Promise<void>)
  isShowAction?: boolean
}> = ({ companyId, data, onUpdateProfile, callbackSubmitLocation, isShowAction }) => {
  const { t } = useTranslation()
  const { setToast } = useToastStore()
  const [openLocationModal, setOpenLocationModal] = useState<{
    open: boolean
    defaultValue?: LocationForm
  }>({
    open: false
  })

  return (
    <div className="space-y-1.5 py-1.5 pl-2">
      {data?.length > 0 &&
        data?.map((item, index) => {
          return (
            <div className="group" key={index}>
              <div className="relative flex justify-between">
                <Tooltip
                  content={[item?.address, item.city, item.state, item.country].filter((item) => !!item).join(', ')}>
                  <div className="mr-1 line-clamp-1 text-sm text-gray-900">
                    {[item.state, item.country].filter((item) => !!item).join(', ')}
                  </div>
                </Tooltip>

                <If condition={isShowAction}>
                  <div className="relative top-2 right-0">
                    <div className="absolute right-0 bottom-0 hidden rounded border-[1px] border-solid border-gray-100 bg-white p-0.5 shadow-sm group-hover:flex">
                      <div className="mr-1">
                        <IconButton
                          onClick={() => {
                            setOpenLocationModal({
                              open: true,
                              defaultValue: {
                                ...item,
                                id: String(item.id),
                                country: {
                                  value: item.country,
                                  supportingObj: {
                                    name: item.country
                                  }
                                },
                                state: {
                                  value: item.state,
                                  supportingObj: {
                                    name: item.state
                                  }
                                }
                              }
                            })
                          }}
                          type="secondary"
                          size="xs"
                          iconMenus="Edit3Icon"
                        />
                      </div>

                      <IconButton
                        onClick={() => {
                          openAlert({
                            className: 'w-[480px]',
                            title: `${t('settings:locations:removeLocationAlert:title')}`,
                            description: (
                              <Trans
                                i18nKey="settings:locations:removeLocationAlert:content"
                                values={{
                                  name: [item?.address, item.city, item.state, item.country]
                                    .filter((item) => !!item)
                                    .join(', ')
                                }}>
                                <span className="font-medium text-gray-900" />
                              </Trans>
                            ),
                            actions: [
                              {
                                label: `${t('button:cancel')}`,
                                type: 'secondary',
                                size: 'sm'
                              },
                              {
                                isCallAPI: true,
                                label: `${t('button:delete')}`,
                                type: 'destructive',
                                size: 'sm',
                                onClick: async (e) => {
                                  await onUpdateProfile({
                                    locations: [
                                      {
                                        ...item,
                                        _destroy: true
                                      }
                                    ]
                                  })

                                  setToast({
                                    open: true,
                                    type: 'success',
                                    title: `${t('notification:settings:locations:success_delete')}`
                                  })
                                }
                              }
                            ]
                          })
                        }}
                        type="secondary-destructive"
                        size="xs"
                        iconMenus="Trash2"
                      />
                    </div>
                  </div>
                </If>
              </div>
            </div>
          )
        })}

      <If condition={isShowAction}>
        <div>
          <TextButton
            iconMenus="Plus"
            underline={false}
            size="md"
            label={`${t('button:add')}`}
            onClick={() => {
              setOpenLocationModal({
                open: true
              })
              // setDefaultValue({
              //   index: data?.permittedFields?.languages?.value?.length
              // })
            }}
          />
        </div>
      </If>

      <LocationModal
        companyId={companyId}
        openAddLocation={openLocationModal.open}
        defaultValue={openLocationModal.defaultValue}
        onOpenChange={(open) => setOpenLocationModal({ open: false })}
        callback={callbackSubmitLocation}
        onSubmit={onUpdateProfile}
      />
    </div>
  )
}

export default LocationView
