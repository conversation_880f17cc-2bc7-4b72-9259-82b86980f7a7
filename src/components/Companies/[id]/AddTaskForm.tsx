import { formatISO } from 'date-fns'
import type { Dispatch, FC, SetStateAction } from 'react'
import type { FieldValues } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import pathConfiguration from 'src/configuration/path'
import type { IRouterWith<PERSON> } from '~/core/@types/global'
import type { ISelectOption } from '~/core/ui/Select'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import CreateTaskCompanyMutation from '~/lib/features/agency/companies/graphql/create-task-company-mutation'
import type { TaskFormInActiveType } from '~/lib/features/tasks/types'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import TaskForm from './TaskForm'

const AddTaskForm: FC<{
  autoFocus?: boolean
  callback?: () => void
  formInActive?: TaskFormInActiveType
  setFormInActive: Dispatch<SetStateAction<TaskFormInActiveType | undefined>>
  companyId?: IRouterWithID
}> = ({ autoFocus, callback, formInActive, setFormInActive, companyId }) => {
  const { t } = useTranslation()
  const { trigger, isLoading } = useSubmitCommon(CreateTaskCompanyMutation)
  const { setToast } = useToastStore()
  const user = useBoundStore((state) => state.user)

  const onFinish = async (data: FieldValues) => {
    if (isLoading) return
    const formatData = {
      title: data.title,
      ...(data.dueDate ? { dueDate: formatISO(data.dueDate) } : {}),
      assigneeIds: data.assigneeIds.map((assignee: ISelectOption) => Number(assignee.value)),
      companyId: Number(companyId)
    }

    trigger(formatData).then((result) => {
      if (result.error) {
        return catchErrorFromGraphQL({
          error: result.error,
          page: pathConfiguration.tasks.list,
          setToast
        })
      }
      const { companyTasksCreate } = result.data
      if (companyTasksCreate?.task?.id) {
        setToast({
          open: true,
          type: 'success',
          title: `${t('company:task_tab:task_created')}`,
          classNameConfig: {
            viewport: 'mb-[48px]'
          }
        })

        setFormInActive(undefined)
        callback && callback()
      }

      return
    })
  }

  const onClose = () => {
    setFormInActive(undefined)
  }

  return (
    <TaskForm
      autoFocus={autoFocus}
      formInActive={formInActive}
      companyId={Number(companyId)}
      isEdited={false}
      isExpand={formInActive?.type === 'add'}
      setIsExpand={(value) => setFormInActive(value ? { type: 'add', taskId: undefined } : undefined)}
      onFinish={onFinish}
      onClose={onClose}
      defaultValue={{
        assigneeIds: [
          {
            value: String(user.id),
            avatar: user.avatarVariants?.thumb?.url,
            avatarVariants: user.avatarVariants,
            supportingObj: {
              name: user.fullName || user.email || '',
              defaultColour: user.defaultColour
            }
          }
        ]
      }}
    />
  )
}

export default AddTaskForm
