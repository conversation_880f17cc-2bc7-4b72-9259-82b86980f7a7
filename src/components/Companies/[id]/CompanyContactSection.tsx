import { useRouter } from 'next/navigation'
import type { FC } from 'react'
import { useCallback, useRef, useState } from 'react'
import { Trans, useTranslation } from 'react-i18next'
import type { UseQueryExecute } from 'urql'

import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { openAlert } from '~/core/ui/AlertDialog'
import { Avatar } from '~/core/ui/Avatar'
import type { IColorBadgeType } from '~/core/ui/Badge'
import { Badge } from '~/core/ui/Badge'
import { Dialog } from '~/core/ui/Dialog'
import type { IFormAction } from '~/core/ui/Form'
import { IconButton } from '~/core/ui/IconButton'
import If from '~/core/ui/If'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import type { CompanyContactFormType, CompanyContactType } from '~/lib/features/agency/companies/types/company-detail'
import MutationContactCreate from '~/lib/features/agency/contacts/graphql/mutation-contact-create'
import MutationContactUpdate from '~/lib/features/agency/contacts/graphql/mutation-contact-update'
import MutationDeleteContact from '~/lib/features/agency/contacts/graphql/mutation-delete-contact'
import QueryContactsShow from '~/lib/features/agency/contacts/graphql/query-contact-show'
import type { IContactDetailType } from '~/lib/features/agency/contacts/types'
import { CONTACT_COLOR_STATUS } from '~/lib/features/agency/contacts/utilities/enum'
import usePermissionCompany from '~/lib/features/permissions/hooks/use-permission-company'
import useHiringPortalSetting from '~/lib/features/settings/company-settings/hooks/useHiringPortalSetting'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import { useRouterContext } from '~/lib/next/use-router-context'
import useToastStore from '~/lib/store/toast'

import CompanyContactForm from './CompanyContactForm'

const ShowMoreList: FC<{
  list?: CompanyContactType[]
  max?: number
  onClickEditButton?: (data: CompanyContactType) => void
  onClickDeleteButton?: (data: CompanyContactType) => void
  companyName: string
}> = ({ list = [], max = 3, onClickEditButton, onClickDeleteButton, companyName }) => {
  const { t } = useTranslation()
  const [showFullList, setShowFullList] = useState<boolean>(false)
  const { actionCompanyContact } = usePermissionCompany()
  const { enableHiringPortal } = useHiringPortalSetting()
  return (
    <div>
      {list.slice(0, showFullList ? list.length : max).map((item, index) => {
        let permittedFields = item?.permittedFields
        const fullName = [permittedFields?.firstName?.value, permittedFields?.lastName?.value]
          .filter((name) => !!name)
          .join(' ')

        return (
          <div key={`contact-${index}`} className="group relative mb-3 flex items-center space-x-2">
            <div>
              <Avatar src={undefined} alt={fullName} size="md" excludeRandColor={['#FFFFFF']} />
            </div>
            <div className="w-full">
              <div className="mb-0.5 flex items-center">
                <div className="contents">
                  <div className="max-w-[50%]">
                    <Tooltip content={fullName}>
                      <TypographyText className="line-clamp-1 w-full text-sm font-medium break-all text-gray-900">
                        {fullName}
                      </TypographyText>
                    </Tooltip>
                  </div>
                  {permittedFields?.title?.value && (
                    <div className="max-w-[50%]">
                      <div className="ml-1 flex w-full text-sm text-gray-600">
                        (
                        <Tooltip content={permittedFields?.title?.value}>
                          <span className="line-clamp-1 break-all">{permittedFields?.title?.value}</span>
                        </Tooltip>
                        )
                      </div>
                    </div>
                  )}
                </div>
                {enableHiringPortal && item.status && (
                  <Badge
                    className="ml-1 flex-none"
                    size="sm"
                    radius="circular"
                    color={CONTACT_COLOR_STATUS(item.status).color as IColorBadgeType}>
                    {item.statusDescription}
                  </Badge>
                )}
              </div>
              <div className="flex items-center space-x-1.5 text-sm text-gray-700">
                <div className="line-clamp-1 break-all">
                  <Tooltip content={permittedFields?.email?.value}>
                    <div>{permittedFields?.email?.value}</div>
                  </Tooltip>
                </div>
                {permittedFields?.phoneNumber?.value && (
                  <>
                    <div className="h-0.5 w-0.5 flex-none rounded bg-gray-600" />
                    <div className="flex-none">{permittedFields?.phoneNumber?.value}</div>
                  </>
                )}
              </div>
            </div>

            <If condition={actionCompanyContact.update || actionCompanyContact.delete}>
              <div className="absolute top-[7px] right-0 hidden items-center rounded border-[1px] border-solid border-gray-100 bg-white p-0.5 shadow-sm group-hover:flex">
                <If condition={actionCompanyContact.update}>
                  <div className="mr-1">
                    <IconButton
                      onClick={() => {
                        onClickEditButton && onClickEditButton(item)
                      }}
                      type="secondary"
                      size="xs"
                      iconMenus="Edit3Icon"
                    />
                  </div>
                </If>

                <If condition={actionCompanyContact.delete}>
                  <IconButton
                    onClick={() => {
                      openAlert({
                        isPreventAutoFocusDialog: false,
                        className: 'w-[480px]',
                        title: `${t('company:contact:delete_modal:title')}`,
                        description: (
                          <Trans
                            i18nKey="company:contact:delete_modal:description"
                            values={{
                              contact_name: fullName,
                              company_name: companyName
                            }}>
                            <span className="font-medium text-gray-900" />
                          </Trans>
                        ),
                        actions: [
                          {
                            label: `${t('button:cancel')}`,
                            type: 'secondary',
                            size: 'sm'
                          },
                          {
                            label: `${t('button:delete')}`,
                            type: 'destructive',
                            size: 'sm',
                            onClick: () => {
                              onClickDeleteButton && onClickDeleteButton(item)
                            }
                          }
                        ]
                      })
                    }}
                    type="secondary-destructive"
                    size="xs"
                    iconMenus="Trash2"
                  />
                </If>
              </div>
            </If>
          </div>
        )
      })}
      {list.length > max && (
        <TextButton
          size="md"
          type="secondary"
          underline={false}
          label={showFullList ? `${t('company:contact:show_less')}` : `${t('company:contact:show_more')}`}
          onClick={() => setShowFullList(!showFullList)}
        />
      )}
    </div>
  )
}

const CompanyContactSection: FC<{
  companyId: number
  companyName: string
  data?: CompanyContactType[]
  fetchCompanyProfile?: UseQueryExecute | (() => Promise<void>)
}> = ({ companyId, data = [], fetchCompanyProfile, companyName }) => {
  const { t } = useTranslation()
  const { pathname, params } = useRouterContext()
  const contactRef = useRef<HTMLDivElement>(null)
  const { setToast } = useToastStore()
  const [openAddContactModal, setOpenAddContactModal] = useState<boolean>(false)
  const [editContactModal, setEditContactModal] = useState<{
    open: boolean
    defaultValue?: CompanyContactFormType
  }>({ open: false })
  const { actionCompanyContact } = usePermissionCompany()
  const { trigger: triggerContactCreate, isLoading: loadingContactCreate } = useSubmitCommon(MutationContactCreate)
  const { trigger: triggerContactUpdate, isLoading: loadingContactUpdate } = useSubmitCommon(MutationContactUpdate)
  const { trigger: deleteContact } = useSubmitCommon(MutationDeleteContact)
  const { clientGraphQL } = useContextGraphQL()
  const router = useRouter()

  const fetchContactAndCheckContact = useCallback(
    (
      email: string,
      formAction: IFormAction,
      session: {
        field: string
        message: string
      }
    ) => {
      return clientGraphQL
        .query(QueryContactsShow, {
          email
        })
        .toPromise()
        .then((result: { error: { graphQLErrors: Array<object> }; data: { contactsShow: IContactDetailType } }) => {
          if (result.error) {
            return catchErrorFromGraphQL({
              error: result.error,
              setToast,
              router: {
                push: router.push,
                pathname,
                params
              }
            })
          }

          const { contactsShow } = result.data
          if (contactsShow?.id) {
            formAction.setError('email', {
              type: 'custom',
              message: `[${contactsShow?.id}]`
            })
          } else {
            formAction?.setError('email', {
              type: 'custom',
              message: session.message
            })
          }

          return
        })
    },
    []
  )
  const onSubmitAddForm = useCallback(
    async (data: CompanyContactFormType, formAction?: IFormAction) => {
      if (loadingContactCreate) {
        return
      }
      triggerContactCreate({
        title: data.title,
        firstName: data.firstName || '',
        lastName: data.lastName,
        phoneNumber: !!data.phoneNumber && data.phoneNumber !== data.countryCode ? data.phoneNumber : '',
        email: data.email || '',
        companyIds: companyId
      }).then((result) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast,
            callbackHandleStatusError422: (keys) => {
              keys.forEach((session) => {
                if (session.field === 'email') {
                  if (data?.email && formAction) {
                    fetchContactAndCheckContact(data.email, formAction, session)
                  }
                } else {
                  if (session.field && formAction?.control._fields[session.field]) {
                    formAction?.setError(session.field, {
                      type: 'custom',
                      message: session.message
                    })
                  }
                }
              })
            }
          })
        }

        const { contactsCreate } = result.data
        if (contactsCreate) {
          setToast({
            open: true,
            type: 'success',
            title: `${t('company:contact:contactAdded')}`,
            classNameConfig: {
              viewport: 'mb-[48px]'
            }
          })
          setOpenAddContactModal(false)
          fetchCompanyProfile && fetchCompanyProfile()
        }
        return
      })
    },
    [loadingContactCreate]
  )
  const onSubmitEditForm = useCallback(
    async (data: CompanyContactFormType, formAction?: IFormAction) => {
      if (loadingContactUpdate) {
        return
      }
      triggerContactUpdate({
        title: data.title,
        firstName: data.firstName || '',
        lastName: data.lastName,
        phoneNumber: !!data.phoneNumber && data.phoneNumber !== data.countryCode ? data.phoneNumber : '',
        email: data.email || '',
        companyIds: companyId,
        id: Number(editContactModal?.defaultValue?.id)
      }).then((result) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast,
            callbackHandleStatusError422: (keys) => {
              keys.forEach((session) => {
                if (session.field && formAction?.control._fields[session.field]) {
                  formAction?.setError(session.field, {
                    type: 'custom',
                    message: session.message
                  })
                }
              })
            }
          })
        }

        const { contactsUpdate } = result.data
        if (contactsUpdate) {
          setToast({
            open: true,
            type: 'success',
            title: `${t('company:contact:contactUpdated')}`
          })
          setEditContactModal({ open: false })
          fetchCompanyProfile && fetchCompanyProfile()
        }
        return
      })
    },
    [loadingContactUpdate, editContactModal?.defaultValue?.id]
  )

  return (
    <>
      <div ref={contactRef} className="mb-3 flex items-center justify-between">
        <TypographyText className="text-base font-medium text-gray-900">
          {t('company:contact:contacts')} ({data?.length})
        </TypographyText>
        <If condition={actionCompanyContact.create}>
          <TextButton
            label={`${t('button:add')}`}
            size="md"
            iconMenus="Plus"
            underline={false}
            onClick={() => setOpenAddContactModal(true)}
          />
        </If>
      </div>
      <ShowMoreList
        companyName={companyName}
        list={data}
        max={3}
        onClickEditButton={(data) =>
          setEditContactModal({
            open: true,
            defaultValue: {
              ...data,
              firstName: data?.permittedFields?.firstName?.value,
              lastName: data?.permittedFields?.lastName?.value,
              email: data?.permittedFields?.email?.value,
              phoneNumber: data?.permittedFields?.phoneNumber?.value,
              title: data?.permittedFields?.title?.value
            }
          })
        }
        onClickDeleteButton={(data) =>
          deleteContact({
            id: Number(data.id),
            companyId: Number(companyId)
          }).then((result) => {
            if (result.error) {
              return catchErrorFromGraphQL({
                error: result.error,
                setToast
              })
            }
            setToast({
              open: true,
              type: 'success',
              title: `${t('company:contact:contactDeleted')}`
            })
            fetchCompanyProfile && fetchCompanyProfile()
            return
          })
        }
      />
      {!!contactRef?.current && (
        <>
          <Dialog
            className="min-w-[480px]"
            open={openAddContactModal}
            isPreventAutoFocusDialog
            dialogContainer={contactRef?.current}
            label={`${t('company:contact:add_contact')}`}
            onOpenChange={setOpenAddContactModal}>
            <CompanyContactForm onClickCancelButton={() => setOpenAddContactModal(false)} onSubmit={onSubmitAddForm} />
          </Dialog>
          <Dialog
            className="min-w-[480px]"
            open={editContactModal.open}
            dialogContainer={contactRef?.current}
            isPreventAutoFocusDialog
            label={`${t('company:contact:edit_contact')}`}
            onOpenChange={() => setEditContactModal({ open: false })}>
            <CompanyContactForm
              defaultValue={editContactModal.defaultValue}
              onSubmit={onSubmitEditForm}
              disabledFields={editContactModal?.defaultValue?.state ? ['email'] : []}
              onClickCancelButton={() => setEditContactModal({ open: false })}
            />
          </Dialog>
        </>
      )}
    </>
  )
}

export default CompanyContactSection
