import type {
  DraggableProvided,
  DraggableStateSnapshot,
  DraggingStyle,
  DroppableProvided,
  DropR<PERSON>ult,
  NotDraggingStyle
} from '@hello-pangea/dnd'
import { DragDropContext, Draggable, Droppable } from '@hello-pangea/dnd'
import { useTranslation } from 'react-i18next'
import type { FC } from 'react'
import { useCallback, useEffect, useMemo, useState } from 'react'
import useOnclickOutside from 'react-cool-onclickoutside'

import { Button } from '~/core/ui/Button'
import { DebouncedInput } from '~/core/ui/DebouncedInput'
import type { LucideIconName } from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'

import { FIELDS_USER_SETTING_DISPLAY } from '~/lib/features/candidates/utilities/enum'

import type { FieldSettingType } from '~/components/DisplayConfig'
import DisplayConfigRow from '~/components/DisplayConfig/DisplayConfigRow'
import SectionHeaderConfig from '~/components/DisplayConfig/SectionHeaderConfig'

import SwitchTabs from './SwitchTabs'

// ========================= CONSTANTS =========================
const INVISIBLE_LOCKED_FIELDS = ['createdAt', 'updatedAt']
const LIST_FIELDS_KANBAN = ['name', 'updatedAt', 'companyStatistic', 'domain', 'owner']
const PUBLIC_ID_KEY = FIELDS_USER_SETTING_DISPLAY?.publicId?.key
const SPECIAL_FIELD_NAME = 'name'

const DROPPABLE_IDS = {
  SHOW: 'droppable',
  HIDE: 'droppable2'
}

// ========================= TYPES =========================
interface CompanyDisplayConfigProps {
  fields?: FieldSettingType[]
  isDefaultFields?: boolean
  onReOrder?: (newStages: FieldSettingType[]) => Promise<void> | null
  refetchFilterList?: () => void
  resetAsDefault?: () => Promise<void>
  tabs?: Array<{
    value: string
    label?: string
    iconMenus?: LucideIconName
    totalCount?: number
  }>
  currentTab?: string
  onTabChange?: (tab: string) => void
}

// ========================= UTILITY FUNCTIONS =========================
const getItemStyle = (isDragging: boolean, draggableStyle?: DraggingStyle | NotDraggingStyle) => ({
  userSelect: 'none' as const,
  ...draggableStyle,
  background: 'white'
})

const reorderArray = <T,>(list: T[], startIndex: number, endIndex: number): T[] => {
  const result = Array.from(list)
  const [removed] = result.splice(startIndex, 1)
  if (removed) result.splice(endIndex, 0, removed)
  return result
}

const moveItemBetweenLists = (
  source: FieldSettingType[],
  destination: FieldSettingType[],
  droppableSource: { index: number; droppableId: string },
  droppableDestination: { index: number; droppableId: string }
) => {
  const sourceClone = Array.from(source)
  const destClone = Array.from(destination)
  const [removed] = sourceClone.splice(droppableSource.index, 1)

  if (removed) destClone.splice(droppableDestination.index, 0, removed)

  const isSourceVisible = droppableSource.droppableId === DROPPABLE_IDS.SHOW
  const isDestVisible = droppableDestination.droppableId === DROPPABLE_IDS.SHOW

  return {
    [droppableSource.droppableId]: sourceClone.map((item) => ({
      ...item,
      visibleValue: isSourceVisible
    })),
    [droppableDestination.droppableId]: destClone.map((item) => ({
      ...item,
      visibleValue: isDestVisible
    }))
  }
}

const cloneAndUpdate = <T,>(array: T[], index: number, newItem: T): T[] => {
  const result = [...array]
  result[index] = newItem
  return result
}

// ========================= FIELD CATEGORIZATION HOOKS =========================
const useFieldCategories = (fieldsSearchResult: FieldSettingType[] | undefined) => {
  return useMemo(() => {
    const fields = fieldsSearchResult || []

    // Filter settings (highest priority)
    const filterSettings = fields.filter((item) => !!item.filter)

    // Locked visible fields (not in invisible list)
    const lockedVisibleFields = fields.filter(
      (item) => item.locked && item.visibleValue && !INVISIBLE_LOCKED_FIELDS.includes(item.accessorKey)
    )

    // Regular draggable visible fields
    const draggableShowFields = fields.filter(
      (item) =>
        !item.filter &&
        item.visibleValue &&
        item.value !== PUBLIC_ID_KEY &&
        !item.locked &&
        item.accessorKey !== SPECIAL_FIELD_NAME
    )

    // Hidden fields
    const draggableHideFields = fields.filter(
      (item) => !item.filter && !item.visibleValue && item.value !== PUBLIC_ID_KEY
    )

    // Locked invisible fields (special case)
    const lockedInvisibleFields = fields.filter((item) => item.value === PUBLIC_ID_KEY && !item.visibleValue)

    const lockedKanbanFields = fields
      .filter((item) => LIST_FIELDS_KANBAN.includes(item.accessorKey))
      .map((f) => ({ ...f, locked: true, isDisableVisible: true }))

    // Locked fields at last position (createdAt, updatedAt)
    const lockedAtLastPosition = fields.filter(
      (item) => item.locked && item.visibleValue && INVISIBLE_LOCKED_FIELDS.includes(item.accessorKey)
    )

    return {
      filterSettings,
      lockedVisibleFields,
      draggableShowFields,
      draggableHideFields,
      lockedInvisibleFields,
      lockedAtLastPosition,
      lockedKanbanFields
    }
  }, [fieldsSearchResult])
}

// ========================= MAIN COMPONENT =========================
const CompanyDisplayConfig: FC<CompanyDisplayConfigProps> = ({
  fields,
  isDefaultFields,
  onReOrder,
  resetAsDefault,
  refetchFilterList,
  tabs,
  onTabChange,
  currentTab
}) => {
  const { t } = useTranslation()
  const [search, setSearch] = useState('')
  const [openDropdown, setOpenDropdown] = useState<boolean>(false)

  // ========================= COMPUTED VALUES =========================
  const fieldsSearchResult = useMemo(() => {
    if (!search) return fields
    return fields?.filter((field) => field.label?.toLowerCase().includes(search.toLowerCase()))
  }, [search, fields])

  const fieldCategories = useFieldCategories(fieldsSearchResult)
  const {
    filterSettings,
    lockedVisibleFields,
    draggableShowFields,
    draggableHideFields,
    lockedInvisibleFields,
    lockedAtLastPosition,
    lockedKanbanFields
  } = fieldCategories

  // ========================= HELPER FUNCTIONS =========================
  const buildFinalFieldOrder = (
    filterSettings: FieldSettingType[],
    lockedInvisible: FieldSettingType[],
    lockedVisible: FieldSettingType[],
    showFields: FieldSettingType[],
    hideFields: FieldSettingType[],
    lockedAtLast: FieldSettingType[]
  ): FieldSettingType[] => [
    ...filterSettings,
    ...lockedInvisible,
    ...lockedVisible,
    ...showFields,
    ...hideFields,
    ...lockedAtLast // 🔥 Always at the end
  ]

  const hasAnyFields = () =>
    [draggableHideFields, draggableShowFields, lockedVisibleFields, lockedInvisibleFields, lockedAtLastPosition].some(
      (list) => list.length > 0
    )

  const shouldShowVisibleSection = () =>
    [draggableShowFields, lockedVisibleFields, lockedAtLastPosition].some((list) => list.length > 0)

  // ========================= EVENT HANDLERS =========================
  const ref = useOnclickOutside(() => setOpenDropdown(false), {
    ignoreClass: 'my-ignore-action'
  })

  useEffect(() => {
    if (!openDropdown) setSearch('')
  }, [openDropdown])

  const handleResetToDefault = useCallback(() => {
    resetAsDefault?.()?.then(() => refetchFilterList?.())
  }, [resetAsDefault, refetchFilterList])

  // Hide all fields (name and locked at last position NOT affected)
  const handleHideAllFields = () => {
    const hasPublicId = lockedVisibleFields.some((f) => f.value === PUBLIC_ID_KEY)
    const processedLockedFields = hasPublicId ? lockedVisibleFields : [...lockedInvisibleFields, ...lockedVisibleFields]

    const fieldsToHide = processedLockedFields.map((f) =>
      f.value === PUBLIC_ID_KEY ? { ...f, visibleValue: false } : f
    )

    const hiddenShowFields = draggableShowFields.map((f) =>
      f.locked || f.isDisableVisible ? f : { ...f, visibleValue: false }
    )

    const newOrder = buildFinalFieldOrder(
      filterSettings,
      [],
      fieldsToHide,
      hiddenShowFields,
      draggableHideFields,
      lockedAtLastPosition // 🔥 NOT affected
    )

    onReOrder?.(newOrder)
  }

  // Show all fields (name and locked at last position NOT affected)
  const handleShowAllFields = () => {
    const visibleInvisibleFields = lockedInvisibleFields.map((f) => ({
      ...f,
      visibleValue: true
    }))

    const visibleHideFields = draggableHideFields.map((f) => ({
      ...f,
      visibleValue: true
    }))

    const newOrder = buildFinalFieldOrder(
      filterSettings,
      visibleInvisibleFields,
      lockedVisibleFields,
      draggableShowFields,
      visibleHideFields,
      lockedAtLastPosition // 🔥 NOT affected
    )

    onReOrder?.(newOrder)
  }

  const handleDragEnd = (result: DropResult) => {
    const { source, destination } = result
    if (!destination) return

    const getListForDroppable = (id: string) => (id === DROPPABLE_IDS.SHOW ? draggableShowFields : draggableHideFields)

    if (source.droppableId === destination.droppableId) {
      // Reorder within same droppable
      const reorderedItems = reorderArray(getListForDroppable(source.droppableId), source.index, destination.index)

      const isShowSection = source.droppableId === DROPPABLE_IDS.SHOW
      const newOrder = buildFinalFieldOrder(
        filterSettings,
        lockedInvisibleFields,
        lockedVisibleFields,
        isShowSection ? reorderedItems : draggableShowFields,
        isShowSection ? draggableHideFields : reorderedItems,
        lockedAtLastPosition
      )

      onReOrder?.(newOrder)
    } else {
      // Move between droppables
      const moveResult = moveItemBetweenLists(
        getListForDroppable(source.droppableId),
        getListForDroppable(destination.droppableId),
        source,
        destination
      )

      const newOrder = buildFinalFieldOrder(
        filterSettings,
        lockedInvisibleFields,
        lockedVisibleFields,
        moveResult[DROPPABLE_IDS.SHOW] ?? [],
        moveResult[DROPPABLE_IDS.HIDE] ?? [],
        lockedAtLastPosition
      )

      onReOrder?.(newOrder)
    }
  }

  const handleToggleVisibility = (
    newVisibility: boolean,
    currentItem: FieldSettingType,
    isMovingToShow: boolean = true
  ) => {
    const updatedItem = { ...currentItem, visibleValue: newVisibility }

    if (search) {
      // When searching, update the specific item directly
      const updatedFields = (fields || []).map((f) => (f.id === currentItem.id ? updatedItem : f))
      onReOrder?.(updatedFields)
      return
    }

    // Find if it's a locked field
    const sourceList = isMovingToShow ? lockedInvisibleFields : lockedVisibleFields
    const lockedIndex = sourceList.findIndex((item) => item.id === currentItem.id)

    let newOrder: FieldSettingType[]

    if (lockedIndex === -1) {
      // Regular field: move between sections
      if (isMovingToShow) {
        newOrder = buildFinalFieldOrder(
          filterSettings,
          lockedInvisibleFields,
          lockedVisibleFields,
          [...draggableShowFields, updatedItem],
          draggableHideFields.filter((f) => f.id !== currentItem.id),
          lockedAtLastPosition
        )
      } else {
        newOrder = buildFinalFieldOrder(
          filterSettings,
          lockedInvisibleFields,
          lockedVisibleFields,
          draggableShowFields.filter((f) => f.id !== currentItem.id),
          [updatedItem, ...draggableHideFields],
          lockedAtLastPosition
        )
      }
    } else {
      // Locked field: update in place
      const updatedLockedFields = cloneAndUpdate(sourceList, lockedIndex, updatedItem)

      newOrder = buildFinalFieldOrder(
        filterSettings,
        isMovingToShow ? updatedLockedFields : lockedInvisibleFields,
        isMovingToShow ? lockedVisibleFields : updatedLockedFields,
        isMovingToShow ? draggableShowFields : draggableShowFields.filter((f) => f.id !== currentItem.id),
        isMovingToShow ? draggableHideFields.filter((f) => f.id !== currentItem.id) : draggableHideFields,
        lockedAtLastPosition
      )
    }

    onReOrder?.(newOrder)
  }

  // ========================= RENDER HELPERS =========================
  const renderFieldRow = (
    item: FieldSettingType,
    index: number,
    keyPrefix: string,
    options: {
      canToggle?: boolean
      onToggle?: (value: boolean) => void
      className?: string
      isDraggable?: boolean
    } = {}
  ) => {
    const { canToggle = false, onToggle, className = '', isDraggable = false } = options

    const rowContent = <DisplayConfigRow {...item} {...(canToggle && onToggle ? { onChangeVisible: onToggle } : {})} />

    if (isDraggable) {
      return (
        <Draggable key={item.id} draggableId={item.id} index={index} isDragDisabled={!!search}>
          {(provided: DraggableProvided, snapshot: DraggableStateSnapshot) => (
            <div
              ref={provided.innerRef}
              {...provided.draggableProps}
              {...provided.dragHandleProps}
              style={getItemStyle(snapshot.isDragging, provided.draggableProps.style)}
              className={snapshot.isDragging ? 'shadow-lg' : ''}>
              {rowContent}
            </div>
          )}
        </Draggable>
      )
    }

    return (
      <div key={`${keyPrefix}-${index}`} className={className}>
        {rowContent}
      </div>
    )
  }

  const renderDisplayBoardMode = () => {
    return (
      <If condition={currentTab !== 'listing'}>
        <div className={`${isDefaultFields ? 'max-h-[602px]' : 'max-h-[458px]'} overflow-y-auto px-1`}>
          <TypographyText className="mt-4 px-3 text-sm font-medium text-gray-700">
            {t('companiesListing:displayConfig:fields')}
          </TypographyText>
          <SectionHeaderConfig
            label={t('companiesListing:displayConfig:show')}
            labelBtn={t('settings:custom_fields:displayConfig:hideAll')}
            hiddenBtn={false}
            eventBtn={handleHideAllFields}
            disabled
          />
          {lockedKanbanFields.map((item, index) => renderFieldRow(item, index, 'locked-at-last'))}
        </div>
      </If>
    )
  }

  // ========================= MAIN RENDER =========================
  return (
    <div ref={ref} className="relative z-50">
      <Button
        label={t('button:display') || ''}
        iconMenus="SlidersHorizontal"
        size="xs"
        type="secondary"
        onClick={() => setOpenDropdown(!openDropdown)}
      />

      <div
        className={`${
          openDropdown ? '' : 'hidden'
        } absolute right-0 z-50 mt-1 max-h-[662px] min-w-[364px] rounded bg-white shadow-[0px_0px_0px_1px_rgba(0,0,0,0.05),0px_4px_6px_-2px_rgba(0,0,0,0.05),0px_10px_15px_-3px_rgba(0,0,0,0.10)]`}>
        <div className="flex max-h-[662px] flex-col overflow-hidden">
          <SwitchTabs tabs={tabs} currentTab={currentTab} onTabChange={onTabChange} />

          <If condition={currentTab === 'listing'}>
            <div className="w-full px-4 pt-3">
              <DebouncedInput
                size="sm"
                value={search}
                onClear={() => setSearch('')}
                onChange={(value) => setSearch(value as string)}
                placeholder={t('settings:custom_fields:displayConfig:search') || ''}
              />
            </div>
          </If>

          <If condition={currentTab === 'listing' && !hasAnyFields()}>
            <div className="flex h-[40px] w-full items-center justify-center">
              <TypographyText className="text-sm font-medium text-gray-500">{t('label:noOptions')}</TypographyText>
            </div>
          </If>

          <If condition={hasAnyFields() && currentTab === 'listing'}>
            <div className={`${isDefaultFields ? 'max-h-[602px]' : 'max-h-[458px]'} overflow-y-auto px-1`}>
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId={DROPPABLE_IDS.SHOW}>
                  {(provided: DroppableProvided) => (
                    <div {...provided.droppableProps} ref={provided.innerRef}>
                      <If condition={shouldShowVisibleSection()}>
                        <SectionHeaderConfig
                          label={t('companiesListing:displayConfig:show')}
                          labelBtn={t('settings:custom_fields:displayConfig:hideAll')}
                          hiddenBtn={!!search}
                          eventBtn={handleHideAllFields}
                        />
                      </If>

                      {lockedVisibleFields
                        .filter((item) => item.value === PUBLIC_ID_KEY)
                        .map((item, index) =>
                          renderFieldRow(item, index, 'locked-visible-publicid', {
                            canToggle: true,
                            onToggle: (value) => handleToggleVisibility(value, item, false)
                          })
                        )}

                      {lockedVisibleFields
                        .filter((item) => item.value !== PUBLIC_ID_KEY)
                        .map((item, index) => renderFieldRow(item, index, 'locked-visible-other'))}

                      {draggableShowFields.map((item, index) =>
                        renderFieldRow(item, index, 'draggable-show', {
                          isDraggable: true,
                          canToggle: true,
                          onToggle: (value) => handleToggleVisibility(value, item, false)
                        })
                      )}

                      {provided.placeholder}

                      {lockedAtLastPosition.map((item, index) => renderFieldRow(item, index, 'locked-at-last'))}
                    </div>
                  )}
                </Droppable>

                {(draggableHideFields.length > 0 || lockedInvisibleFields.length > 0) && (
                  <Droppable droppableId={DROPPABLE_IDS.HIDE}>
                    {(provided: DroppableProvided) => (
                      <div ref={provided.innerRef} {...provided.droppableProps}>
                        <SectionHeaderConfig
                          label={t('companiesListing:displayConfig:hide')}
                          labelBtn={t('settings:custom_fields:displayConfig:showAll')}
                          eventBtn={handleShowAllFields}
                          hiddenBtn={!!search}
                        />

                        {lockedInvisibleFields.map((item, index) =>
                          renderFieldRow(item, index, 'locked-invisible', {
                            canToggle: item.value === PUBLIC_ID_KEY,
                            onToggle: (value) => handleToggleVisibility(value, item, true)
                          })
                        )}

                        {draggableHideFields.map((item, index) =>
                          renderFieldRow(item, index, 'draggable-hide', {
                            isDraggable: true,
                            canToggle: true,
                            onToggle: (value) => handleToggleVisibility(value, item, true)
                          })
                        )}

                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                )}
              </DragDropContext>
            </div>

            {!isDefaultFields && (
              <div className="flex h-10 items-center border-t border-t-gray-100">
                <TextButton
                  size="sm"
                  label={t('button:resetToDefault')}
                  className="w-full"
                  underline={false}
                  onClick={handleResetToDefault}
                />
              </div>
            )}
          </If>
          {renderDisplayBoardMode()}
        </div>
      </div>
    </div>
  )
}

export default CompanyDisplayConfig
