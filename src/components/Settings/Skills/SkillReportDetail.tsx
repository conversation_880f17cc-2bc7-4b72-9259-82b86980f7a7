'use client'

import dynamic from 'next/dynamic'
import { useRouter } from 'next/navigation'
import type { FC } from 'react'
import { useCallback, useEffect, useState } from 'react'
import { Trans, useTranslation } from 'react-i18next'
import type { AnyVariables, DocumentInput } from 'urql'

import configuration from '~/configuration'
import type { IRouterWithID } from '~/core/@types/global'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { openAlert } from '~/core/ui/AlertDialog'
import type { IBarItemChart } from '~/core/ui/BarChart'
import { BarChart } from '~/core/ui/BarChart'
import { Button } from '~/core/ui/Button'
import { Container } from '~/core/ui/Container'
import { Divider } from '~/core/ui/Divider'
import { DropdownMenu } from '~/core/ui/DropdownMenu'
import { LineChart } from '~/core/ui/LineChart'
import { Metric<PERSON>hart } from '~/core/ui/MetricChart'
import { SuggestionInlineChips } from '~/core/ui/SuggestionChips'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { detectSearchEmpty } from '~/core/utilities/common'
import { defaultFormatDate, monthYearFormatDate } from '~/core/utilities/format-date'

import type { IReportDetailApplication, IReportDetailOpenJob } from '~/lib/features/reports/types'
import QuerySkillsShowHiredJobsSummaryDetails from '~/lib/features/settings/skills/graphql/query-skill-show-hired-jobs-summary'
import QuerySkillShowJobSummaryDetail from '~/lib/features/settings/skills/graphql/query-skill-show-jobs-summary'
import QuerySkillsShowProfilesSummaryDetails from '~/lib/features/settings/skills/graphql/query-skill-show-profiles-summary'
import QuerySkillsShow from '~/lib/features/settings/skills/graphql/query-skills-detail'
import QuerySkillsShowOvertimeReport from '~/lib/features/settings/skills/graphql/query-skills-show-overtime'
import QuerySkillsShowSummary from '~/lib/features/settings/skills/graphql/query-skills-show-summary'
import QueryDeleteSkillMutation from '~/lib/features/settings/skills/graphql/submit-delete-skill-mutation'
import useSkillReportManagement from '~/lib/features/settings/skills/hooks/use-skill-report-management'
import type { ISkill, TextAnchor } from '~/lib/features/settings/skills/types'
import {
  GROUPED_CUMULATIVE_GROWTH,
  GROUPED_SUPPLY_DEMAND_TRENDS,
  METRIC_SKILL_DATA_TYPE
} from '~/lib/features/settings/skills/utilities/enum'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

import Filter from '~/components/Filter/Filter'
import LongContentDisplay from '~/components/LongContentDisplay'
import SkeletonContainer from '~/components/Skeleton'

import LineOrBarChartWrapper from './LineOrBarChartWrapper'
import MetricChartWrapper from './MetricChartWrapper'
import SkillDetailModal from './SkillDetailModal'
import SkillModal from './SkillModal'

const ResponsiveLine = dynamic(() => import('@nivo/line').then((m) => m.ResponsiveLine), { ssr: false })

const SkillReportDetail: FC<{ id: IRouterWithID }> = ({ id }) => {
  const { t, i18n } = useTranslation()
  const router = useRouter()
  const { user } = useBoundStore()
  const { setToast } = useToastStore()
  const { filterControl } = useSkillReportManagement({ user })
  const { trigger: triggerDelete, isLoading: isLoadingDelete } = useSubmitCommon(QueryDeleteSkillMutation)

  const [openSkill, setOpenSKill] = useState<{
    open: boolean
    defaultValue?: ISkill
    moveOnly?: boolean
  }>({ open: false, defaultValue: undefined, moveOnly: false })
  const [skillDetailConfig, setSkillDetailConfig] = useState<{
    open: boolean
    type?: 'application' | 'job' | 'hires'
    title?: string
    mappingData?: (dataCollection: { data: Array<any> }) => {
      data: Array<IReportDetailApplication | IReportDetailOpenJob>
    }
    query?: DocumentInput<any, AnyVariables>
    variablesFilter?: {
      stageKey?: string
      sourced?: string
      stageTypeId?: number
      fromDateTimePipelineItem?: string
      toDateTimePipelineItem?: string
    }
  }>({
    open: false
  })
  const [groupMode, setGroupMode] = useState(GROUPED_SUPPLY_DEMAND_TRENDS)
  const [filterPipelineLocal, setFilterPipelineLocal] = useState<{
    [id: string]: {
      color: string
      enable: boolean
      id: number
      bgColor: string
      hoverBgColor: string
      label: string
    }
  }>({
    jobs: {
      enable: true,
      color: 'hsl(158.76deg 55.67% 60.2%)',
      id: 1,
      bgColor: 'bg-chart-300',
      hoverBgColor: 'group-hover:bg-chart-300',
      label: `${t('settings:skills:report:overtimeReport:jobsTitle')}`
    },
    profiles: {
      enable: true,
      color: 'hsl(221.71deg 100% 67.84%)',
      id: 2,
      bgColor: 'bg-chart-100',
      hoverBgColor: 'group-hover:bg-chart-100',
      label: `${t('settings:skills:report:overtimeReport:profilesTitle')}`
    }
  })

  const {
    isLoading,
    data: skillData,
    trigger: triggerSkillDetail
  } = useQueryGraphQL({
    query: QuerySkillsShow,
    variables: {
      id: Number(id)
    },
    shouldPause: true
  })

  useEffect(() => {
    if (id) {
      triggerSkillDetail()
    }
  }, [id])

  const deleteSkillCallback = useCallback(
    async (data: ISkill) => {
      if (isLoadingDelete) {
        return
      }

      triggerDelete({
        ids: [Number(data.id)],
        group: true
      }).then((result) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.skillsDetail(String(id)),
            setToast
          })
        }

        const { skillsDelete } = result.data
        if (skillsDelete.success) {
          setToast({
            open: true,
            type: 'success',
            title: `${t('notification:settings:skills:success_remove_skill', {
              name: data.name
            })}`
          })

          router.push(configuration.path.settings.skills)
        }

        return
      })
    },
    [isLoadingDelete, triggerDelete, id, setToast, t]
  )

  const isSearchEmpty = detectSearchEmpty({
    filters: filterControl.value as {
      [key: string]: unknown
    },
    filterKeys: ['dateRange']
  })

  const getTitleTooltipPipeline = (type: string, fromDate: string, toDate: string) => {
    const fromDateByTimeZone = new Date(
      new Date(fromDate).toLocaleString('en-US', {
        timeZone: user?.timezone
      })
    )
    const toDateByTimeZone = new Date(
      new Date(toDate).toLocaleString('en-US', {
        timeZone: user?.timezone
      })
    )

    if (type === 'day') {
      return defaultFormatDate(fromDateByTimeZone)
    }
    if (type === 'week') {
      return `${defaultFormatDate(fromDateByTimeZone)} - ${defaultFormatDate(toDateByTimeZone)}`
    }
    return monthYearFormatDate(fromDateByTimeZone, i18n.language)
  }

  return (
    <Container overrideClass="overflow-x-hidden">
      <SkeletonContainer
        useLoading={false}
        classNameFirstLoading="px-6 py-2"
        isFirstLoading={skillData === undefined && (isLoading || !isLoading)}>
        <div className="mx-auto flex max-w-[1024px] justify-between px-6 py-2">
          <div className="mr-6 flex-1">
            <div className="flex items-center">
              <TypographyText className="text-xl font-semibold text-gray-900">
                {skillData?.skillsShow.name}
              </TypographyText>
              {skillData?.skillsShow.parent?.name ? (
                <>
                  <span className="mx-2 inline-block h-0.5 min-h-[2px] w-0.5 min-w-[2px] rounded-xs bg-gray-400" />
                  <TypographyText className="text-sm text-gray-900">
                    {skillData?.skillsShow.parent?.name}
                  </TypographyText>
                </>
              ) : null}
            </div>

            {skillData?.skillsShow?.similar?.length ? (
              <div className="flex flex-wrap">
                <SuggestionInlineChips
                  size="sm"
                  source={skillData.skillsShow.similar.map((item) => ({
                    label: item,
                    maxLength: 30
                  }))}
                  type="default"
                />
              </div>
            ) : null}

            {skillData?.skillsShow?.description ? (
              <div className="mt-2">
                <LongContentDisplay
                  isHTML
                  limitLines={2}
                  content={skillData.skillsShow.description}
                  className="max-w-full flex-1 text-sm text-gray-900"
                  textButtonProps={{ size: 'md' }}
                />
              </div>
            ) : null}
          </div>

          <div className="flex justify-center pt-1.5">
            <div>
              <Tooltip position="bottom" content={t('button:moveToGroup')} classNameAsChild="mx-1">
                <Button
                  size="xs"
                  iconMenus="ArrowRight"
                  type="tertiary"
                  onClick={() => {
                    setOpenSKill({
                      open: true,
                      defaultValue: {
                        parentId: skillData?.skillsShow?.id,
                        id: skillData?.skillsShow?.id,
                        name: skillData?.skillsShow?.name,
                        group: {
                          value: skillData?.skillsShow?.parent?.id,
                          supportingObj: {
                            name: skillData?.skillsShow?.parent?.name
                          }
                        },
                        similar: skillData?.skillsShow?.similar,
                        description: skillData?.skillsShow?.description
                      },
                      moveOnly: true
                    })
                  }}
                  label={`${t('button:move')}`}
                />
              </Tooltip>
            </div>

            <div>
              <Tooltip position="bottom" content={t('button:edit')} classNameAsChild="mx-1">
                <Button
                  size="xs"
                  iconMenus="Edit3Icon"
                  type="tertiary"
                  onClick={() => {
                    setOpenSKill({
                      open: true,
                      defaultValue: {
                        parentId: skillData?.skillsShow?.id,
                        id: skillData?.skillsShow?.id,
                        name: skillData?.skillsShow?.name,
                        group: {
                          value: skillData?.skillsShow?.parent?.id,
                          supportingObj: {
                            name: skillData?.skillsShow?.parent?.name
                          }
                        },
                        similar: skillData?.skillsShow?.similar,
                        description: skillData?.skillsShow?.description
                      }
                    })
                  }}
                  label={`${t('button:edit')}`}
                />
              </Tooltip>
            </div>

            <div>
              <Tooltip position="bottom" content={t('button:delete')} classNameAsChild="mx-1">
                <Button
                  size="xs"
                  iconMenus="Trash2"
                  type="destructive"
                  isLoading={isLoadingDelete}
                  onClick={() => {
                    openAlert({
                      isPreventAutoFocusDialog: false,
                      className: 'w-[480px]',
                      title: `${t('settings:skills:removeSkillAlert:title')}`,
                      description: (
                        <Trans
                          i18nKey="settings:skills:removeSkillAlert:content"
                          values={{
                            name: String(skillData?.skillsShow.name)
                          }}>
                          <span className="font-medium text-gray-900" />
                          <br />
                        </Trans>
                      ),
                      actions: [
                        {
                          label: `${t('button:cancel')}`,
                          type: 'secondary',
                          size: 'sm'
                        },
                        {
                          isCallAPI: true,
                          label: `${t('button:delete')}`,
                          type: 'destructive',
                          size: 'sm',
                          onClick: async () => {
                            await deleteSkillCallback({
                              id: String(id),
                              name: String(skillData?.skillsShow.name)
                            })
                          }
                        }
                      ]
                    })
                  }}
                />
              </Tooltip>
            </div>
          </div>
        </div>
        <Divider />

        <div className="mx-auto max-w-[1024px] space-y-4 px-6 py-4">
          <div className="w-fit max-w-[324px]">
            <Filter
              value={filterControl.value}
              onChange={(filter, name) => {
                if (name === 'dateRange') {
                  const from = filter?.dateRange?.from
                  const to = filter?.dateRange?.to
                  if ((from && !to) || (!from && to)) {
                    return
                  }
                }

                filterControl.onChange && filterControl.onChange(filter)
              }}>
              <Filter.RangeDatePicker
                menuSide="start"
                size="sm"
                name="dateRange"
                placeholder={`${t('label:placeholder:formTo')}`}
                config={{ showOutsideDays: false }}
              />
            </Filter>
          </div>

          <div className="flex items-center justify-between space-x-4">
            <MetricChartWrapper
              id={Number(id)}
              dataTypeMapping={METRIC_SKILL_DATA_TYPE.skillsShowSummary}
              url={QuerySkillsShowSummary}
              filter={filterControl.value}>
              {({ isLoading, data }) => (
                <>
                  <MetricChart
                    className="flex-1"
                    title={t('settings:skills:report:statistics:profiles')}
                    helperConfig={{
                      icon: 'HelpCircle',
                      content: t('settings:skills:report:statistics:profilesHelpInfo')
                    }}
                    previousTooltip={`${t('tooltip:previousPeriod')}`}
                    dataSource={{
                      noChange: t('settings:skills:report:noChangeFromPreviousTime'),
                      isLoading,
                      data: data?.profiles
                    }}
                    onClickTotalNumber={() => {
                      setSkillDetailConfig({
                        open: true,
                        type: 'application',
                        query: QuerySkillsShowProfilesSummaryDetails,
                        title: `${t('settings:skills:report:statistics:profiles')}`
                      })
                    }}
                  />

                  <MetricChart
                    className="flex-1"
                    title={t('settings:skills:report:statistics:jobs')}
                    helperConfig={{
                      icon: 'HelpCircle',
                      content: t('settings:skills:report:statistics:jobsHelpInfo')
                    }}
                    previousTooltip={`${t('tooltip:previousPeriod')}`}
                    dataSource={{
                      noChange: t('settings:skills:report:noChangeFromPreviousTime'),
                      isLoading,
                      data: data?.jobs
                    }}
                    onClickTotalNumber={() => {
                      setSkillDetailConfig({
                        open: true,
                        type: 'job',
                        query: QuerySkillShowJobSummaryDetail,
                        title: `${t('settings:skills:report:statistics:jobs')}`
                      })
                    }}
                  />

                  <MetricChart
                    className="flex-1"
                    title={t('settings:skills:report:statistics:hiredJobs')}
                    helperConfig={{
                      icon: 'HelpCircle',
                      content: t('settings:skills:report:statistics:hiredJobsHelpInfo')
                    }}
                    previousTooltip={`${t('tooltip:previousPeriod')}`}
                    dataSource={{
                      noChange: t('settings:skills:report:noChangeFromPreviousTime'),
                      isLoading,
                      data: data?.hiredJobs
                    }}
                    onClickTotalNumber={() => {
                      setSkillDetailConfig({
                        open: true,
                        type: 'hires',
                        query: QuerySkillsShowHiredJobsSummaryDetails,
                        title: `${t('settings:skills:report:statistics:hiredJobs')}`
                      })
                    }}
                  />
                </>
              )}
            </MetricChartWrapper>
          </div>

          <div className="min-h-[456px]">
            <LineOrBarChartWrapper
              id={Number(id)}
              groupMode={groupMode === GROUPED_SUPPLY_DEMAND_TRENDS ? 'line' : 'bar'}
              dataTypeMapping={METRIC_SKILL_DATA_TYPE.skillsShowOvertimeReport}
              url={QuerySkillsShowOvertimeReport}
              filter={filterControl.value}>
              {({ isLoading, data }) => (
                <div className="flex min-h-[281px] flex-col rounded-md border border-solid border-gray-100">
                  <div className="flex items-center justify-between space-x-1.5 px-4 pt-4">
                    <p className="text-xl font-medium text-gray-900">
                      {t('settings:skills:report:overtimeReport:title')}
                    </p>
                    <DropdownMenu
                      side="bottom"
                      align="end"
                      menuClassName="w-auto"
                      trigger={
                        <Tooltip
                          content={
                            groupMode === GROUPED_CUMULATIVE_GROWTH
                              ? t('settings:skills:report:overtimeReport:cumulativeGrowthInfo')
                              : t('settings:skills:report:overtimeReport:supplyDemandTrendsInfo')
                          }>
                          <TextButton
                            type="tertiary"
                            size="md"
                            underline={false}
                            iconMenus="ChevronDown"
                            icon="trailing"
                            label={
                              groupMode === GROUPED_CUMULATIVE_GROWTH
                                ? t('settings:skills:report:overtimeReport:cumulativeGrowth')
                                : t('settings:skills:report:overtimeReport:supplyDemandTrends')
                            }
                          />
                        </Tooltip>
                      }
                      showCheckedIcon
                      menu={[
                        {
                          isChecked: groupMode === GROUPED_SUPPLY_DEMAND_TRENDS,
                          label: `${t('settings:skills:report:overtimeReport:supplyDemandTrends')}`,
                          onClick: () => setGroupMode(GROUPED_SUPPLY_DEMAND_TRENDS)
                        },
                        {
                          isChecked: groupMode === GROUPED_CUMULATIVE_GROWTH,
                          label: `${t('settings:skills:report:overtimeReport:cumulativeGrowth')}`,
                          onClick: () => setGroupMode(GROUPED_CUMULATIVE_GROWTH)
                        }
                      ]}
                    />
                  </div>

                  {groupMode === GROUPED_CUMULATIVE_GROWTH ? (
                    <BarChart
                      dataSource={{
                        isLoading,
                        emptyData: t('settings:skills:report:empty:noDataCollect'),
                        emptySearch: t('settings:skills:report:empty:noResultsFound'),
                        data: data ? (data as unknown as Array<IBarItemChart>) : isSearchEmpty ? [] : undefined,
                        onClick: (node) => {}
                      }}
                      configBarChartProps={{
                        groupMode: 'grouped',
                        data: data || [],
                        keys: Object.keys(filterPipelineLocal)
                          .filter((key) => {
                            return filterPipelineLocal[key as keyof typeof filterPipelineLocal]?.enable
                          })
                          .map((key) => key),
                        tooltip: ({ data }) => {
                          return (
                            <div className="animate-appear invisible min-w-[148px] rounded-xs bg-white px-3 py-3 shadow-lg">
                              <span className="text-xs font-medium text-gray-700">
                                {getTitleTooltipPipeline(
                                  data.groupType as string,
                                  data.fromDate as string,
                                  data.toDate as string
                                )}
                              </span>
                              <div>
                                {Object.keys(filterPipelineLocal)
                                  .filter((key) => filterPipelineLocal[key]?.enable)
                                  .map((key) => {
                                    return (
                                      <div key={key} className="mt-2 flex flex-row items-center">
                                        <div
                                          className="h-3 w-3 rounded-xs"
                                          style={{
                                            background: filterPipelineLocal[key]?.color
                                          }}
                                        />
                                        <span className="ml-2 text-xs font-normal text-gray-700">
                                          {filterPipelineLocal[key]?.label}:{' '}
                                          <span className="font-medium text-gray-900">
                                            {Number(data[key as keyof typeof data] || 0)}
                                          </span>
                                        </span>
                                      </div>
                                    )
                                  })}
                              </div>
                            </div>
                          )
                        },
                        onMouseEnter: (datum, event) => {
                          event.currentTarget.style.cursor = 'pointer'
                        },
                        indexBy: 'id',
                        margin: { top: 32, right: 32, bottom: 50, left: 50 },
                        padding: 0.3,
                        colors: ({ id, data }) => String(data[`${id}Color` as keyof typeof data]),
                        axisBottom: {
                          tickSize: 5,
                          tickPadding: 10,
                          tickRotation: -15,
                          renderTick: (props) => {
                            const { tickIndex, x, y, textX, textY, textAnchor, value } = props
                            const record = (data || [])[tickIndex]
                            const label = record?.label
                            const groupType = record?.groupType
                            const rotate = record?.label?.includes('-') ? 'rotate(-15)' : 'rotate(-35)'

                            return (
                              <g transform={`translate(${x},${y})`} style={{ opacity: 1 }}>
                                <line
                                  x1="0"
                                  x2="0"
                                  y1="0"
                                  y2="5"
                                  style={{
                                    stroke: 'rgb(119, 119, 119)',
                                    strokeWidth: 1
                                  }}></line>
                                <text
                                  textAnchor={textAnchor as TextAnchor}
                                  transform={`translate(${textX + (groupType === 'day' ? 5 : 10)},${textY + 2}) ${rotate}`}
                                  style={{
                                    fontFamily: 'sans-serif',
                                    fontSize: 11,
                                    fill: 'rgb(51, 51, 51)'
                                  }}>
                                  {label}
                                </text>
                                {record?.extraLabel && (
                                  <text
                                    textAnchor={textAnchor as TextAnchor}
                                    transform={`translate(${textX + 12},${textY + 20}) ${rotate}`}
                                    style={{
                                      fontWeight: 'bold',
                                      fontFamily: 'sans-serif',
                                      fontSize: 11,
                                      fill: 'rgb(51, 51, 51)'
                                    }}>
                                    {record?.extraLabel}
                                  </text>
                                )}
                              </g>
                            )
                          }
                        },
                        layers: ['grid', 'axes', 'bars'],
                        axisLeft: {
                          tickSize: 5,
                          tickPadding: 5,
                          tickRotation: 0,
                          legend: '',
                          legendPosition: 'middle',
                          legendOffset: 0
                        },
                        animate: true,
                        enableLabel: false,
                        isInteractive: true
                      }}
                      footerBarChart={
                        <div className="flex items-center space-x-8 pt-6 pr-4 pb-4 pl-[50px]">
                          {Object.keys(filterPipelineLocal).map((key) => (
                            <div key={key} className="flex items-center justify-end space-x-2">
                              <span
                                className="h-3 w-3 rounded-lg"
                                style={{
                                  backgroundColor: filterPipelineLocal[key]?.color
                                }}
                              />
                              <span className="text-sm text-gray-700">
                                {filterPipelineLocal[key]?.label ===
                                t('settings:skills:report:overtimeReport:profilesTitle') ? (
                                  <Trans
                                    i18nKey="settings:skills:report:overtimeReport:profilesGrowth"
                                    values={{
                                      name: String(skillData?.skillsShow.name)
                                    }}>
                                    <span className="font-medium text-gray-900" />
                                  </Trans>
                                ) : (
                                  <Trans
                                    i18nKey="settings:skills:report:overtimeReport:jobsGrowth"
                                    values={{
                                      name: String(skillData?.skillsShow.name)
                                    }}>
                                    <span className="font-medium text-gray-900" />
                                  </Trans>
                                )}
                              </span>
                            </div>
                          ))}
                        </div>
                      }
                    />
                  ) : null}

                  {groupMode === GROUPED_SUPPLY_DEMAND_TRENDS ? (
                    <LineChart
                      component={ResponsiveLine}
                      className="border-none"
                      customClass={{
                        classNameResponsiveLine: 'px-2.5',
                        explication: 'items-start',
                        dot: 'mt-1'
                      }}
                      dataSource={{
                        isLoading,
                        emptyData: t('settings:skills:report:empty:noDataCollect'),
                        emptySearch: t('settings:skills:report:empty:noResultsFound'),
                        data: data
                          ? data?.map((item) => ({
                              ...item,
                              label:
                                item.id === t('settings:skills:report:overtimeReport:profilesTitle') ? (
                                  <Trans
                                    i18nKey="settings:skills:report:overtimeReport:profiles"
                                    values={{
                                      name: String(skillData?.skillsShow.name)
                                    }}>
                                    <span className="font-medium text-gray-900" />
                                  </Trans>
                                ) : (
                                  <Trans
                                    i18nKey="settings:skills:report:overtimeReport:jobs"
                                    values={{
                                      name: String(skillData?.skillsShow.name)
                                    }}>
                                    <span className="font-medium text-gray-900" />
                                  </Trans>
                                )
                            }))
                          : isSearchEmpty
                            ? []
                            : undefined
                      }}
                      configLineChartProps={{
                        data: data || [],
                        colors: (data) => data.color,
                        margin: { top: 32, right: 16, bottom: 60, left: 38 },
                        axisTop: null,
                        axisRight: null,
                        axisBottom: {
                          tickSize: 5,
                          tickPadding: 10,
                          tickRotation: -15,
                          renderTick: (props) => {
                            const { tickIndex, x, y, textX, textY, textAnchor, value } = props
                            const record = (data || [])?.[0]?.data?.[tickIndex] as {
                              label: string
                              groupType?: string
                            }
                            const label = record?.label
                            const groupType = record?.groupType
                            const rotate = record?.label?.includes('-') ? 'rotate(-15)' : 'rotate(-35)'

                            return (
                              <g transform={`translate(${x},${y})`} style={{ opacity: 1 }}>
                                <line
                                  x1="0"
                                  x2="0"
                                  y1="0"
                                  y2="5"
                                  style={{
                                    stroke: 'rgb(119, 119, 119)',
                                    strokeWidth: 1
                                  }}></line>
                                <text
                                  textAnchor={textAnchor as TextAnchor}
                                  transform={`translate(${textX + (groupType === 'day' ? 5 : 10)},${textY + 2}) ${rotate}`}
                                  style={{
                                    fontFamily: 'sans-serif',
                                    fontSize: 11,
                                    fill: 'rgb(51, 51, 51)'
                                  }}>
                                  {label}
                                </text>
                              </g>
                            )
                          }
                        },
                        pointSize: 8,
                        pointColor: { theme: 'background' },
                        pointBorderWidth: 1.5,
                        pointBorderColor: { from: 'serieColor' },
                        // @ts-ignore - doesn't need to fix
                        useMesh: true,
                        enableCrosshair: false,
                        enableSlices: 'x',
                        legends: []
                      }}
                    />
                  ) : null}
                </div>
              )}
            </LineOrBarChartWrapper>
          </div>
        </div>

        <SkillModal
          open={openSkill.open}
          setOpen={setOpenSKill}
          data={openSkill}
          isMove={openSkill.moveOnly}
          callback={() => triggerSkillDetail()}
        />
      </SkeletonContainer>

      {skillDetailConfig?.open ? (
        <SkillDetailModal
          open={skillDetailConfig.open}
          setOpen={(value) => setSkillDetailConfig(value ? { ...skillDetailConfig, open: value } : { open: false })}
          title={skillDetailConfig?.title}
          type={skillDetailConfig?.type}
          mappingData={skillDetailConfig?.mappingData}
          queryReportDetail={skillDetailConfig?.query}
          filterControl={{
            value: {
              id: Number(id),
              ...(filterControl?.value || {})
            },
            onChange: filterControl?.onChange
          }}
        />
      ) : null}
    </Container>
  )
}

export default SkillReportDetail
