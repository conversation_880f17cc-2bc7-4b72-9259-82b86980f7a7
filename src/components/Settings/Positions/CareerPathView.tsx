import { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import configuration from '~/configuration'
import ComboboxSelect from '~/core/ui/ComboboxSelect'
import { DebouncedInput } from '~/core/ui/DebouncedInput'
import type { ISelectOption } from '~/core/ui/Select'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

import QueryCareerPathList from '~/lib/features/settings/positions/graphql/query-career-path-list'
import QueryDeleteCareerPathMutation from '~/lib/features/settings/positions/graphql/submit-delete-career-path'
import usePositionOptions from '~/lib/features/settings/positions/hooks/use-position-options'
import { mappingPositionSearchGraphQL } from '~/lib/features/settings/positions/mapping/search-position.mapping'
import type { IC<PERSON>erListing, ICareerPath, QueryPositionProps } from '~/lib/features/settings/positions/type'
import { POSITION_STATUS_ENUM } from '~/lib/features/settings/positions/utilities/enum'
import usePaginationGraphPage from '~/lib/hooks/use-pagination-graph-page'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useToastStore from '~/lib/store/toast'

import CareerPathAddModal from './CareerPathAdd'
import CareerPathTable from './CareerPathTable'

export interface CareerPathModalProps {
  open: boolean
  defaultValue?: ICareerListing
}

const CareerPathView: React.FC<{
  suspend?: boolean
  setCount?: (param: number) => void
  isOpenNewCareerPath?: boolean
  setCloseAddCareerPath: (open: boolean) => void
}> = ({ suspend, setCount, setCloseAddCareerPath, isOpenNewCareerPath }) => {
  const { t } = useTranslation()
  const { setToast } = useToastStore()
  const { optionsStatusPosition } = usePositionOptions()
  const [isDefaultFilters, setIsDefaultFilters] = useState<boolean>(true)
  const [queryKey, setQueryKey] = useState<QueryPositionProps>({
    statuses: optionsStatusPosition.filter((statusPosition) =>
      [POSITION_STATUS_ENUM.active, POSITION_STATUS_ENUM.draft].includes(statusPosition.value)
    )
  })
  const { trigger: triggerDelete, isLoading: isLoadingDelete } = useSubmitCommon(QueryDeleteCareerPathMutation)

  const { data, isFetching, globalFilter, setGlobalFilter, refetch, fetchPagination, forceChangeCurrentPage } =
    usePaginationGraphPage({
      queryDocumentNode: QueryCareerPathList,
      queryKey: 'my-setting-position-career-path',
      filter: mappingPositionSearchGraphQL(queryKey),
      enabled: !suspend
    })

  useEffect(() => {
    setCount && setCount(data?.meta?.totalRowCount || 0)
  }, [data?.data])

  const deleteCareerPathCallback = useCallback(
    async (career: ICareerPath) => {
      if (isLoadingDelete) {
        return
      }

      triggerDelete({
        id: Number(career.id)
      }).then(async (result) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.positions,
            setToast
          })
        }

        const { tenantCareerPathDelete } = result.data
        if (tenantCareerPathDelete.success) {
          setToast({
            open: true,
            type: 'success',
            title: t('notification:careerPathDeleted')
          })
          refetch()
        }

        return
      })
    },
    [isLoadingDelete, triggerDelete, setToast, t, queryKey]
  )

  const onEditCareerPath = (career: ICareerListing) => {
    setOpenAddCareerPath({
      open: true,
      defaultValue: career
    })
  }

  const handleChangeInput = (value: string | number) => {
    setTimeout(() => {
      setQueryKey((queryPrev) => ({
        ...queryPrev,
        search: value,
        page: 1
      }))
      setIsDefaultFilters(false)
    }, 0)
  }

  const handleClearInput = () => {
    setTimeout(() => {
      setQueryKey((queryPrev) => ({
        ...queryPrev,
        search: '',
        page: 1
      }))
    }, 0)
  }

  const handleChangeStatusOption = (value?: ISelectOption | ISelectOption[]) => {
    const newValue = value || []
    setQueryKey({
      ...queryKey,
      statuses: Array.isArray(newValue) ? newValue : [newValue],
      page: 1
    })
    setIsDefaultFilters(false)
  }

  const [isOpenAddCareerPath, setOpenAddCareerPath] = useState<CareerPathModalProps>({
    open: false,
    defaultValue: undefined
  })

  useEffect(() => {
    if (!isOpenNewCareerPath) return
    setOpenAddCareerPath({
      open: true,
      defaultValue: {} as ICareerListing
    })
  }, [isOpenNewCareerPath])

  useEffect(() => {
    setCloseAddCareerPath(false)
  }, [isOpenAddCareerPath])

  return (
    <>
      <div className="flex items-center justify-between pb-2">
        <div className="flex items-center space-x-2">
          <div className="w-[300px]">
            <DebouncedInput
              debounceDelay={500}
              placeholder={`${t('settings:positions:searchInput')}`}
              value={globalFilter ?? ''}
              onChange={handleChangeInput}
              onClear={handleClearInput}
              forceOnChangeCallBack={queryKey}
            />
          </div>

          <ComboboxSelect
            size="md"
            isMulti={true}
            isSearchable={false}
            isClearable
            menuOptionAlign="end"
            menuOptionSide="bottom"
            avatarToolTipPosition="bottom"
            toolTipPositionAvatarCount="bottom"
            tooltipAlignAvatarCount="end"
            dropdownMenuClassName="!w-[128px]"
            containerMenuClassName="w-[128px]"
            buttonClassName="max-w-[164px]"
            placeholder={`${t('settings:positions:allStatus')}`}
            loadingMessage={`${t('label:loading')}`}
            noOptionsMessage={`${t('label:noOptions')}`}
            value={queryKey?.statuses}
            options={optionsStatusPosition}
            onChange={handleChangeStatusOption}
            countName={`${t('label:placeholder:status')}`}
          />
        </div>
      </div>
      <CareerPathTable
        queryKey={queryKey}
        isDefaultFilters={isDefaultFilters}
        globalFilter={globalFilter}
        setGlobalFilter={setGlobalFilter}
        isFetching={isFetching}
        data={data}
        fetcher={{
          fetchPagination,
          forceChangeCurrentPage
        }}
        isLoadingDelete={isLoadingDelete}
        onEditCareerPath={onEditCareerPath}
        setOpenAddNewCareerPath={setOpenAddCareerPath}
        deleteCareerPathCallback={deleteCareerPathCallback}></CareerPathTable>

      <CareerPathAddModal
        open={isOpenAddCareerPath.open}
        setOpen={setOpenAddCareerPath}
        data={isOpenAddCareerPath}
        callback={() => {
          refetch()
        }}
      />
    </>
  )
}

export default withQueryClientProvider(CareerPathView)
