import { Trans, useTranslation } from 'react-i18next'

import type { ISelectOption } from '~/core/@types/global'
import { AGENCY_TENANT } from '~/core/constants/enum'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import { formatDatePickerToDate } from '~/core/ui/SingleDateWithYearOnlyPicker'
import { SuggestionChips } from '~/core/ui/SuggestionChips'
import { TypographyText } from '~/core/ui/Text'
import { Toggle } from '~/core/ui/Toggle'
import { Tooltip } from '~/core/ui/Tooltip'
import { defaultFormatDate } from '~/core/utilities/format-date'

import useCandidateProfile from '~/lib/features/candidates/hooks/use-query-candidate'
import type {
  CertificatesType,
  ICandidateProfile,
  PreferredWorkStatesType,
  TalentPoolType
} from '~/lib/features/candidates/types'
import { getYearOld, permittedFieldsManagement, useConvertSalary } from '~/lib/features/candidates/utilities'
import { DEFAULT_CURRENCY, totalYoeOptions } from '~/lib/features/candidates/utilities/enum'
import useCustomFieldSettingByUser from '~/lib/features/settings/profile-fields/hooks/use-custom-field-setting-by-user'
import { formatInitialValueCustomField } from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'
import type { CustomFieldFormType } from '~/lib/features/settings/profile-fields/types/custom-field'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import useBoundStore from '~/lib/store'

import {
  checkFieldFormatDate,
  getFieldFormatDate
} from '~/components/Candidates/Profile/components/Overview/Certificates/CertificatesView'
import EducationView from '~/components/Candidates/Profile/components/Overview/Education/EducationView'
import WorkExperiencesView from '~/components/Candidates/Profile/components/Overview/WorkExperiences/WorkExperiencesView'
import type { CustomFieldComponentType } from '~/components/CustomField'
import CustomField from '~/components/CustomField'
import HTMLDisplay from '~/components/HTMLDisplay'

const TypeOfSalaryKey = {
  monthly: 'monthly',
  annually: 'annual'
}

function SettingsProfileInformationView({ profilesShow }: { profilesShow?: ICandidateProfile }) {
  const permittedFields = profilesShow?.permittedFields
  const { t, i18n } = useTranslation()
  const { user } = useBoundStore()
  const { numberWithCommas } = useConvertSalary()
  const { data: customFieldViewData } = useCustomFieldSettingByUser({
    objectKind: 'profile',
    employeeId: Number(user?.id)
  })
  const { profileLevel } = useCandidateProfile({})

  const mappingsField = (customFieldViewData || [])?.filter((item) => item.visibleToEmployeeProfile && item.visibility)
  const skillString = JSON.parse(JSON.stringify(permittedFields?.skills?.value || []))

  const suggestionChipSkill = skillString.map((item: string) => {
    return {
      label: item,
      maxLength: 30
    }
  })
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })

  const hasDepartments =
    permittedFields?.departments?.value && permittedFields.departments?.value?.departments.length > 0

  const hasPreferredWorkStates =
    permittedFields?.preferredWorkStates?.value && permittedFields.preferredWorkStates?.value?.length > 0

  const suggestionChipPreferredWorkStates = (profilesShow?.permittedFields?.preferredWorkStates?.value || []).map(
    (item: PreferredWorkStatesType) => ({
      label: item.full_name,
      maxLength: 30
    })
  )

  const departments: string[] = profilesShow?.permittedFields?.departments?.value?.departments || []

  const allDepartments = profilesShow?.permittedFields?.departments?.value?.all_departments

  const suggestionChipDepartments = departments.map((item: string) => ({
    label: item,
    maxLength: 30
  }))

  const suggestionChipTalentPools =
    (permittedFields?.talentPools?.value || [])?.map((item: TalentPoolType, index: number) => {
      return {
        id: item.id,
        label: item.name,
        classNameChip: 'prevent-clickable',
        maxLength: 25
      }
    }) || []

  const findProfileLevel = profileLevel.find(
    (item: ISelectOption) => String(item.value) === String(permittedFields?.profileLevel?.value)
  )
  const formatProfileLevel = findProfileLevel
    ? {
        value: permittedFields?.profileLevel?.value,
        supportingObj: {
          name: findProfileLevel?.supportingObj?.name || ''
        }
      }
    : undefined

  const checkShowSectionName = () => {
    let show = false

    if (permittedFields?.links) show = true
    if (permittedFields?.publicId) show = true
    if (permittedFields?.headline) show = true
    if (permittedFields?.totalYearsOfExp) show = true
    if (permittedFields?.skills) show = true
    if (permittedFields?.languages) show = true
    if (permittedFields?.currentSalary) show = true
    if (permittedFields?.expectedSalary) show = true
    if (permittedFields?.birthday) show = true
    if (permittedFields?.profileLevel) show = true
    if (permittedFields?.nationality) show = true
    if (permittedFields?.willingToRelocate) show = true
    if (permittedFields?.noticeToPeriodDays) show = true
    if (permittedFields?.talentPools) show = true

    const customField = mappingsField.find((customField) =>
      Object.values((formatInitialValueCustomField(profilesShow?.customFields) || {}) as CustomFieldFormType).find(
        (item) => String(item.id) === String(customField.id)
      )
    )

    if (customField) show = true

    return show
  }

  return (
    <div>
      <div className="flex items-center justify-between">
        {checkShowSectionName() ? (
          <TypographyText className="text-lg font-medium text-gray-900">
            {t('candidates:tabs:candidateOverview:profileInformation:heading')}
          </TypographyText>
        ) : null}
      </div>
      <div className="desktop:grid-cols-[200px_1fr] grid grid-cols-[156px_1fr]">
        <If condition={permittedFieldsManagement(permittedFields?.publicId?.roles) || permittedFields?.publicId}>
          <div className="flex items-center">
            <IconWrapper size={16} name="ClipboardList" className="desktop:block hidden text-gray-600" />
            <TypographyText className="ml-2 text-sm text-gray-700">
              {t('candidates:tabs:candidateOverview:profileInformation:publicId')}
            </TypographyText>
          </div>

          <div className="w-full">
            <div className="px-2 py-1.5">
              {permittedFields?.publicId?.value ? (
                <Tooltip content={permittedFields?.publicId?.value} align="start">
                  <div className="line-clamp-1 text-sm text-gray-900">{permittedFields?.publicId?.value}</div>
                </Tooltip>
              ) : (
                <div className="line-clamp-1 text-sm text-gray-600">
                  {t('candidates:tabs:candidateOverview:notAvailable')}
                </div>
              )}
            </div>
          </div>
        </If>

        <If condition={permittedFieldsManagement(permittedFields?.headline?.roles) || permittedFields?.headline}>
          <div className="flex items-center">
            <IconWrapper size={16} name="Newspaper" className="desktop:block hidden text-gray-600" />
            <TypographyText className="ml-2 text-sm text-gray-700">
              {t('candidates:tabs:candidateOverview:profileInformation:position')}
            </TypographyText>
          </div>

          <div className="w-full">
            <div className="px-2 py-1.5">
              {permittedFields?.headline?.value ? (
                <Tooltip content={permittedFields?.headline?.value} align="start">
                  <div className="line-clamp-1 text-sm text-gray-900">{permittedFields?.headline?.value}</div>
                </Tooltip>
              ) : (
                <div className="line-clamp-1 text-sm text-gray-600">
                  {t('candidates:tabs:candidateOverview:notAvailable')}
                </div>
              )}
            </div>
          </div>
        </If>

        <If condition={permittedFieldsManagement(permittedFields?.departments?.roles) || permittedFields?.departments}>
          <div>
            <div className="flex w-full items-center py-1.5">
              <IconWrapper size={16} name="Network" className="desktop:block hidden text-gray-600" />
              <TypographyText className="ml-2 text-sm text-gray-700">
                {t('candidates:tabs:candidateOverview:profileInformation:department')}
              </TypographyText>
            </div>
          </div>

          <div className="w-full">
            <div className="flex items-center px-2 py-1.5 text-sm text-gray-900 dark:text-gray-300">
              {allDepartments ? (
                <TypographyText className="truncate text-sm text-gray-900 dark:text-gray-300">
                  {isCompanyKind ? `${t('settings:team:allTeams')}` : `${t('settings:departments:allDepartments')}`}
                </TypographyText>
              ) : hasDepartments ? (
                <SuggestionChips size="sm" source={suggestionChipDepartments} type="default" />
              ) : (
                <div className="text-gray-600 dark:text-gray-400">
                  {t('candidates:tabs:candidateOverview:notAvailable')}
                </div>
              )}
            </div>
          </div>
        </If>

        <If condition={permittedFieldsManagement(permittedFields?.talentPools?.roles) || permittedFields?.talentPools}>
          <div className="flex items-center">
            <IconWrapper size={16} name="FolderSearch" className="desktop:block hidden text-gray-600" />
            <TypographyText className="ml-2 text-sm text-gray-700">
              {t('candidates:tabs:candidateOverview:profileInformation:talentPoolsNew')}
            </TypographyText>
          </div>

          <div className="w-full">
            <div className="flex items-center px-2 py-1.5 text-sm">
              {permittedFields?.talentPools?.value && permittedFields?.talentPools?.value?.length > 0 ? (
                <div className="text-gray-900">
                  <SuggestionChips size="sm" source={suggestionChipTalentPools} type="default" />
                </div>
              ) : (
                <div className="text-gray-600">{t('candidates:tabs:candidateOverview:notAvailable')}</div>
              )}
            </div>
          </div>
        </If>

        <If
          condition={permittedFieldsManagement(permittedFields?.profileLevel?.roles) || permittedFields?.profileLevel}>
          <div className="flex items-center">
            <IconWrapper size={16} name="Boxes" className="desktop:block hidden text-gray-600" />
            <TypographyText className="ml-2 text-sm text-gray-700">
              {t('candidates:tabs:candidateOverview:profileInformation:experienceLevel')}
            </TypographyText>
          </div>

          <div className="w-full">
            <div className="flex items-center px-2 py-1.5 text-sm">
              {permittedFields?.profileLevel?.value ? (
                <div className="text-gray-900">{formatProfileLevel?.supportingObj?.name}</div>
              ) : (
                <div className="text-gray-600">{t('candidates:tabs:candidateOverview:notAvailable')}</div>
              )}
            </div>
          </div>
        </If>

        <If
          condition={
            permittedFieldsManagement(permittedFields?.totalYearsOfExp?.roles) || permittedFields?.totalYearsOfExp
          }>
          <div className="flex items-center">
            <IconWrapper size={16} name="Boxes" className="desktop:block hidden text-gray-600" />
            <TypographyText className="ml-2 text-sm text-gray-700">
              {t('candidates:tabs:candidateOverview:profileInformation:totalYoe')}
            </TypographyText>
          </div>

          <div className="w-full">
            <div className="flex items-center px-2 py-1.5 text-sm">
              {['number', 'string'].includes(typeof permittedFields?.totalYearsOfExp?.value) ? (
                <div className="text-gray-900">
                  {
                    totalYoeOptions
                      .map((item) => ({
                        ...item,
                        supportingObj: {
                          name: `${t(`candidates:yoeOptions:${item.value}`)}`
                        }
                      }))
                      .find((item) => item.value === permittedFields?.totalYearsOfExp?.value?.toString())?.supportingObj
                      ?.name
                  }
                </div>
              ) : (
                <div className="text-gray-600">{t('candidates:tabs:candidateOverview:notAvailable')}</div>
              )}
            </div>
          </div>
        </If>

        <If condition={permittedFieldsManagement(permittedFields?.skills?.roles) || permittedFields?.skills}>
          <div>
            <div className="flex w-full items-center py-1.5">
              <IconWrapper size={16} name="ListChecks" className="desktop:block hidden text-gray-600" />
              <TypographyText className="ml-2 text-sm text-gray-700">
                {t('candidates:tabs:candidateOverview:profileInformation:skills')}
              </TypographyText>
            </div>
          </div>

          <div className="w-full">
            <div className="flex items-center px-2 py-1.5 text-sm">
              {permittedFields?.skills?.value && permittedFields?.skills?.value?.length > 0 ? (
                <div className="text-gray-900">
                  <SuggestionChips size="sm" source={suggestionChipSkill} type="default" />
                </div>
              ) : (
                <div className="text-gray-600">{t('candidates:tabs:candidateOverview:notAvailable')}</div>
              )}
            </div>
          </div>
        </If>

        <If condition={permittedFieldsManagement(permittedFields?.languages?.roles) || permittedFields?.languages}>
          <div>
            <div className="flex w-full items-center py-1.5">
              <IconWrapper size={16} name="Languages" className="desktop:block hidden text-gray-600" />
              <TypographyText className="ml-2 text-sm text-gray-700">
                {t('candidates:tabs:candidateOverview:profileInformation:languages')}
              </TypographyText>
            </div>
          </div>

          <div className="px-2 py-1.5">
            {permittedFields?.languages?.value && permittedFields?.languages?.value?.length > 0 ? (
              permittedFields?.languages?.value?.map((item, index) => {
                return (
                  <div className="group mt-1.5 first:mt-0" key={index}>
                    <div className="flex items-center">
                      <div className="mr-1 text-sm text-gray-900">{item.languageDescription}</div>
                      {item.proficiencyDescription && (
                        <div className="text-sm text-gray-600">({item.proficiencyDescription})</div>
                      )}
                    </div>
                  </div>
                )
              })
            ) : (
              <div className="text-sm text-gray-600">{t('candidates:tabs:candidateOverview:notAvailable')}</div>
            )}
          </div>
        </If>

        <If
          condition={
            permittedFieldsManagement(permittedFields?.currentSalary?.roles) || permittedFields?.currentSalary
          }>
          <div className="flex w-full items-center">
            <IconWrapper size={16} name="DollarSign" className="desktop:block hidden text-gray-600" />
            <TypographyText className="ml-2 text-sm text-gray-700">
              {t('candidates:tabs:candidateOverview:profileInformation:currentSalary')}
            </TypographyText>
          </div>

          <div className="w-full">
            <div className="flex items-center px-2 py-1.5 text-sm">
              {Number(permittedFields?.currentSalary?.value) > 0 ? (
                <div className="text-gray-900">
                  {numberWithCommas(Number(permittedFields?.currentSalary?.value))}{' '}
                  {permittedFields?.currentSalaryCurrency?.value || DEFAULT_CURRENCY}{' '}
                  {permittedFields?.typeOfCurrentSalary?.value === TypeOfSalaryKey.monthly
                    ? `(${t('candidates:tabs:candidateOverview:profileInformation:typeOfCurrentSalaryMonthly')})`
                    : `(${t('candidates:tabs:candidateOverview:profileInformation:typeOfCurrentSalaryAnnual')})`}
                </div>
              ) : (
                <div className="text-gray-600">{t('candidates:tabs:candidateOverview:notAvailable')}</div>
              )}
            </div>
          </div>
        </If>

        <If
          condition={
            permittedFieldsManagement(permittedFields?.expectedSalary?.roles) || permittedFields?.expectedSalary
          }>
          <div className="flex w-full items-center">
            <IconWrapper size={16} name="DollarSign" className="desktop:block hidden text-gray-600" />
            <TypographyText className="ml-2 text-sm text-gray-700">
              {t('candidates:tabs:candidateOverview:profileInformation:expectedSalary')}
            </TypographyText>
          </div>

          <div className="w-full">
            <div className="flex items-center px-2 py-1.5 text-sm">
              {Number(permittedFields?.expectedSalary?.value) > 0 ? (
                <div className="text-gray-900">
                  {numberWithCommas(Number(permittedFields?.expectedSalary?.value))}{' '}
                  {permittedFields?.expectedSalaryCurrency?.value || DEFAULT_CURRENCY}{' '}
                  {permittedFields?.typeOfExpectedSalary?.value === TypeOfSalaryKey.monthly
                    ? `(${t('candidates:tabs:candidateOverview:profileInformation:typeOfCurrentSalaryMonthly')})`
                    : `(${t('candidates:tabs:candidateOverview:profileInformation:typeOfCurrentSalaryAnnual')})`}
                </div>
              ) : (
                <div className="text-gray-600">{t('candidates:tabs:candidateOverview:notAvailable')}</div>
              )}
            </div>
          </div>
        </If>

        <If condition={permittedFieldsManagement(permittedFields?.birthday?.roles) || permittedFields?.birthday}>
          <div className="flex items-center">
            <IconWrapper size={16} name="Cake" className="desktop:block hidden text-gray-600" />
            <TypographyText className="ml-2 text-sm text-gray-700">
              {t('candidates:tabs:candidateOverview:profileInformation:birthday')}
            </TypographyText>
          </div>
          <div className="w-full">
            <div className="flex items-center px-2 py-1.5 text-sm">
              {permittedFields?.birthday?.value?.birth_year ? (
                <div className="text-gray-900">
                  {!!permittedFields?.birthday?.value?.birth_year &&
                  !permittedFields?.birthday?.value.birth_month &&
                  !permittedFields?.birthday?.value.birth_date ? (
                    <Trans
                      i18nKey="candidates:tabs:candidateOverview:profileInformation:formatSupportBirthday"
                      values={{
                        date: permittedFields?.birthday?.value.birth_year,
                        years_old: getYearOld(
                          formatDatePickerToDate({
                            year: permittedFields?.birthday?.value.birth_year,
                            month: permittedFields?.birthday?.value.birth_month,
                            date: permittedFields?.birthday?.value.birth_date
                          })
                        )
                      }}>
                      <span className="text-gray-600" />
                    </Trans>
                  ) : (
                    <Trans
                      i18nKey="candidates:tabs:candidateOverview:profileInformation:formatSupportBirthday"
                      values={{
                        date: defaultFormatDate(
                          formatDatePickerToDate({
                            year: permittedFields?.birthday?.value?.birth_year,
                            month: permittedFields?.birthday?.value.birth_month,
                            date: permittedFields?.birthday?.value?.birth_date
                          })
                        ),
                        years_old: getYearOld(
                          formatDatePickerToDate({
                            year: permittedFields?.birthday?.value?.birth_year,
                            month: permittedFields?.birthday?.value.birth_month,
                            date: permittedFields?.birthday?.value?.birth_date
                          })
                        )
                      }}>
                      <span className="text-gray-600" />
                    </Trans>
                  )}
                </div>
              ) : (
                <div className="text-gray-600">{t('candidates:tabs:candidateOverview:notAvailable')}</div>
              )}
            </div>
          </div>
        </If>

        <If condition={permittedFieldsManagement(permittedFields?.nationality?.roles) || permittedFields?.nationality}>
          <div className="flex w-full items-center">
            <IconWrapper size={16} name="Flag" className="desktop:block hidden text-gray-600" />
            <TypographyText className="ml-2 text-sm text-gray-700">
              {t('candidates:tabs:candidateOverview:profileInformation:nationality')}
            </TypographyText>
          </div>

          <div className="w-full">
            <div className="flex items-center px-2 py-1.5 text-sm">
              {permittedFields?.nationality?.value ? (
                <div className="text-gray-900">{permittedFields?.nationality?.value}</div>
              ) : (
                <div className="text-gray-600">{t('candidates:tabs:candidateOverview:notAvailable')}</div>
              )}
            </div>
          </div>
        </If>

        <If
          condition={
            permittedFieldsManagement(permittedFields?.willingToRelocate?.roles) || permittedFields?.willingToRelocate
          }>
          <div className="flex w-full items-center">
            <IconWrapper size={16} name="Rotate3d" className="desktop:block hidden text-gray-600" />
            <TypographyText className="ml-2 text-sm text-gray-700">
              {t('candidates:tabs:candidateOverview:profileInformation:willingToRelocate')}
            </TypographyText>
          </div>

          <div className="flex w-full px-2 py-1.5">
            <Toggle
              name="willingToRelocate"
              isChecked={permittedFields?.willingToRelocate?.value}
              isDisabled
              size="sm"
            />
          </div>
        </If>

        <If
          condition={
            permittedFieldsManagement(permittedFields?.preferredWorkStates?.roles) ||
            permittedFields?.preferredWorkStates
          }>
          <div>
            <div className="flex w-full items-center py-1.5">
              <IconWrapper size={16} name="Map" className="desktop:block hidden text-gray-600" />
              <TypographyText className="ml-2 text-sm text-gray-700">
                {t('candidates:tabs:candidateOverview:profileInformation:preferredWorkStates')}
              </TypographyText>
            </div>
          </div>

          <div className="w-full">
            <div className="flex items-center px-2 py-1.5 text-sm text-gray-900 dark:text-gray-300">
              {hasPreferredWorkStates ? (
                <SuggestionChips size="sm" source={suggestionChipPreferredWorkStates} type="default" />
              ) : (
                <div className="text-gray-600 dark:text-gray-400">
                  {t('candidates:tabs:candidateOverview:notAvailable')}
                </div>
              )}
            </div>
          </div>
        </If>

        <If
          condition={
            permittedFieldsManagement(permittedFields?.noticeToPeriodDays?.roles) || permittedFields?.noticeToPeriodDays
          }>
          <div className="flex w-full items-center">
            <IconWrapper size={16} name="CalendarClock" className="desktop:block hidden text-gray-600" />
            <TypographyText className="ml-2 text-sm text-gray-700">
              {t('candidates:tabs:candidateOverview:profileInformation:noticeOfPeriods')}
            </TypographyText>
          </div>

          <div className="w-full">
            <div className="flex items-center px-2 py-1.5 text-sm">
              {permittedFields?.noticeToPeriodDays?.value ? (
                <div className="text-gray-900">{permittedFields?.noticeToPeriodDays?.value}</div>
              ) : (
                <div className="text-gray-600">{t('candidates:tabs:candidateOverview:notAvailable')}</div>
              )}
            </div>
          </div>
        </If>

        {mappingsField.map((customField, customFieldIndex) => (
          <CustomField
            key={customFieldIndex}
            type={customField.type as CustomFieldComponentType['type']}
            display="view_horizontal"
            viewDefaultPlaceholder={`${t('candidates:tabs:candidateOverview:notAvailable')}`}
            name={customField.name}
            label={customField.label}
            value={
              Object.values(
                (formatInitialValueCustomField(profilesShow?.customFields) || {}) as CustomFieldFormType
              ).find((item) => String(item.id) === String(customField.id))?.value || ''
            }
            onChange={() => {}}
            extraProps={{
              options: customField.selectOptions
            }}
          />
        ))}
      </div>

      <If condition={permittedFieldsManagement(permittedFields?.summary?.roles) || permittedFields?.summary}>
        <div className="mt-6 flex items-center justify-between">
          <TypographyText className="text-lg font-medium text-gray-900">
            {t('candidates:tabs:candidateOverview:summary:heading')}
          </TypographyText>
        </div>

        <div className="w-full max-w-[656px]">
          <div className="flex items-center text-xl">
            {permittedFields?.summary?.value ? (
              <HTMLDisplay
                content={permittedFields?.summary?.value}
                className="max-w-full py-1.5 text-sm text-gray-900"
              />
            ) : (
              <TypographyText className="mt-2 text-sm text-gray-600">
                {t('candidates:tabs:candidateOverview:notAvailable')}
              </TypographyText>
            )}
          </div>
        </div>
      </If>

      <If
        condition={
          permittedFieldsManagement(permittedFields?.workExperiences?.roles) || permittedFields?.workExperiences
        }>
        <div className="mt-6 space-y-1">
          <div className="flex items-center justify-between">
            <TypographyText className="text-lg font-medium text-gray-900">
              {t('candidates:tabs:candidateOverview:workExperiences:heading')}
            </TypographyText>
          </div>
          <div className="flex items-center text-xl">
            {permittedFields?.workExperiences?.value && permittedFields?.workExperiences?.value.length ? (
              <WorkExperiencesView source={permittedFields?.workExperiences.value} isViewOnly />
            ) : (
              <TypographyText className="text-sm text-gray-600">
                {t('candidates:tabs:candidateOverview:notAvailable')}
              </TypographyText>
            )}
          </div>
        </div>
      </If>

      <If condition={permittedFieldsManagement(permittedFields?.educations?.roles) || permittedFields?.educations}>
        <div className="mt-6 space-y-1">
          <div className="flex items-center justify-between">
            <TypographyText className="text-lg font-medium text-gray-900">
              {t('candidates:tabs:candidateOverview:education:heading')}
            </TypographyText>
          </div>
          <div className="flex items-center text-xl">
            {permittedFields?.educations?.value && permittedFields?.educations?.value?.length ? (
              <EducationView isViewOnly source={permittedFields?.educations.value} />
            ) : (
              <TypographyText className="text-sm text-gray-600">
                {t('candidates:tabs:candidateOverview:notAvailable')}
              </TypographyText>
            )}
          </div>
        </div>
      </If>

      <If condition={permittedFieldsManagement(permittedFields?.certificates?.roles) || permittedFields?.certificates}>
        <div className="mt-6 space-y-1">
          <div className="flex items-center justify-between">
            <TypographyText className="text-lg font-medium text-gray-900">
              {t('candidates:tabs:candidateOverview:certificates:heading')}
            </TypographyText>
          </div>
          <div className="flex items-center text-xl">
            {permittedFields?.certificates?.value && permittedFields?.certificates?.value?.length ? (
              <div className="space-y-1.5">
                {permittedFields?.certificates?.value.map((item: CertificatesType, index) => (
                  <div className="relative flex flex-col" key={`item-${index}`}>
                    <div className="group flex justify-between">
                      <div>
                        <div className="flex items-center">
                          <span className="mr-2.5 h-1 w-1 rounded-xs bg-gray-600" />
                          {item.certificateName ? (
                            <TypographyText className="text-sm font-medium text-gray-900">
                              {item.certificateName}
                            </TypographyText>
                          ) : null}
                          {item.institution ? (
                            <>
                              <span className="mx-2 h-0.5 w-0.5 rounded-xs bg-gray-400" />
                              <TypographyText className="text-sm font-medium text-gray-900">
                                {item.institution}
                              </TypographyText>
                            </>
                          ) : null}
                          {item.institution ? (
                            <>
                              <span className="mx-2 h-0.5 w-0.5 rounded bg-gray-400" />
                              <TypographyText className="text-sm font-medium text-gray-900">
                                {item.institution}
                              </TypographyText>
                            </>
                          ) : null}
                        </div>

                        {checkFieldFormatDate({ attributes: item }) && (
                          <div className="mt-2-px ml-[14px] text-sm text-gray-600">
                            {getFieldFormatDate({
                              attributes: item,
                              locale: i18n?.language
                            })}
                          </div>
                        )}
                      </div>

                      {checkFieldFormatDate({ attributes: item }) && (
                        <div className="mt-2-px ml-[14px] text-sm text-gray-600">
                          {getFieldFormatDate({ attributes: item })}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <TypographyText className="text-sm text-gray-600">
                {t('candidates:tabs:candidateOverview:notAvailable')}
              </TypographyText>
            )}
          </div>
        </div>
      </If>

      <If condition={permittedFieldsManagement(permittedFields?.references?.roles) || permittedFields?.references}>
        <div className="mt-6 space-y-1">
          <div className="flex items-center justify-between">
            <TypographyText className="text-lg font-medium text-gray-900">
              {t('candidates:tabs:candidateOverview:references:heading')}
            </TypographyText>
          </div>
          <div className="flex items-center text-xl">
            {permittedFields?.references?.value && permittedFields?.references?.value?.length ? (
              <div className="space-y-1.5">
                {permittedFields?.references?.value.map((item, index) => (
                  <div className="relative flex flex-col" key={`item-${index}`}>
                    <div className="group flex justify-between">
                      <div>
                        <div className="flex items-center">
                          <span className="mr-2.5 h-1 w-1 rounded-xs bg-gray-600" />
                          <TypographyText className="mr-2 text-sm font-medium text-gray-900">
                            {item.name}
                          </TypographyText>
                          <TypographyText className="text-sm text-gray-600">({item.email})</TypographyText>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <TypographyText className="text-sm text-gray-600">
                {t('candidates:tabs:candidateOverview:notAvailable')}
              </TypographyText>
            )}
          </div>
        </div>
      </If>
    </div>
  )
}

export default SettingsProfileInformationView
