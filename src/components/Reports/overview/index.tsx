'use client'

import type { BarDatum } from '@nivo/bar'
import type { FC } from 'react'
import { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import type { AnyVariables, DocumentInput } from 'urql'

import useStaticData from 'src/hooks/data/use-static-data'
import { AGENCY_TENANT } from '~/core/constants/enum'
import { Bar<PERSON><PERSON> } from '~/core/ui/BarChart'
import { Met<PERSON><PERSON><PERSON> } from '~/core/ui/MetricChart'
import { Pie<PERSON><PERSON> } from '~/core/ui/PieChart'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn } from '~/core/ui/utils'
import { detectSearchEmpty } from '~/core/utilities/common'
import { insertKeyInOrder } from '~/core/utilities/common'
import { defaultFormatDate, monthYearFormatDate } from '~/core/utilities/format-date'

import type { IStageType } from '~/lib/features/jobs/types'
import QueryDetailApplications from '~/lib/features/reports/graphql/query-detail-applications'
import QueryDetailApplicantsPerSourceList from '~/lib/features/reports/graphql/query-detail-applications-per-source-list'
import QueryReportsHiresBySourceList from '~/lib/features/reports/graphql/query-detail-hires-by-source-list'
import QueryDetailOpenJobs from '~/lib/features/reports/graphql/query-detail-open-jobs'
import QueryDetailOverviewPipelineDetailList from '~/lib/features/reports/graphql/query-detail-overview-pipeline'
import QueryReportsOverviewApplicants from '~/lib/features/reports/graphql/query-overview-applicants'
import QueryReportsOverviewApplicantsPerSource from '~/lib/features/reports/graphql/query-overview-applications-per-source'
import QueryReportsOverviewHires from '~/lib/features/reports/graphql/query-overview-hires'
import QueryReportsOverviewHiresBySource from '~/lib/features/reports/graphql/query-overview-hires-by-source'
import QueryReportsOverviewOpenJobs from '~/lib/features/reports/graphql/query-overview-open-jobs'
import QueryReportsOverviewPipelines from '~/lib/features/reports/graphql/query-overview-pipelines'
import QueryReportsOverviewTimeToHire from '~/lib/features/reports/graphql/query-overview-time-to-hire'
import QueryReportsTimeToHiresList from '~/lib/features/reports/graphql/query-time-to-hires-list'
import type {
  IReportDetailApplication,
  IReportDetailOpenJob,
  IReportManagementFilter
} from '~/lib/features/reports/types'
import { METRIC_REPORT_DATA_TYPE } from '~/lib/features/reports/utilities/enum'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import type { ISkill, TextAnchor } from '~/lib/features/settings/skills/types'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'

import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'

import BarChartWrapper from '../components/BarChartWrapper'
import MetricChartWrapper from '../components/MetricChartWrapper'
import PieChartWrapper from '../components/PieChartWrapper'
import ReportDetailModal from '../components/ReportDetailModal'

const ItemToolTip: FC<{ color: string; value: number; name: string }> = ({ color, value, name }) => {
  return (
    <div className="mt-2 flex flex-row items-center">
      <div className="h-3 w-3 rounded-xs" style={{ background: color }} />
      <span className="ml-2 text-xs font-normal text-gray-700">
        {name}: <span className="font-medium text-gray-900">{value}</span>
      </span>
    </div>
  )
}

const getTitleTooltipPipeline = (type: string, fromDate: string, toDate: string, locale: string) => {
  if (type === 'day') {
    return defaultFormatDate(new Date(fromDate))
  }
  if (type === 'week') {
    return `${defaultFormatDate(new Date(fromDate))} - ${defaultFormatDate(new Date(toDate))}`
  }
  return monthYearFormatDate(new Date(fromDate), locale)
}

const OverviewReport = ({
  tab,
  filterControl
}: {
  tab: string
  filterControl: {
    value?: IReportManagementFilter
    onChange?: (value?: IReportManagementFilter) => void
  }
}) => {
  const { t, i18n } = useTranslation('report')
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()

  const { isCompanyKind: isAgency } = useDetectCompanyWithKind({
    kind: AGENCY_TENANT
  })

  const isShowCompanyFeature = isFeatureEnabled(PLAN_FEATURE_KEYS.company) && isUnLockFeature(PLAN_FEATURE_KEYS.company)

  const isCompanyKind = isAgency || isShowCompanyFeature

  const stageTypes = useStaticData({
    keyName: 'agency_stageTypes',
    locale: i18n.language
  })

  const [filterPipelineLocal, setFilterPipelineLocal] = useState<{
    [id: string]: {
      color: string
      enable: boolean
      id: number
      bgColor: string
      hoverBgColor: string
    }
  }>({
    sourced: {
      enable: true,
      color: 'hsl(221.71deg 100% 67.84%)',
      id: 1,
      bgColor: 'bg-chart-100',
      hoverBgColor: 'group-hover:bg-chart-100'
    },
    applied: {
      enable: true,
      color: 'hsl(258.37deg 100% 71.18%)',
      id: 2,
      bgColor: 'bg-chart-50',
      hoverBgColor: 'group-hover:bg-chart-50'
    },
    screening: {
      enable: true,
      color: 'hsl(0deg 90.91% 78.43%)',
      id: 3,
      bgColor: 'bg-chart-1000',
      hoverBgColor: 'group-hover:bg-chart-1000'
    },
    ...(isCompanyKind
      ? {
          clientSubmission: {
            enable: true,
            color: 'hsl(21.64deg 100% 76.08%)',
            id: 7,
            bgColor: 'bg-chart-500',
            hoverBgColor: 'group-hover:bg-chart-500'
          }
        }
      : undefined),
    interview: {
      enable: true,
      color: 'hsl(43.05deg 84.52% 69.61%)',
      id: 4,
      bgColor: 'bg-chart-400',
      hoverBgColor: 'group-hover:bg-chart-400'
    },
    offer: {
      enable: true,
      color: 'hsl(116.51deg 50.59% 66.67%)',
      id: 5,
      bgColor: 'bg-pastel-green-500',
      hoverBgColor: 'group-hover:bg-pastel-green-500'
    },
    hired: {
      enable: true,
      color: 'hsl(158.76deg 55.67% 60.2%)',
      id: 6,
      bgColor: 'bg-chart-300',
      hoverBgColor: 'group-hover:bg-chart-300'
    }
  })

  const [reportDetailConfig, setReportDetailConfig] = useState<{
    open: boolean
    type?: 'application' | 'job' | 'referral' | 'hires' | 'pipeline'
    title?: string
    mappingData?: (dataCollection: { data: Array<any> }) => {
      data: Array<IReportDetailApplication | IReportDetailOpenJob>
    }
    query?: DocumentInput<any, AnyVariables>
    variablesFilter?: {
      stageKey?: string
      sourced?: string
      stageTypeId?: number
      fromDateTimePipelineItem?: string
      toDateTimePipelineItem?: string
    }
  }>({
    open: false
  })

  const isSearchEmpty = detectSearchEmpty({
    filters: filterControl.value as {
      [key: string]: unknown
    },
    filterKeys: ['dateRange', 'departmentIds', 'hiringMemberIds', 'locationIds', 'jobIds', 'teamIds']
  })

  const mappingActionLogData = useCallback((collection: { data: Array<any>; meta?: object }) => {
    return {
      data: collection?.data?.map((actionLog) => ({
        profile: actionLog.profile,
        job: actionLog.job,
        ...actionLog.applicant,
        timeToHire: actionLog.hiredHiringDays
      })),
      meta: collection?.meta
    }
  }, [])

  useEffect(() => {
    if (isCompanyKind) {
      setFilterPipelineLocal((prev) =>
        insertKeyInOrder(
          prev,
          'clientSubmission',
          {
            enable: true,
            color: 'hsl(21.64deg 100% 76.08%)',
            id: 7,
            bgColor: 'bg-chart-500',
            hoverBgColor: 'group-hover:bg-chart-500'
          },
          'screening'
        )
      )
    }
  }, [isCompanyKind])

  return (
    <div className="flex flex-col space-y-4 pr-6 pb-6">
      {reportDetailConfig?.open ? (
        <ReportDetailModal
          open
          setOpen={(value) => setReportDetailConfig(value ? { ...reportDetailConfig, open: value } : { open: false })}
          title={reportDetailConfig?.title}
          type={reportDetailConfig?.type}
          mappingData={reportDetailConfig?.mappingData}
          queryReportDetail={reportDetailConfig?.query}
          filterControl={{
            value: {
              ...(filterControl?.value || {}),
              ...(reportDetailConfig?.variablesFilter || {})
            },
            onChange: filterControl?.onChange
          }}
        />
      ) : null}

      <div className="flex flex-1 items-center space-x-4">
        <MetricChartWrapper
          tab={tab}
          dataTypeMapping={METRIC_REPORT_DATA_TYPE.openJobs}
          url={QueryReportsOverviewOpenJobs}
          filter={filterControl.value}>
          {({ isLoading, data }) => (
            <MetricChart
              className="flex-1"
              title={t('report:detail:open_jobs')}
              helperConfig={{
                icon: 'HelpCircle',
                content: t('report:detail:open_job_help_info')
              }}
              previousTooltip={`${t('report:tooltip:previousPeriod')}`}
              dataSource={{
                noChange: t('report:noChangeFromPreviousTime'),
                isLoading,
                data
              }}
              onClickTotalNumber={() => {
                setReportDetailConfig({
                  open: true,
                  type: 'job',
                  query: QueryDetailOpenJobs,
                  title: `${t('report:detail:open_jobs')}`
                })
              }}
            />
          )}
        </MetricChartWrapper>

        <MetricChartWrapper
          tab={tab}
          dataTypeMapping={METRIC_REPORT_DATA_TYPE.applicants}
          url={QueryReportsOverviewApplicants}
          filter={filterControl.value}>
          {({ isLoading, data }) => (
            <MetricChart
              className="flex-1"
              title={t('report:detail:applicants')}
              helperConfig={{
                icon: 'HelpCircle',
                content: <>{t('report:detail:application_help_info')}</>
              }}
              previousTooltip={`${t('report:tooltip:previousPeriod')}`}
              dataSource={{
                noChange: t('report:noChangeFromPreviousTime'),
                isLoading,
                data
              }}
              onClickTotalNumber={() => {
                setReportDetailConfig({
                  open: true,
                  type: 'application',
                  query: QueryDetailApplications,
                  title: `${t('report:detail:applicants')}`
                })
              }}
            />
          )}
        </MetricChartWrapper>

        <MetricChartWrapper
          tab={tab}
          dataTypeMapping={METRIC_REPORT_DATA_TYPE.hires}
          url={QueryReportsOverviewHires}
          filter={filterControl.value}>
          {({ isLoading, data: hiresData }) => (
            <>
              <MetricChart
                className="flex-1"
                title={t('report:detail:hires')}
                helperConfig={{
                  icon: 'HelpCircle',
                  content: <>{t('report:detail:hires_help_info')}</>
                }}
                previousTooltip={`${t('report:tooltip:previousPeriod')}`}
                dataSource={{
                  noChange: t('report:noChangeFromPreviousTime'),
                  isLoading,
                  data: hiresData
                }}
                onClickTotalNumber={() => {
                  setReportDetailConfig({
                    open: true,
                    type: 'hires',
                    query: QueryReportsTimeToHiresList,
                    title: `${t('report:detail:hires')}`,
                    mappingData: mappingActionLogData
                  })
                }}
              />
              <MetricChartWrapper
                tab={tab}
                dataTypeMapping={METRIC_REPORT_DATA_TYPE.timeToHires}
                url={QueryReportsOverviewTimeToHire}
                filter={filterControl.value}>
                {({ isLoading, data }) => (
                  <MetricChart
                    className="flex-1"
                    title={t('report:detail:time_to_hire')}
                    helperConfig={{
                      icon: 'HelpCircle',
                      align: 'end',
                      content: (
                        <>
                          {t('report:detail:time_to_hire_help_info', {
                            position_text:
                              (filterControl?.value?.jobIds || [])?.length > 1
                                ? `${(filterControl?.value?.jobIds || [])?.length} ${t('report:positions')}`
                                : (filterControl?.value?.jobIds || [])?.length === 1
                                  ? t('report:thisPosition')
                                  : t('report:allPositions')
                          })}{' '}
                        </>
                      )
                    }}
                    previousTooltip={`${t('report:tooltip:previousPeriod')}`}
                    dataSource={{
                      noChange: t('report:noChangeFromPreviousTime'),
                      isLoading,
                      data
                    }}
                    isShowZeroNumber={(hiresData?.currentData || 0) > 0}
                    onClickTotalNumber={() => {
                      setReportDetailConfig({
                        open: true,
                        type: 'hires',
                        query: QueryReportsTimeToHiresList,
                        title: `${t('report:detail:application_table:time_to_hire')}`,
                        mappingData: mappingActionLogData
                      })
                    }}
                  />
                )}
              </MetricChartWrapper>
            </>
          )}
        </MetricChartWrapper>
      </div>

      <div className="max-h-[500px] rounded-md border border-solid border-gray-100">
        <BarChartWrapper
          tab={tab}
          dataTypeMapping={METRIC_REPORT_DATA_TYPE.reportsOverviewPipelines}
          url={QueryReportsOverviewPipelines}
          filter={filterControl.value}>
          {({ isLoading, data: pipelineData }) => {
            const summaryTotal = (row: BarDatum) => {
              const { applied, hired, sourced, interview, offer, screening, clientSubmission } = row
              return (
                (filterPipelineLocal['applied']?.enable ? Number(applied || 0) : 0) +
                (filterPipelineLocal['hired']?.enable ? Number(hired || 0) : 0) +
                (filterPipelineLocal['sourced']?.enable ? Number(sourced || 0) : 0) +
                (filterPipelineLocal['interview']?.enable ? Number(interview || 0) : 0) +
                (filterPipelineLocal['offer']?.enable ? Number(offer || 0) : 0) +
                (filterPipelineLocal['screening']?.enable ? Number(screening || 0) : 0) +
                (filterPipelineLocal['clientSubmission']?.enable ? Number(clientSubmission || 0) : 0)
              )
            }

            return (
              <BarChart
                title={`${t('report:detail:pipeline_overview')}`}
                helperConfig={{
                  icon: 'HelpCircle',
                  iconTitle: '',
                  content: t('report:tooltip:pipeline_overview')
                }}
                dataSource={{
                  isLoading,
                  emptyData: t('report:empty:no_data_collect'),
                  emptySearch: t('report:empty:no_results_found'),
                  data: pipelineData ? pipelineData : isSearchEmpty ? [] : undefined,
                  onClick: (node) => {}
                }}
                configBarChartProps={{
                  data: pipelineData || [],
                  keys: Object.keys(filterPipelineLocal)
                    .filter((key) => {
                      return filterPipelineLocal[key as keyof typeof filterPipelineLocal]?.enable
                    })
                    .map((key) => key),
                  tooltip: ({ data }) => {
                    return (
                      <div className="animate-appear invisible min-w-[148px] rounded-xs bg-white px-3 py-3 shadow-lg">
                        <span className="text-xs font-medium text-gray-700">
                          {getTitleTooltipPipeline(
                            data.groupType as string,
                            data.fromDate as string,
                            data.toDate as string,
                            i18n.language
                          )}
                        </span>
                        <div>
                          {Object.keys(filterPipelineLocal)
                            .filter((key) => filterPipelineLocal[key]?.enable)
                            .map((key) => {
                              const newKey = key === 'clientSubmission' ? 'submission' : key
                              return (
                                <ItemToolTip
                                  key={key}
                                  name={
                                    (stageTypes || []).filter((stage: IStageType) => {
                                      return stage.colorClassName === newKey
                                    })?.[0]?.label
                                  }
                                  value={Number(data[key as keyof typeof data] || 0)}
                                  color={String(filterPipelineLocal[key]?.color)}
                                />
                              )
                            })}
                        </div>
                        <div className="mt-2">
                          <span className="text-xs font-medium text-gray-900">
                            {t('report:totalCandidate', {
                              totalCount: summaryTotal(data)
                            })}
                          </span>
                        </div>
                      </div>
                    )
                  },
                  onMouseEnter: (datum, event) => {
                    event.currentTarget.style.cursor = 'pointer'
                  },
                  indexBy: 'id',
                  margin: { top: 32, right: 32, bottom: 50, left: 50 },
                  padding: 0.3,
                  colors: ({ id, data }) => String(data[`${id}Color` as keyof typeof data]),
                  axisBottom: {
                    tickSize: 5,
                    tickPadding: 10,
                    tickRotation: -15,
                    renderTick: (props) => {
                      const { tickIndex, x, y, textX, textY, textAnchor, value } = props
                      const record = (pipelineData || [])[tickIndex]
                      const label = record?.label
                      const groupType = record?.groupType
                      const rotate = record?.label?.includes('-') ? 'rotate(-15)' : 'rotate(0)'

                      return (
                        <g transform={`translate(${x},${y})`} style={{ opacity: 1 }}>
                          <line
                            x1="0"
                            x2="0"
                            y1="0"
                            y2="5"
                            style={{
                              stroke: 'rgb(119, 119, 119)',
                              strokeWidth: 1
                            }}></line>
                          <text
                            textAnchor={textAnchor as TextAnchor}
                            transform={`translate(${textX + (groupType === 'day' ? 5 : 10)},${textY + 2}) ${rotate}`}
                            style={{
                              fontFamily: 'sans-serif',
                              fontSize: 11,
                              fill: 'rgb(51, 51, 51)'
                            }}>
                            {label}
                          </text>
                          {record?.extraLabel && (
                            <text
                              textAnchor={textAnchor as TextAnchor}
                              transform={`translate(${textX + 12},${textY + 20}) ${rotate}`}
                              style={{
                                fontWeight: 'bold',
                                fontFamily: 'sans-serif',
                                fontSize: 11,
                                fill: 'rgb(51, 51, 51)'
                              }}>
                              {record?.extraLabel}
                            </text>
                          )}
                        </g>
                      )
                    }
                  },
                  layers: ['grid', 'axes', 'bars'],
                  axisLeft: {
                    tickSize: 5,
                    tickPadding: 5,
                    tickRotation: 0,
                    legend: '',
                    legendPosition: 'middle',
                    legendOffset: 0
                  },
                  animate: true,
                  enableLabel: false,
                  isInteractive: true,
                  onClick: (datum, event) => {
                    setReportDetailConfig({
                      open: true,
                      type: 'pipeline',
                      query: QueryDetailOverviewPipelineDetailList,
                      title: `${t('report:detail:pipeline_overview')}`,
                      variablesFilter: {
                        stageTypeId: filterPipelineLocal[datum.id]?.id,
                        fromDateTimePipelineItem: datum?.data?.fromDate as string,
                        toDateTimePipelineItem: datum?.data?.toDate as string
                      }
                    })
                  }
                }}
                footerBarChart={
                  <div className="my-4 flex">
                    {Object.keys(filterPipelineLocal).map((key) => (
                      <div key={key} className="group ml-8 flex flex-row items-center hover:opacity-50">
                        <div
                          onClick={() => {
                            setFilterPipelineLocal({
                              ...filterPipelineLocal,
                              [key]: {
                                ...filterPipelineLocal[key],
                                id: Number(filterPipelineLocal[key]?.id),
                                color: String(filterPipelineLocal[key]?.color),
                                bgColor: String(filterPipelineLocal[key]?.bgColor),
                                hoverBgColor: String(filterPipelineLocal[key]?.hoverBgColor),
                                enable: !filterPipelineLocal[key]?.enable
                              }
                            })
                          }}
                          className={cn(
                            'h-3 w-3 cursor-pointer rounded-xs',
                            !filterPipelineLocal[key]?.enable
                              ? `bg-gray-300 ${filterPipelineLocal[key]?.hoverBgColor}`
                              : filterPipelineLocal[key]?.bgColor
                          )}
                        />
                        <Tooltip content={t('report:tooltip:click_to_exclude_this_stage')}>
                          <span
                            onClick={() => {
                              setFilterPipelineLocal({
                                ...filterPipelineLocal,
                                [key]: {
                                  ...filterPipelineLocal[key],
                                  id: Number(filterPipelineLocal[key]?.id),
                                  color: String(filterPipelineLocal[key]?.color),
                                  bgColor: String(filterPipelineLocal[key]?.bgColor),
                                  hoverBgColor: String(filterPipelineLocal[key]?.hoverBgColor),
                                  enable: !filterPipelineLocal[key]?.enable
                                }
                              })
                            }}
                            className={cn(
                              'ml-2 cursor-pointer text-xs capitalize',
                              !filterPipelineLocal[key]?.enable ? 'text-gray-400' : 'text-gray-700'
                            )}>
                            {
                              (stageTypes || []).filter((stage: IStageType) => {
                                const newKey = key === 'clientSubmission' ? 'submission' : key
                                return stage.colorClassName === newKey
                              })?.[0]?.label
                            }
                          </span>
                        </Tooltip>
                      </div>
                    ))}
                  </div>
                }
              />
            )
          }}
        </BarChartWrapper>
      </div>

      <div className="grid flex-1 grid-cols-2 gap-x-4">
        <PieChartWrapper
          tab={tab}
          dataTypeMapping={METRIC_REPORT_DATA_TYPE.applicantsPerSource}
          url={QueryReportsOverviewApplicantsPerSource}
          filter={filterControl.value}>
          {({ isLoading, data }) => (
            <PieChart
              className="min-h-[414px] hover:cursor-pointer"
              title={t('report:detail:new_applications') || ''}
              helperConfig={{
                icon: 'HelpCircle',
                iconTitle: `${t('report:tooltip:per_source')}`,
                content: t('report:tooltip:total_per_applications')
              }}
              dataSource={{
                isLoading,
                emptyData: t('report:empty:no_data_collect'),
                emptySearch: t('report:empty:no_results_found'),
                data: data ? data : isSearchEmpty ? [] : undefined,
                onClick: (node) => {
                  setReportDetailConfig({
                    open: true,
                    type: 'application',
                    query: QueryDetailApplicantsPerSourceList,
                    variablesFilter: {
                      sourced: node.data.key
                    },
                    title: `${t('report:detail:new_applications_per_source')}`
                  })
                }
              }}
            />
          )}
        </PieChartWrapper>

        <PieChartWrapper
          tab={tab}
          dataTypeMapping={METRIC_REPORT_DATA_TYPE.hiresBySource}
          url={QueryReportsOverviewHiresBySource}
          filter={filterControl.value}>
          {({ isLoading, data }) => (
            <PieChart
              className="min-h-[280px] hover:cursor-pointer"
              title={`${t('report:detail:new_hires')}`}
              helperConfig={{
                icon: 'HelpCircle',
                iconTitle: `${t('report:tooltip:per_source')}`,
                content: t('report:tooltip:total_per_hires')
              }}
              dataSource={{
                isLoading,
                emptyData: t('report:empty:no_data_collect'),
                emptySearch: t('report:empty:no_results_found'),
                data: data ? data : isSearchEmpty ? [] : undefined,
                onClick: (node) => {
                  setReportDetailConfig({
                    open: true,
                    type: 'hires',
                    query: QueryReportsHiresBySourceList,
                    variablesFilter: {
                      sourced: node.data.key
                    },
                    mappingData: mappingActionLogData,
                    title: `${t('report:detail:new_hires_per_source')}`
                  })
                }
              }}
            />
          )}
        </PieChartWrapper>
      </div>
    </div>
  )
}

export default OverviewReport
