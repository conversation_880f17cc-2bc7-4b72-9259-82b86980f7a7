import { gql } from 'urql'

import type { ICompanyPermittedFields } from '~/lib/features/agency/companies/types'

import usePaginationGraphPage from '../../../../../lib/hooks/use-pagination-graph-page'
import useGetDefaultTime from '../../hooks/use-get-default-time'
import type { TeamProductivityDetailInterviewQueryVariables } from '../../type'
import type { TeamProductivityDetailsQueryVariables } from '../../type'

export type ReportDetailSourcedModelDataFilter = Omit<
  TeamProductivityDetailInterviewQueryVariables,
  'page' | 'limit' | 'productivityType'
>

const queryReportDetailSentCandidates = gql<
  {
    teamProductivityShow: {
      collection: {
        id: string
        createdAt: string
        job: {
          id: number
          title: string
          status: string
          statusDescription: string
          company: {
            id: number
            permittedFields?: ICompanyPermittedFields
          }
          permittedFields?: {
            [key: string]: {
              role_changeable?: boolean
              visibility_changeable?: boolean
              roles?: Array<string>
              value?: string & { name?: string; id?: number }
            }
          }
        }
        applicant: {
          profile: {
            id: number
            fullName: string
            avatarVariants: {
              thumb: { url: string }
            }
          }
        }
        loggedDate: string
        payload: {
          stage_label: {
            from: string
            to: string
          }
        }
      }[]
      metadata: { totalCount: number }
    }
  },
  TeamProductivityDetailsQueryVariables
>`
  query (
    $limit: Int!
    $page: Int!
    $productivityType: String
    $userId: Int
    $fromDatetime: ISO8601DateTime!
    $toDatetime: ISO8601DateTime!
    $jobIds: [Int!]
    $hiringMemberIds: [Int!]
    $locationIds: [Int!]
    $departmentId: Int
    $departmentIds: [Int!]
  ) {
    teamProductivityShow(
      limit: $limit
      page: $page
      productivityType: $productivityType
      userId: $userId
      fromDatetime: $fromDatetime
      toDatetime: $toDatetime
      jobIds: $jobIds
      hiringMemberIds: $hiringMemberIds
      departmentId: $departmentId
      locationIds: $locationIds
      departmentIds: $departmentIds
    ) {
      collection {
        propertiesRelated
        createdAt
        actionKey
        payload
        job {
          id
          title
          status
          statusDescription
          permittedFields
        }
        applicant {
          profile {
            id
            fullName
            avatarVariants
          }
        }
        loggedDate
      }
      metadata {
        totalCount
      }
    }
  }
`

const useReportDetailSource = ({
  filter,
  enabled,
  type
}: {
  filter?: ReportDetailSourcedModelDataFilter
  enabled: boolean
  type: string
}) => {
  const getDefaultTime = useGetDefaultTime()
  const query = usePaginationGraphPage({
    queryDocumentNode: queryReportDetailSentCandidates,
    filter: {
      ...(filter as ReportDetailSourcedModelDataFilter),
      productivityType: type,
      ...(filter ? getDefaultTime(filter) : {})
    },
    enabled
  })
  return { query }
}
export default useReportDetailSource
