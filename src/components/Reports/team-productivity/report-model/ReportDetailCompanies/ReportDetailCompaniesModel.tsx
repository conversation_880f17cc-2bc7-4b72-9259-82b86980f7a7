import type { CellContext } from '@tanstack/react-table'
import React from 'react'
import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import type { IUserInformation } from '~/core/@types/global'
import { Avatar } from '~/core/ui/Avatar'
import { Dialog } from '~/core/ui/Dialog'
import { InputChips } from '~/core/ui/InputChips'
import { TablePagination } from '~/core/ui/TablePagination'
import { TypographyText } from '~/core/ui/Text'
import { Tooltip } from '~/core/ui/Tooltip'
import { defaultFormatDate } from '~/core/utilities/format-date'

import HeaderTable from '~/components/Reports/components/HeaderTable'

import type { ReportDetailModelDataFilter } from '../../type'
import useReportDetailCompanies from './report-detail-companies-hooks'

interface ReportDetailCompaniesModelProps {
  open: boolean
  setOpen: (open: boolean) => void
  filter: ReportDetailModelDataFilter | undefined
  user: IUserInformation
}
type ReportDetailCompaniesTableData = NonNullable<
  ReturnType<typeof useReportDetailCompanies>['query']['data']
>['data'][0]
const ReportDetailCompaniesModel: React.FC<ReportDetailCompaniesModelProps> = ({ open, setOpen, filter, user }) => {
  const { query } = useReportDetailCompanies({ enabled: open, filter })
  const { t, i18n } = useTranslation()

  return (
    <Dialog
      className="min-w-[880px]"
      open={open}
      isPreventAutoFocusDialog
      label={`${t('label:reportKPI:company_added')}`}
      isDivider={false}
      headingClassName="border-b border-b-gray-100"
      description={
        <InputChips
          buttonClassName="max-w-full w-auto"
          type="ava-leading"
          label={user?.fullName}
          colorAvatar={user?.defaultColour}
          altAvatar={user?.fullName}
          srcAvatar={user?.avatarVariants?.thumb?.url}
          size="sm"
        />
      }
      onOpenChange={() => {
        setOpen(false)
        query.goToPage(1)
      }}>
      <TablePagination
        textOverride={{
          of: `${t('label:of')}`,
          page: `${t('label:page')}`,
          placeholder: `${t('label:placeholder:select')}`,
          search: `${t('label:placeholder:search')}`,
          loading: `${t('label:loading')}`,
          noOptions: `${t('label:noOptions')}`,
          rowsPerPage: `${t('label:rowsPerPage')}`
        }}
        isHeaderSticky
        search={{}}
        emptyConfig={{
          classNameEmpty: 'min-h-[300px] flex items-center h-full justify-center',
          title: `${t('report:empty:no_data_collect')}`,
          titleSearch: `${t('report:empty:no_results_found')}`
        }}
        tableConfig={{
          defaultPageSize: configuration.defaultPageSize
        }}
        dataQuery={{
          isFetching: query.isFetching,
          fetcher: {
            fetchPagination: query.fetchPagination,
            forceChangeCurrentPage: query.forceChangeCurrentPage
          },
          data: query.data
        }}
        columns={[
          {
            accessorKey: 'jobTitle',
            header: () => <HeaderTable text={t('report:detail:companiesTable:name')} />,
            cell: (info: CellContext<ReportDetailCompaniesTableData, undefined>) => {
              const actionLog = info?.row?.original?.company as any

              return (
                <div className="flex items-center">
                  <div className="mr-2">
                    <Avatar
                      defaultAvatar={false}
                      shape="rounded"
                      src={actionLog?.logoVariants?.thumb?.url}
                      alt={actionLog?.permittedFields?.name?.value}
                      size="sm"
                    />
                  </div>
                  <div className="line-clamp-1">
                    <Tooltip content={actionLog?.permittedFields?.name?.value}>
                      <TypographyText
                        className="mr-2 cursor-pointer truncate text-sm font-medium text-gray-900 hover:underline"
                        onClick={() => {
                          window.open(configuration.path.agency.companyDetail(Number(actionLog?.id)), '_blank')
                        }}>
                        {actionLog?.permittedFields?.name?.value}
                      </TypographyText>
                    </Tooltip>
                  </div>
                </div>
              )
            },
            size: 328
          },
          {
            accessorKey: 'status',
            header: () => <HeaderTable text={t('report:detail:companiesTable:status')} />,
            cell: (info: CellContext<ReportDetailCompaniesTableData, undefined>) => {
              const actionLog = info?.row?.original?.company
              return (
                <Tooltip content={actionLog?.companyStatus?.name}>
                  <div className="truncate text-sm font-normal text-gray-900">
                    {actionLog?.companyStatus?.name || '-'}
                  </div>
                </Tooltip>
              )
            },
            size: 140
          },
          {
            accessorKey: 'owner',
            header: () => <HeaderTable text={t('report:detail:companiesTable:owner')} />,
            cell: (info: CellContext<ReportDetailCompaniesTableData, undefined>) => {
              const actionLog = info?.row?.original?.company?.permittedFields as any

              const owner = actionLog?.owner?.value
              return (
                <div className="flex justify-start">
                  <Avatar
                    size="sm"
                    src={owner?.avatarVariants?.thumb?.url}
                    alt={owner?.fullName}
                    color={owner?.default_colour}
                  />
                </div>
              )
            },
            size: 108
          },
          {
            accessorKey: 'createdBy',
            header: () => <HeaderTable text={t('report:detail:companiesTable:createdBy')} />,
            cell: (info: CellContext<ReportDetailCompaniesTableData, undefined>) => {
              const actionLog = info?.row.original?.company
              const profile = actionLog?.createdBy
              return (
                <div className="flex justify-start">
                  <Avatar
                    size="sm"
                    src={profile?.avatarVariants?.thumb?.url}
                    alt={profile?.fullName}
                    color={profile?.defaultColour}
                  />
                </div>
              )
            },
            size: 108
          },
          {
            accessorKey: 'createdAt',
            header: () => <HeaderTable text={t('report:detail:companiesTable:createdAt')} />,
            cell: (info: CellContext<ReportDetailCompaniesTableData, undefined>) => {
              const actionLog = info?.row?.original?.company
              return (
                <div className="text-sm font-normal text-gray-900">
                  {actionLog?.createdAt ? defaultFormatDate(new Date(actionLog.createdAt)) : '-'}
                </div>
              )
            },
            size: 180
          }
        ]}
        classNameTable={'max-h-[calc(100vh-234px)] -mr-6'}
        classNamePaginationWrapper={(query.data?.data || [])?.length > 0 ? 'pb-0 -mb-3' : ''}
      />
    </Dialog>
  )
}

export default ReportDetailCompaniesModel
