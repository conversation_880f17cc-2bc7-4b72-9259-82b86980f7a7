'use client'

import { useRouter } from 'next/navigation'
import type { FC } from 'react'
import { useEffect, useRef, useState } from 'react'
import type { UseFormReset, UseFormSetValue } from 'react-hook-form'
import { Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import useEnumsData from 'src/hooks/data/use-enums-data'
import type { IRouterWithID, ISelectOption, ITenantDetail } from '~/core/@types/global'
import ComboboxSelect from '~/core/ui/ComboboxSelect'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import { FormControlItem } from '~/core/ui/FormControlItem'
import IconWrapper from '~/core/ui/IconWrapper'
import { Input } from '~/core/ui/Input'
import { InputLeftAddon, InputRightAddon } from '~/core/ui/InputAddon'
import { InputGroup } from '~/core/ui/InputGroup'
import { debounce } from '~/core/ui/utils'
import { buildURL } from '~/core/utilities/common'

import { useCareer } from '~/lib/features/careers/[id]/hooks/use-career'
import schemaCareerSearch from '~/lib/features/careers/[id]/schema/career-search-schema'
import type { CareerPageSettingType, ICareerForm, TenantType } from '~/lib/features/careers/[id]/types'
import { renderCombineText } from '~/lib/features/jobs/utilities/common'
import { DEPARTMENT_SHOW_TOP_LEVEL } from '~/lib/features/settings/careers/utilities/enum'
import useDepartment from '~/lib/features/settings/departments/hooks/useDepartment'
import type { IDepartment } from '~/lib/features/settings/departments/types'
import { useRouterContext } from '~/lib/next/use-router-context'

import type { ICareerSearchParam } from '../../../lib/features/careers/[id]/types'

interface CareerSearchProps {
  tenantSlug?: IRouterWithID
  tenant?: TenantType | ITenantDetail
  filter: ICareerForm | ICareerSearchParam
  onFinishCallback: (data: ICareerSearchParam) => Promise<void>
  careerPageSetting?: CareerPageSettingType
  extras?: any
  configHide?: {
    remoteStatus: boolean
    department?: boolean
    jobLevel?: boolean
  }
}

let careerSearchTimeout: NodeJS.Timeout
const ignoreKeys = ['isFilterTouched', 'jobsKey', 'country', 'state', 'page', 'key']
const CareerSearch: FC<CareerSearchProps> = ({
  tenantSlug,
  tenant,
  filter,
  onFinishCallback,
  careerPageSetting,
  configHide,
  extras
}) => {
  const router = useRouter()
  const { pathname, searchParams, asPath } = useRouterContext()
  const { t, i18n } = useTranslation()
  const firstLoadRef = useRef(false)
  const jobRemoteStatusOptions = useEnumsData({
    enumType: 'JobRemoteStatus',
    locale: i18n.language
  })
  const jobLevelOptions = useEnumsData({
    enumType: 'JobJobLevel',
    locale: i18n.language
  })
  const { promiseLocationsOptions } = useCareer({ tenant })
  const { promisePublicDepartmentMultiLevelOptions, promisePublicDepartmentOptions } = useDepartment()
  const formResetFuncRef = useRef<UseFormReset<ICareerForm>>()
  const formSetValueFuncRef = useRef<UseFormSetValue<ICareerForm>>()
  const [updateFilter, setUpdateFilter] = useState<any>()
  const remoteStatus = searchParams?.get('remoteStatus')
  const jobLevel = searchParams?.get('jobLevel')
  const search = searchParams?.get('search')

  useEffect(() => {
    if (!asPath.includes('/settings')) {
      const debouncedPush = debounce(() => {
        const keys = Object.keys(filter ?? {})

        let params: { [x: string]: any }[] = []
        if (keys.length > 0) {
          params = keys
            .filter((key: string) => !ignoreKeys.includes(key))
            .map((key: string) => {
              const filterItem = (filter as Record<string, any>)?.[key]
              let keyCus = key
              if (keyCus === 'remoteStatusOption') {
                keyCus = 'remoteStatus'
              }
              if (keyCus === 'referredBy') {
                keyCus = 'referred_by'
              }
              if (Array.isArray(filterItem) && filterItem?.length) {
                return {
                  [keyCus]: filterItem.map((fi: ISelectOption) => fi.value)
                }
              }
              if (
                !!(filter as Record<string, any>)?.[key] &&
                typeof (filter as Record<string, any>)?.[key] === 'object'
              ) {
                return {
                  [keyCus]:
                    ((filter as Record<string, any>)?.[key] as ISelectOption)?.id ||
                    ((filter as Record<string, any>)?.[key] as ISelectOption)?.value
                }
              }
              return {
                [keyCus]: (filter as Record<string, any>)?.[key]
              }
            })
        }

        // Get locale from current URL or default
        const pathParts = (pathname ?? '').split('/')
        const currentLocale = ['en', 'ja'].includes(pathParts[1] ?? '') ? pathParts[1] : 'en'

        // Determine correct path based on current URL
        let basePath
        if (pathname?.includes('/careers/')) {
          basePath = `/careers/${tenantSlug}`
        } else if (pathname?.includes('/career/')) {
          basePath = '/career'
        } else {
          basePath = `/careers/${tenantSlug}`
        }

        // Ensure final URL has structure: /{locale}{basePath}
        const url = buildURL(`/${currentLocale}${basePath}`, Object.assign({}, ...params))

        // Use replace instead of push and disable scroll
        router.replace(url, { scroll: false })
      }, 300)
      debouncedPush()
    }
  }, [filter])

  useEffect(() => {
    // Only run this after we have extras data from API's response for ONE time!!
    if (firstLoadRef.current || !extras) return
    // Code mapping ISelectOption from API extras data
    let cloneFilter = { ...updateFilter }
    if (filter && extras?.countryStateList && filter?.countryStateId) {
      const updatedLocations = extras?.countryStateList?.map((item: any) => ({
        id: item.countryStateId,
        value: String(item.countryStateId),
        label: item.country,
        supportingObj: {
          name: renderCombineText([item?.state, item?.country])
        }
      }))
      cloneFilter = { ...cloneFilter, countryStateId: updatedLocations?.[0] }
      formSetValueFuncRef.current && formSetValueFuncRef.current('countryStateId', updatedLocations?.[0])
    }
    if (filter && extras?.departmentsList && filter.departmentIds?.length) {
      const cloneData = extras?.departmentsList?.map((item: IDepartment) => {
        return {
          id: item.id,
          name: item.name,
          openJobsCount: item.openJobsCount,
          parentId: item.parentId,
          subordinates: item.subordinates
        }
      })

      const newCloneData = [] as Array<ISelectOption>
      const subDepartmentCloneData = cloneData
        .filter((i: IDepartment) => !!i.parentId)
        .map((department: IDepartment) => ({
          value: String(department.id),
          parentId: String(department.parentId),
          supportingObj: {
            name: department.name || ''
          }
        }))
      cloneData
        .filter((i: IDepartment) => !i.parentId)
        .forEach((item: IDepartment) => {
          newCloneData.push({
            value: String(item.id),
            parentId: undefined,
            supportingObj: {
              name: item.name || ''
            }
          })

          if (item.subordinates?.length) {
            item.subordinates.forEach((sub) => {
              newCloneData.push({
                value: String(sub.id),
                parentId: String(item.id),
                supportingObj: {
                  name: sub.name || ''
                }
              })
            })
          }
        })
      const departmentIds = [...subDepartmentCloneData, ...newCloneData]
      cloneFilter = {
        ...cloneFilter,
        departmentIds
      }
      formSetValueFuncRef.current && formSetValueFuncRef.current('departmentIds', departmentIds)
    }
    // RemoteStatus and JobLevel is Enum data, don't need to wait for fetching data
    if (remoteStatus) {
      const remoteStatusValue = remoteStatus as string
      const remoteStatusOption = jobRemoteStatusOptions.find((opt: ISelectOption) => opt.value === remoteStatusValue)

      cloneFilter = { ...cloneFilter, remoteStatusOption }
      formSetValueFuncRef.current && formSetValueFuncRef.current('remoteStatusOption', remoteStatusOption)
    }
    if (jobLevel) {
      const jobLevelValue = jobLevel as string
      const jobLevelOption = jobLevelOptions.find((opt: ISelectOption) => opt.value === jobLevelValue)

      cloneFilter = { ...cloneFilter, jobLevel: jobLevelOption }
      formSetValueFuncRef.current && formSetValueFuncRef.current('jobLevel', jobLevelOption)
    }
    if (search) {
      cloneFilter = { ...cloneFilter, search: search }
      formSetValueFuncRef.current && formSetValueFuncRef.current('search', search as string)
    }

    setUpdateFilter(cloneFilter)
    firstLoadRef.current = true
  }, [extras, filter])

  const onSubmit = (data: ICareerForm) => {
    return onFinishCallback(data)
  }

  useEffect(() => {
    //case clear filter
    const filterKeys = Object.keys(filter).filter((key) => {
      return (key !== 'search' && ![...ignoreKeys, 'referredBy'].includes(key)) || (key === 'search' && !!filter?.[key])
    })
    if (filterKeys.length === 0) {
      setUpdateFilter(filter)
      formResetFuncRef.current && formResetFuncRef.current(filter)
    }
  }, [filter])

  return (
    <DynamicImportForm
      id="career-search-form"
      className="tablet:mb-6 mb-4 w-full"
      schema={schemaCareerSearch(t)}
      defaultValue={updateFilter}
      onSubmit={onSubmit}>
      {({ formState, control, getValues, reset, setValue }) => {
        const data = getValues()
        formResetFuncRef.current = reset
        formSetValueFuncRef.current = setValue

        return (
          <>
            <div className="tablet:mb-4 mb-3">
              <Controller
                control={control}
                name="search"
                render={({ field: { onChange, value } }) => {
                  return (
                    <FormControlItem destructiveText={formState.errors?.search?.message}>
                      <InputGroup>
                        <InputLeftAddon>
                          <IconWrapper name="Search" />
                        </InputLeftAddon>
                        <Input
                          onChange={(value) => {
                            setUpdateFilter({
                              ...updateFilter,
                              search: value
                            })
                            onChange(value)

                            if (careerSearchTimeout) clearTimeout(careerSearchTimeout)
                            careerSearchTimeout = setTimeout(() => {
                              // submit && submit()
                              onSubmit({
                                ...data,
                                search: value ? String(value) : ''
                              })
                            }, 500)
                          }}
                          value={value}
                          placeholder={`${t('careers:searchForm:searchPlaceholder')}`}
                          destructive={formState.errors && !!formState.errors?.search}
                          className="brand-color-input-focus pl-9"
                        />
                        {value && (
                          <InputRightAddon>
                            <a
                              className="cursor-pointer"
                              onClick={() => {
                                onChange('')
                                onSubmit({ ...data, search: '' })
                                // submit && submit()
                              }}>
                              <IconWrapper name="X" />
                            </a>
                          </InputRightAddon>
                        )}
                      </InputGroup>
                    </FormControlItem>
                  )
                }}
              />
            </div>
            <div className="flex flex-wrap">
              <div className="tablet:mb-0 mr-2 mb-2">
                <Controller
                  control={control}
                  name="countryStateId"
                  render={({ field: { onChange, value } }) => {
                    return (
                      <FormControlItem destructiveText={formState.errors?.countryStateId?.message}>
                        <ComboboxSelect
                          options={promiseLocationsOptions}
                          buttonClassName="text-gray-900 max-w-[248px] brand-color-select-focus"
                          buttonFontWeightClassName="font-normal"
                          dropdownMenuClassName="w-[248px]!"
                          containerMenuClassName="max-w-[248px]"
                          size="lg"
                          menuOptionAlign="start"
                          menuOptionSide="bottom"
                          placeholder={`${t('careers:searchForm:locationPlaceholder')}`}
                          searchPlaceholder={`${t('label:placeholder:search')}`}
                          loadingMessage={`${t('label:loading')}`}
                          noOptionsMessage={`${t('label:noOptions')}`}
                          onChange={async (newValue) => {
                            setUpdateFilter({
                              ...updateFilter,
                              countryStateId: newValue
                            })
                            onChange(newValue)
                            onSubmit({
                              ...data,
                              countryStateId: newValue as ISelectOption
                            })
                          }}
                          value={value}
                          destructive={formState.errors && !!formState.errors?.countryStateId}
                        />
                      </FormControlItem>
                    )
                  }}
                />
              </div>

              {!configHide?.department && (
                <div className="tablet:mb-0 mr-2 mb-2">
                  <Controller
                    control={control}
                    name="departmentIds"
                    render={({ field: { onChange, value } }) => {
                      return (
                        <FormControlItem destructiveText={formState.errors?.departmentIds?.message}>
                          <ComboboxSelect
                            options={(params) => {
                              return careerPageSetting?.department_visibility === DEPARTMENT_SHOW_TOP_LEVEL
                                ? promisePublicDepartmentOptions({
                                    ...params,
                                    tenantSlug
                                  })
                                : promisePublicDepartmentMultiLevelOptions({
                                    ...params,
                                    tenantSlug
                                  })
                            }}
                            size="lg"
                            buttonClassName="text-gray-900 max-w-[248px] brand-color-select-focus"
                            checkboxSelectedClassName="brand-color-button rounded-xs"
                            buttonFontWeightClassName="font-normal"
                            dropdownMenuClassName="w-[248px]!"
                            containerMenuClassName="max-w-[248px]"
                            menuOptionAlign="end"
                            menuOptionSide="bottom"
                            isMulti
                            onChange={(newValue) => {
                              setUpdateFilter({
                                ...updateFilter,
                                departmentIds: newValue
                              })
                              onChange(newValue)
                              onSubmit({
                                ...data,
                                departmentIds: newValue as ISelectOption[]
                              })
                              // submit && submit()
                            }}
                            placeholder={`${t('careers:searchForm:departmentPlaceholder')}`}
                            searchPlaceholder={`${t('label:placeholder:search')}`}
                            loadingMessage={`${t('label:loading')}`}
                            noOptionsMessage={`${t('label:noOptions')}`}
                            value={value}
                            destructive={formState.errors && !!formState.errors?.departmentIds}
                          />
                        </FormControlItem>
                      )
                    }}
                  />
                </div>
              )}
              {!configHide?.remoteStatus ? (
                <div className="tablet:mb-0 mr-2 mb-2">
                  <Controller
                    control={control}
                    name="remoteStatusOption"
                    render={({ field: { onChange, value } }) => {
                      return (
                        <FormControlItem destructiveText={formState.errors?.remoteStatusOption?.message}>
                          <ComboboxSelect
                            onChange={(newValue) => {
                              setUpdateFilter({
                                ...updateFilter,
                                remoteStatusOption: newValue || null
                              })
                              onChange(newValue || null)
                              onSubmit({
                                ...data,
                                remoteStatusOption: newValue as ISelectOption
                              })
                            }}
                            value={value}
                            size="lg"
                            menuOptionAlign="end"
                            menuOptionSide="bottom"
                            buttonClassName="text-gray-900 brand-color-select-focus"
                            dropdownMenuClassName="w-[248px]!"
                            containerMenuClassName="max-w-[248px]"
                            buttonFontWeightClassName="font-normal"
                            placeholder={`${t('careers:searchForm:remoteStatusPlaceholder')}`}
                            searchPlaceholder={`${t('label:placeholder:search')}`}
                            loadingMessage={`${t('label:loading')}`}
                            noOptionsMessage={`${t('label:noOptions')}`}
                            options={jobRemoteStatusOptions}
                            destructive={formState.errors && !!formState.errors?.remoteStatusOption}
                          />
                        </FormControlItem>
                      )
                    }}
                  />
                </div>
              ) : null}
              {!configHide?.jobLevel && (
                <div className="">
                  <Controller
                    control={control}
                    name="jobLevel"
                    render={({ field: { onChange, value } }) => {
                      return (
                        <FormControlItem destructiveText={formState.errors?.jobLevel?.message}>
                          <ComboboxSelect
                            onChange={(newValue) => {
                              setUpdateFilter({
                                ...updateFilter,
                                jobLevel: newValue
                              })
                              onChange(newValue || null)
                              onSubmit({
                                ...data,
                                jobLevel: newValue as ISelectOption
                              })
                            }}
                            value={value as ISelectOption}
                            size="lg"
                            menuOptionAlign="start"
                            menuOptionSide="bottom"
                            buttonClassName="text-gray-900 brand-color-select-focus"
                            dropdownMenuClassName="w-[248px]!"
                            containerMenuClassName="max-w-[248px]"
                            buttonFontWeightClassName="font-normal"
                            placeholder={`${t('careers:searchForm:jobLevelPlaceholder')}`}
                            searchPlaceholder={`${t('label:placeholder:search')}`}
                            loadingMessage={`${t('label:loading')}`}
                            noOptionsMessage={`${t('label:noOptions')}`}
                            options={jobLevelOptions}
                            destructive={formState.errors && !!formState.errors?.jobLevel}
                          />
                        </FormControlItem>
                      )
                    }}
                  />
                </div>
              )}
            </div>
          </>
        )
      }}
    </DynamicImportForm>
  )
}

export default CareerSearch
