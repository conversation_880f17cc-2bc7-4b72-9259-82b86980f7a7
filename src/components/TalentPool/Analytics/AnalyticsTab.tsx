import dynamic from 'next/dynamic'
import type { FC } from 'react'
import { useState } from 'react'
import { Trans, useTranslation } from 'react-i18next'
import type { AnyVariables, DocumentInput } from 'urql'

import type { IRouterWithID, IUserInformation } from '~/core/@types/global'
import type { IBarItem<PERSON><PERSON> } from '~/core/ui/BarChart'
import { Bar<PERSON><PERSON> } from '~/core/ui/BarChart'
import { DropdownMenu } from '~/core/ui/DropdownMenu'
import { LineChart } from '~/core/ui/LineChart'
import { Metric<PERSON>hart } from '~/core/ui/MetricChart'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { detectSearchEmpty } from '~/core/utilities/common'
import { defaultFormatDate, monthYearFormatDate } from '~/core/utilities/format-date'

import type { IReportDetailApplication, IReportDetailOpenJob } from '~/lib/features/reports/types'
import type { ISkill, TextAnchor } from '~/lib/features/settings/skills/types'
import QueryTalentPoolShowHiredApplicantsSummaryDetails from '~/lib/features/talent-pool/graphql/query-talent-pool-show-hired-jobs-summary'
import QueryTalentPoolShowJobSummaryDetail from '~/lib/features/talent-pool/graphql/query-talent-pool-show-jobs-summary'
import QueryTalentPoolShowOvertimeReport from '~/lib/features/talent-pool/graphql/query-talent-pool-show-overtime'
import QueryTalentPoolShowProfilesSummaryDetails from '~/lib/features/talent-pool/graphql/query-talent-pool-show-profiles-summary'
import QueryTalentPoolShowSummaryReport from '~/lib/features/talent-pool/graphql/query-talent-pool-show-summary'
import type { ITalentPoolReportManagementFilter } from '~/lib/features/talent-pool/types/talent-pool-type'
import {
  GROUPED_CUMULATIVE_GROWTH,
  GROUPED_SUPPLY_DEMAND_TRENDS,
  METRIC_TALENT_POOL_DATA_TYPE
} from '~/lib/features/talent-pool/utilities/enum'

import Filter from '~/components/Filter/Filter'

import AnalyticsReportModal from './AnalyticsReportModal'
import AnalyticsReportTable from './AnalyticsReportTable'
import LineOrBarChartWrapper from './LineOrBarChartWrapper'
import MetricChartWrapper from './MetricChartWrapper'

const ResponsiveLine = dynamic(() => import('@nivo/line').then((m) => m.ResponsiveLine), { ssr: false })

const AnalyticsTab: FC<{
  user: IUserInformation
  id: IRouterWithID
  filterControl: {
    value?: ITalentPoolReportManagementFilter | undefined
    onChange?: ((value?: ITalentPoolReportManagementFilter) => void) | undefined
  }
  name: string
}> = ({ user, id, filterControl, name }) => {
  const { t, i18n } = useTranslation()

  const [talentPoolDetailConfig, setTalentPoolDetailConfig] = useState<{
    open: boolean
    type?: 'application' | 'job' | 'hires'
    title?: string
    mappingData?: (dataCollection: { data: Array<any> }) => {
      data: Array<IReportDetailApplication | IReportDetailOpenJob>
    }
    query?: DocumentInput<any, AnyVariables>
    variablesFilter?: {
      stageKey?: string
      sourced?: string
      stageTypeId?: number
      fromDateTimePipelineItem?: string
      toDateTimePipelineItem?: string
    }
  }>({
    open: false
  })
  const [groupMode, setGroupMode] = useState(GROUPED_SUPPLY_DEMAND_TRENDS)
  const [filterPipelineLocal, setFilterPipelineLocal] = useState<{
    [id: string]: {
      color: string
      enable: boolean
      id: number
      bgColor: string
      hoverBgColor: string
      label: string
    }
  }>({
    jobs: {
      enable: true,
      color: 'hsl(158.76deg 55.67% 60.2%)',
      id: 1,
      bgColor: 'bg-chart-300',
      hoverBgColor: 'group-hover:bg-chart-300',
      label: `${t('talent_pool:report:overtimeReport:jobsTitle')}`
    },
    profiles: {
      enable: true,
      color: 'hsl(221.71deg 100% 67.84%)',
      id: 2,
      bgColor: 'bg-chart-100',
      hoverBgColor: 'group-hover:bg-chart-100',
      label: `${t('talent_pool:report:overtimeReport:profilesTitle')}`
    }
  })

  const isSearchEmpty = detectSearchEmpty({
    filters: filterControl.value as {
      [key: string]: unknown
    },
    filterKeys: ['dateRange']
  })

  const getTitleTooltipPipeline = (type: string, fromDate: string, toDate: string) => {
    const fromDateByTimeZone = new Date(
      new Date(fromDate).toLocaleString('en-US', {
        timeZone: user?.timezone
      })
    )
    const toDateByTimeZone = new Date(
      new Date(toDate).toLocaleString('en-US', {
        timeZone: user?.timezone
      })
    )

    if (type === 'day') {
      return defaultFormatDate(fromDateByTimeZone)
    }
    if (type === 'week') {
      return `${defaultFormatDate(fromDateByTimeZone)} - ${defaultFormatDate(toDateByTimeZone)}`
    }
    return monthYearFormatDate(fromDateByTimeZone, i18n.language)
  }

  return (
    <>
      <div className="mx-auto space-y-4 px-6 pb-4">
        <div className="w-fit max-w-[324px]">
          <Filter
            value={filterControl.value}
            onChange={(filter, name) => {
              if (name === 'dateRange') {
                const from = filter?.dateRange?.from
                const to = filter?.dateRange?.to
                if ((from && !to) || (!from && to)) {
                  return
                }
              }

              filterControl.onChange && filterControl.onChange(filter)
            }}>
            <Filter.RangeDatePicker
              menuSide="start"
              size="sm"
              name="dateRange"
              placeholder={`${t('label:placeholder:formTo')}`}
              config={{ showOutsideDays: false }}
            />
          </Filter>
        </div>

        <div className="flex items-center justify-between space-x-4">
          <MetricChartWrapper
            id={Number(id)}
            dataTypeMapping={METRIC_TALENT_POOL_DATA_TYPE.talentPoolsShowSummary}
            url={QueryTalentPoolShowSummaryReport}
            filter={filterControl.value}>
            {({ isLoading, data }) => (
              <>
                <MetricChart
                  className="flex-1"
                  title={t('talent_pool:report:statistics:profiles')}
                  helperConfig={{
                    icon: 'HelpCircle',
                    content: t('talent_pool:report:statistics:profilesHelpInfo')
                  }}
                  previousTooltip={`${t('tooltip:previousPeriod')}`}
                  dataSource={{
                    noChange: t('talent_pool:report:noChangeFromPreviousTime'),
                    isLoading,
                    data: data?.profiles
                  }}
                  onClickTotalNumber={() => {
                    setTalentPoolDetailConfig({
                      open: true,
                      type: 'application',
                      query: QueryTalentPoolShowProfilesSummaryDetails,
                      title: `${t('talent_pool:report:statistics:profiles')}`
                    })
                  }}
                />

                <MetricChart
                  className="flex-1"
                  title={t('talent_pool:report:statistics:jobs')}
                  helperConfig={{
                    icon: 'HelpCircle',
                    content: t('talent_pool:report:statistics:jobsHelpInfo')
                  }}
                  previousTooltip={`${t('tooltip:previousPeriod')}`}
                  dataSource={{
                    noChange: t('talent_pool:report:noChangeFromPreviousTime'),
                    isLoading,
                    data: data?.jobs
                  }}
                  onClickTotalNumber={() => {
                    setTalentPoolDetailConfig({
                      open: true,
                      type: 'job',
                      query: QueryTalentPoolShowJobSummaryDetail,
                      title: `${t('talent_pool:report:statistics:jobs')}`
                    })
                  }}
                />

                <MetricChart
                  className="flex-1"
                  title={t('talent_pool:report:statistics:hiredJobs')}
                  helperConfig={{
                    icon: 'HelpCircle',
                    content: t('talent_pool:report:statistics:hiredJobsHelpInfo')
                  }}
                  previousTooltip={`${t('tooltip:previousPeriod')}`}
                  dataSource={{
                    noChange: t('talent_pool:report:noChangeFromPreviousTime'),
                    isLoading,
                    data: data?.hiredJobs
                  }}
                  onClickTotalNumber={() => {
                    setTalentPoolDetailConfig({
                      open: true,
                      type: 'hires',
                      query: QueryTalentPoolShowHiredApplicantsSummaryDetails,
                      title: `${t('talent_pool:report:statistics:hiredJobs')}`
                    })
                  }}
                />
              </>
            )}
          </MetricChartWrapper>
        </div>

        <div className="min-h-[434px]">
          <LineOrBarChartWrapper
            id={Number(id)}
            groupMode={groupMode === GROUPED_SUPPLY_DEMAND_TRENDS ? 'line' : 'bar'}
            dataTypeMapping={METRIC_TALENT_POOL_DATA_TYPE.talentPoolsShowOvertimeReport}
            url={QueryTalentPoolShowOvertimeReport}
            filter={filterControl.value}>
            {({ isLoading, data }) => (
              <div className="flex min-h-[281px] flex-col rounded-md border border-solid border-gray-100">
                <div className="flex items-center justify-between space-x-1.5 px-4 pt-4">
                  <p className="text-xl font-medium text-gray-900">{t('talent_pool:report:overtimeReport:title')}</p>
                  <DropdownMenu
                    side="bottom"
                    align="end"
                    menuClassName="w-auto"
                    trigger={
                      <Tooltip
                        content={
                          groupMode === GROUPED_CUMULATIVE_GROWTH
                            ? t('talent_pool:report:overtimeReport:cumulativeGrowthInfo')
                            : t('talent_pool:report:overtimeReport:supplyDemandTrendsInfo')
                        }>
                        <TextButton
                          type="tertiary"
                          size="md"
                          underline={false}
                          iconMenus="ChevronDown"
                          icon="trailing"
                          label={
                            groupMode === GROUPED_CUMULATIVE_GROWTH
                              ? t('talent_pool:report:overtimeReport:cumulativeGrowth')
                              : t('talent_pool:report:overtimeReport:supplyDemandTrends')
                          }
                        />
                      </Tooltip>
                    }
                    showCheckedIcon
                    menu={[
                      {
                        isChecked: groupMode === GROUPED_SUPPLY_DEMAND_TRENDS,
                        label: `${t('talent_pool:report:overtimeReport:supplyDemandTrends')}`,
                        onClick: () => setGroupMode(GROUPED_SUPPLY_DEMAND_TRENDS)
                      },
                      {
                        isChecked: groupMode === GROUPED_CUMULATIVE_GROWTH,
                        label: `${t('talent_pool:report:overtimeReport:cumulativeGrowth')}`,
                        onClick: () => setGroupMode(GROUPED_CUMULATIVE_GROWTH)
                      }
                    ]}
                  />
                </div>

                {groupMode === GROUPED_CUMULATIVE_GROWTH ? (
                  <BarChart
                    dataSource={{
                      isLoading,
                      emptyData: t('talent_pool:report:empty:noDataCollect'),
                      emptySearch: t('talent_pool:report:empty:noResultsFound'),
                      data: data ? (data as unknown as Array<IBarItemChart>) : isSearchEmpty ? [] : undefined,
                      onClick: (node) => {}
                    }}
                    configBarChartProps={{
                      groupMode: 'grouped',
                      data: data || [],
                      keys: Object.keys(filterPipelineLocal)
                        .filter((key) => {
                          return filterPipelineLocal[key as keyof typeof filterPipelineLocal]?.enable
                        })
                        .map((key) => key),
                      tooltip: ({ data }) => {
                        return (
                          <div className="animate-appear invisible min-w-[148px] rounded-xs bg-white px-3 py-3 shadow-lg">
                            <span className="text-xs font-medium text-gray-700">
                              {getTitleTooltipPipeline(
                                data.groupType as string,
                                data.fromDate as string,
                                data.toDate as string
                              )}
                            </span>
                            <div>
                              {Object.keys(filterPipelineLocal)
                                .filter((key) => filterPipelineLocal[key]?.enable)
                                .map((key) => {
                                  return (
                                    <div key={key} className="mt-2 flex flex-row items-center">
                                      <div
                                        className="h-3 w-3 rounded-xs"
                                        style={{
                                          background: filterPipelineLocal[key]?.color
                                        }}
                                      />
                                      <span className="ml-2 text-xs font-normal text-gray-700">
                                        {filterPipelineLocal[key]?.label}:{' '}
                                        <span className="font-medium text-gray-900">
                                          {Number(data[key as keyof typeof data] || 0)}
                                        </span>
                                      </span>
                                    </div>
                                  )
                                })}
                            </div>
                          </div>
                        )
                      },
                      onMouseEnter: (datum, event) => {
                        event.currentTarget.style.cursor = 'pointer'
                      },
                      indexBy: 'id',
                      margin: { top: 32, right: 32, bottom: 50, left: 50 },
                      padding: 0.3,
                      colors: ({ id, data }) => String(data[`${id}Color` as keyof typeof data]),
                      axisBottom: {
                        tickSize: 5,
                        tickPadding: 10,
                        tickRotation: -15,
                        renderTick: (props) => {
                          const { tickIndex, x, y, textX, textY, textAnchor, value } = props
                          const record = (data || [])[tickIndex]
                          const label = record?.label
                          const groupType = record?.groupType
                          const rotate = record?.label?.includes('-') ? 'rotate(-15)' : 'rotate(-35)'

                          return (
                            <g transform={`translate(${x},${y})`} style={{ opacity: 1 }}>
                              <line
                                x1="0"
                                x2="0"
                                y1="0"
                                y2="5"
                                style={{
                                  stroke: 'rgb(119, 119, 119)',
                                  strokeWidth: 1
                                }}></line>
                              <text
                                textAnchor={textAnchor as TextAnchor}
                                transform={`translate(${textX + (groupType === 'day' ? 5 : 10)},${textY + 2}) ${rotate}`}
                                style={{
                                  fontFamily: 'sans-serif',
                                  fontSize: 11,
                                  fill: 'rgb(51, 51, 51)'
                                }}>
                                {label}
                              </text>
                              {record?.extraLabel && (
                                <text
                                  textAnchor={textAnchor as TextAnchor}
                                  transform={`translate(${textX + 12},${textY + 20}) ${rotate}`}
                                  style={{
                                    fontWeight: 'bold',
                                    fontFamily: 'sans-serif',
                                    fontSize: 11,
                                    fill: 'rgb(51, 51, 51)'
                                  }}>
                                  {record?.extraLabel}
                                </text>
                              )}
                            </g>
                          )
                        }
                      },
                      layers: ['grid', 'axes', 'bars'],
                      axisLeft: {
                        tickSize: 5,
                        tickPadding: 5,
                        tickRotation: 0,
                        legend: '',
                        legendPosition: 'middle',
                        legendOffset: 0
                      },
                      animate: true,
                      enableLabel: false,
                      isInteractive: true
                    }}
                    footerBarChart={
                      <div className="flex items-center space-x-8 pt-6 pr-4 pb-4 pl-[50px]">
                        {Object.keys(filterPipelineLocal).map((key) => (
                          <div key={key} className="flex items-center justify-end space-x-2">
                            <span
                              className="h-3 w-3 rounded-lg"
                              style={{
                                backgroundColor: filterPipelineLocal[key]?.color
                              }}
                            />
                            <span className="text-sm text-gray-700">
                              {filterPipelineLocal[key]?.label ===
                              t('talent_pool:report:overtimeReport:profilesTitle') ? (
                                <Trans
                                  i18nKey="talent_pool:report:overtimeReport:profilesGrowth"
                                  values={{
                                    name: String(name)
                                  }}>
                                  <span className="font-medium text-gray-900" />
                                </Trans>
                              ) : (
                                <Trans
                                  i18nKey="talent_pool:report:overtimeReport:jobsGrowth"
                                  values={{
                                    name: String(name)
                                  }}>
                                  <span className="font-medium text-gray-900" />
                                </Trans>
                              )}
                            </span>
                          </div>
                        ))}
                      </div>
                    }
                  />
                ) : null}

                {groupMode === GROUPED_SUPPLY_DEMAND_TRENDS ? (
                  <LineChart
                    component={ResponsiveLine}
                    className="border-none"
                    customClass={{
                      classNameResponsiveLine: 'px-2.5',
                      explication: 'items-start',
                      dot: 'mt-1'
                    }}
                    dataSource={{
                      isLoading,
                      emptyData: t('talent_pool:report:empty:noDataCollect'),
                      emptySearch: t('talent_pool:report:empty:noResultsFound'),
                      data: data
                        ? data?.map((item) => ({
                            ...item,
                            label:
                              item.id === t('talent_pool:report:overtimeReport:profilesTitle') ? (
                                <Trans
                                  i18nKey="talent_pool:report:overtimeReport:profiles"
                                  values={{
                                    name: String(name)
                                  }}>
                                  <span className="font-medium text-gray-900" />
                                </Trans>
                              ) : (
                                <Trans
                                  i18nKey="talent_pool:report:overtimeReport:jobs"
                                  values={{
                                    name: String(name)
                                  }}>
                                  <span className="font-medium text-gray-900" />
                                </Trans>
                              )
                          }))
                        : isSearchEmpty
                          ? []
                          : undefined
                    }}
                    configLineChartProps={{
                      data: data || [],
                      colors: (data) => data.color,
                      margin: { top: 32, right: 16, bottom: 60, left: 38 },
                      axisTop: null,
                      axisRight: null,
                      axisBottom: {
                        tickSize: 5,
                        tickPadding: 10,
                        tickRotation: -15,
                        renderTick: (props) => {
                          const { tickIndex, x, y, textX, textY, textAnchor, value } = props
                          const record = (data || [])?.[0]?.data?.[tickIndex] as {
                            label: string
                            groupType?: string
                          }
                          const label = record?.label
                          const groupType = record?.groupType
                          const rotate = record?.label?.includes('-') ? 'rotate(-15)' : 'rotate(-35)'

                          return (
                            <g transform={`translate(${x},${y})`} style={{ opacity: 1 }}>
                              <line
                                x1="0"
                                x2="0"
                                y1="0"
                                y2="5"
                                style={{
                                  stroke: 'rgb(119, 119, 119)',
                                  strokeWidth: 1
                                }}></line>
                              <text
                                textAnchor={textAnchor as TextAnchor}
                                transform={`translate(${textX + (groupType === 'day' ? 5 : 10)},${textY + 2}) ${rotate}`}
                                style={{
                                  fontFamily: 'sans-serif',
                                  fontSize: 11,
                                  fill: 'rgb(51, 51, 51)'
                                }}>
                                {label}
                              </text>
                            </g>
                          )
                        }
                      },
                      pointSize: 8,
                      pointColor: { theme: 'background' },
                      pointBorderWidth: 1.5,
                      pointBorderColor: { from: 'serieColor' },
                      // @ts-expect-error - doesn't need to fix
                      useMesh: true,
                      enableCrosshair: false,
                      enableSlices: 'x',
                      legends: []
                    }}
                  />
                ) : null}
              </div>
            )}
          </LineOrBarChartWrapper>
        </div>

        <div className="flex space-x-4">
          <AnalyticsReportTable
            tableType="profiles"
            id={id}
            filter={filterControl.value}
            title={`${t('talent_pool:report:table:candidate:title')}`}
          />
          <AnalyticsReportTable
            tableType="jobs"
            id={id}
            filter={filterControl.value}
            title={`${t('talent_pool:report:table:job:title')}`}
          />
        </div>
      </div>

      {talentPoolDetailConfig?.open ? (
        <AnalyticsReportModal
          open={talentPoolDetailConfig.open}
          setOpen={(value) =>
            setTalentPoolDetailConfig(value ? { ...talentPoolDetailConfig, open: value } : { open: false })
          }
          title={talentPoolDetailConfig?.title}
          type={talentPoolDetailConfig?.type}
          mappingData={talentPoolDetailConfig?.mappingData}
          queryReportDetail={talentPoolDetailConfig?.query}
          filterControl={{
            value: {
              id: Number(id),
              ...(filterControl?.value || {})
            },
            onChange: filterControl?.onChange
          }}
        />
      ) : null}
    </>
  )
}

export default AnalyticsTab
