import type { ReactElement } from 'react'
import { createContext, useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import withPermissionFeatureProvider from 'src/hoc/with-permission-feature'
import useEnumsData from 'src/hooks/data/use-enums-data'
import configuration from '~/configuration'
import { useGraphQLRequest } from '~/core/hooks/use-graphQL'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { Divider } from '~/core/ui/Divider'
import type { IFormAction } from '~/core/ui/Form'
import { Tabs, TabsContent, TabsList, TabsTrigger, TabsTriggerView } from '~/core/ui/Tabs'
import { cn } from '~/core/ui/utils'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import type { IDefaultPermissionsList } from '~/core/utilities/feature-permission'
import {
  ACTIONS_PERMISSIONS,
  canAccessF<PERSON>ure,
  DEFAULT_PERMISSIONS_LIST,
  PERMISSIONS_LIST
} from '~/core/utilities/feature-permission'
import { pushStateBrowser } from '~/core/utilities/is-browser'

import MutationUpdateCompanyDetail from '~/lib/features/agency/companies/graphql/mutation-update-company-detail'
import QueryCompanyActivities from '~/lib/features/agency/companies/graphql/query-company-activities'
import QueryCompanyShow from '~/lib/features/agency/companies/graphql/query-company-show'
import useCompanyInfoHook from '~/lib/features/agency/companies/hooks/company-info-hook'
import useCompanyInfoValidationHook from '~/lib/features/agency/companies/hooks/company-info-validation-hook'
import {
  formatUpdateCompanyData,
  mappingCompanyDetailData
} from '~/lib/features/agency/companies/mapping/company-detail-mapping'
import type { CompanyDetailResponseType, CompanyDetailType } from '~/lib/features/agency/companies/types/company-detail'
import {
  ACTIVITIES_TAB,
  DOMAIN_EXISTED_TYPE,
  FILES_TAB,
  JOBS_TAB,
  NOTES_TAB,
  PLACEMENTS_TAB,
  TASKS_TAB
} from '~/lib/features/agency/companies/utilities/company-detail-enum'
import { useInfinityGraphPage } from '~/lib/features/jobs/hooks/use-infinity-graph-page'
import usePermissionCompany from '~/lib/features/permissions/hooks/use-permission-company'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import { formatSubmitCustomFieldData } from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'
import useBrowserTab from '~/lib/hooks/use-browser-tab'
import useBoundStore from '~/lib/store'

import CompanyContactSection from '~/components/Companies/[id]/CompanyContactSection'
import CompanyDetailActivityTab from '~/components/Companies/[id]/CompanyDetailActivity'
import CompanyDetailFilesTab from '~/components/Companies/[id]/CompanyDetailFilesTab'
import CompanyDetailHeader from '~/components/Companies/[id]/CompanyDetailHeader'
import CompanyDetailJobTab from '~/components/Companies/[id]/CompanyDetailJobTab'
import CompanyDetailNotesTab from '~/components/Companies/[id]/CompanyDetailNotesTab'
import CompanyDetailTasksTab from '~/components/Companies/[id]/CompanyDetailTasksTab'
import CompanyInformation from '~/components/Companies/[id]/CompanyInformation'
import DirectCompanyDetailPlacementTab from '~/components/Companies/[id]/DirectCompanyDetailPlacementTab'
import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import type { ISwitchLayoutView } from '~/components/SwitchLayout/SwitchLayoutView'

export const CompaniesDetailPermissionContext = createContext<IDefaultPermissionsList>(DEFAULT_PERMISSIONS_LIST)

const CompaniesDetailContainer = ({
  isDrawer = false,
  setSwitchView,
  companyId
}: {
  isDrawer?: boolean
  setSwitchView?: (value: ISwitchLayoutView) => void
  companyId: number
}): ReactElement => {
  const { t, i18n } = useTranslation()
  const { user } = useBoundStore()
  const clientGraphQL = useGraphQLRequest({ language: user?.language })
  const companySizeOptions = useEnumsData({
    enumType: 'EmployerCompanySize',
    locale: i18n.language
  })
  const { actionCompany } = usePermissionCompany()
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()

  const isShowPlacementFeature =
    isFeatureEnabled(PLAN_FEATURE_KEYS.placement) && isUnLockFeature(PLAN_FEATURE_KEYS.placement)
  const topSpace = useClassBasedTopSpace({
    34: isDrawer ? 'h-screen' : 'h-[calc(100vh_-_34px)]',
    default: 'h-screen'
  })
  const [count, setCount] = useState<{
    jobs?: number
    notes?: number
    tasks?: number
    placements?: number
    files?: number
  }>({
    jobs: 0,
    notes: 0,
    tasks: 0,
    placements: 0,
    files: 0
  })

  const companyTabControl = useBrowserTab({
    defaultValue: ACTIVITIES_TAB,
    queryKeyName: 'tabs',
    pushState: !!isDrawer,
    excludeQueryKeysName: ['id']
  })

  const {
    data: activityList,
    hasNextPage,
    fetchNextPage,
    isLoading,
    isFetchedAfterMount,
    refetch: refetchActivityList
  } = useInfinityGraphPage({
    queryDocumentNote: useMemo(() => QueryCompanyActivities, []),
    getVariable: useCallback((page) => ({ companyId: companyId, limit: 10, page }), [companyId]),
    getPageAttribute: (_lastGroup, groups) => ({
      totalCount: _lastGroup?.companyActivitiesList?.metadata?.totalCount,
      pageLength: groups?.[0]?.companyActivitiesList?.collection?.length || 0
    }),
    queryKey: ['company-activity-list', String(companyId)],
    enabled: !!companyId
  })

  const { submitPartialField, touchingFieldRef } = useCompanyInfoValidationHook()

  const { fetchExistedCompany } = useCompanyInfoHook()
  const { data: company, trigger: fetchCompany } = useQueryGraphQL({
    query: QueryCompanyShow,
    variables: {
      id: Number(companyId)
    },
    shouldPause: true
  })

  const defaultValueCompanyDetail = useMemo(
    () =>
      !!company?.companiesShow
        ? mappingCompanyDetailData({
            data: company?.companiesShow,
            companySizeOptions
          })
        : {},

    [company, companySizeOptions]
  )
  const { canAccessNote, canAccessTask, canAccessPlacement } = usePermissionCompany()

  const onUpdateCompany = useCallback(
    (data: CompanyDetailType, formAction?: IFormAction) => {
      const submittingField = touchingFieldRef.current as keyof typeof data
      const formattedData = formatUpdateCompanyData({
        companyId,
        submittingField,
        data: {
          [submittingField]: data[submittingField]
        }
      })
      return clientGraphQL
        .mutation(MutationUpdateCompanyDetail, {
          ...formattedData,
          ...(data.customFields
            ? {
                customFields: formatSubmitCustomFieldData(data.customFields || {})
              }
            : {})
        })
        .toPromise()
        .then((result) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              formAction,
              callbackHandleStatusError422: (keys) => {
                keys.forEach((session) => {
                  if (session.message.includes(`${t('form:companyAlreadyExisted')}`)) {
                    formAction?.setError('email', {
                      message: `${t('form:contactAlreadyExisted')}`
                    })
                  }
                  if (session.field === 'domain' && session.message.includes(`${t('notification:domainExisting')}`)) {
                    fetchExistedCompany(String(data.domain)).then((companyInfo) => {
                      formAction?.setError('domain', {
                        type: DOMAIN_EXISTED_TYPE,
                        extends: {
                          existedCompanyId: Number((companyInfo as CompanyDetailResponseType)?.id)
                        },
                        message: session.message
                      })
                    })
                  } else {
                    if (session.field && formAction?.control._fields[session.field]) {
                      formAction.setError(session.field, {
                        type: 'custom',
                        message: String(session.message)
                      })
                    }
                  }
                })
              }
            })

            return false
          }

          if (result.data?.companiesUpdate.company.id) {
            fetchCompany()
            refetchActivityList && refetchActivityList()
          }

          return true
        })
    },
    [companyId, touchingFieldRef]
  )

  useEffect(() => {
    if (!!companyId) fetchCompany()
  }, [companyId])

  return (
    <CompaniesDetailPermissionContext.Provider value={actionCompany}>
      <div className={cn('flex flex-col overflow-hidden', topSpace)}>
        <div className="flex-none border-b border-b-gray-100 px-6 py-2">
          <CompanyDetailHeader
            isDrawer={isDrawer}
            defaultValue={defaultValueCompanyDetail}
            companyDetail={company?.companiesShow}
            onSubmit={onUpdateCompany}
            submitPartialField={submitPartialField}
            onCloseCompanyDrawer={() => {
              pushStateBrowser({
                state: {},
                unused: '',
                url: configuration.path.agency.companies
              })
              setSwitchView &&
                setSwitchView({
                  id: undefined,
                  view: '',
                  applicantId: undefined
                })
            }}
          />
        </div>
        <div className="flex flex-1 overflow-y-auto">
          <div className="w-[39.34%]">
            <div className="h-full overflow-y-auto px-6 pt-4 pb-10">
              <div className="mb-6">
                <CompanyContactSection
                  companyId={companyId}
                  data={company?.companiesShow?.contacts}
                  fetchCompanyProfile={fetchCompany}
                  companyName={company?.companiesShow?.permittedFields?.name?.value || ''}
                />
              </div>
              <CompanyInformation
                isDrawer={isDrawer}
                defaultValue={defaultValueCompanyDetail}
                companyDetail={company?.companiesShow}
                onSubmit={onUpdateCompany}
                submitPartialField={submitPartialField}
              />
            </div>
          </div>
          <div className="w-[60.66%] overflow-hidden border-l border-l-gray-100 pt-4">
            <Tabs
              {...companyTabControl}
              onValueChange={(value) => {
                companyTabControl.onValueChange(value)
                if (value === ACTIVITIES_TAB) {
                  refetchActivityList()
                }
              }}>
              <div className="px-6">
                <TabsList size="sm">
                  <TabsTrigger value={ACTIVITIES_TAB} size="sm" gapSize="sm">
                    <TabsTriggerView
                      size="sm"
                      session={{
                        value: ACTIVITIES_TAB,
                        label: `${t('company:activities')}`
                      }}
                    />
                  </TabsTrigger>
                  <TabsTrigger value={JOBS_TAB} size="sm" gapSize="sm">
                    <TabsTriggerView
                      size="sm"
                      session={{
                        value: JOBS_TAB,
                        label: `${t('company:jobs')}`,
                        count: count?.jobs
                      }}
                    />
                  </TabsTrigger>
                  {canAccessNote ? (
                    <TabsTrigger value={NOTES_TAB} size="sm" gapSize="sm">
                      <TabsTriggerView
                        size="sm"
                        session={{
                          value: NOTES_TAB,
                          label: `${t('company:notes')}`,
                          count: count?.notes
                        }}
                      />
                    </TabsTrigger>
                  ) : null}
                  {canAccessTask ? (
                    <TabsTrigger value={TASKS_TAB} size="sm" gapSize="sm">
                      <TabsTriggerView
                        size="sm"
                        session={{
                          value: TASKS_TAB,
                          label: `${t('company:tasks')}`,
                          count: count.tasks
                        }}
                      />
                    </TabsTrigger>
                  ) : null}
                  {isShowPlacementFeature && canAccessPlacement ? (
                    <TabsTrigger value={PLACEMENTS_TAB} size="sm" gapSize="sm">
                      <TabsTriggerView
                        size="sm"
                        session={{
                          value: PLACEMENTS_TAB,
                          label: `${t('company:placements')}`,
                          count: count.placements
                        }}
                      />
                    </TabsTrigger>
                  ) : null}
                  <TabsTrigger value={FILES_TAB} size="sm" gapSize="sm">
                    <TabsTriggerView
                      size="sm"
                      session={{
                        value: FILES_TAB,
                        label: `${t('company:files')}`,
                        count: count.files
                      }}
                    />
                  </TabsTrigger>
                </TabsList>
              </div>
              <Divider />
              <TabsContent value={ACTIVITIES_TAB} className="mt-0 pt-4">
                <CompanyDetailActivityTab
                  isDrawer={isDrawer}
                  data={activityList}
                  fetchNextPage={fetchNextPage}
                  hasNextPage={hasNextPage}
                  isLoading={isLoading}
                  isFetchedAfterMount={isFetchedAfterMount}
                />
              </TabsContent>
              <TabsContent value={JOBS_TAB} className="mt-0 pt-3">
                {company?.companiesShow ? (
                  <CompanyDetailJobTab isDrawer={isDrawer} setCount={setCount} companyDetail={company.companiesShow} />
                ) : null}
              </TabsContent>
              {canAccessNote ? (
                <TabsContent value={NOTES_TAB} className="mt-0 pt-3">
                  <CompanyDetailNotesTab isDrawer={isDrawer} companyId={companyId} setCount={setCount} />
                </TabsContent>
              ) : null}
              {canAccessTask ? (
                <TabsContent value={TASKS_TAB} className="mt-0 pt-3">
                  <CompanyDetailTasksTab isDrawer={isDrawer} companyId={companyId} setCount={setCount} />
                </TabsContent>
              ) : null}
              {isShowPlacementFeature && canAccessPlacement ? (
                <TabsContent value={PLACEMENTS_TAB}>
                  <DirectCompanyDetailPlacementTab companyId={companyId} setCount={setCount} />
                </TabsContent>
              ) : null}
              <TabsContent value={FILES_TAB}>
                <CompanyDetailFilesTab companyId={companyId} setCount={setCount} />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </CompaniesDetailPermissionContext.Provider>
  )
}

export default withPermissionFeatureProvider(
  {
    checkAccessPermission: canAccessFeature,
    keyModule: [PERMISSIONS_LIST.company_management.keyModule],
    keyModuleObject: [PERMISSIONS_LIST.company_management.objects.company.keyModuleObject],
    action: ACTIONS_PERMISSIONS.show
  },
  CompaniesDetailContainer
)
