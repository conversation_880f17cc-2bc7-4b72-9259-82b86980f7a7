import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

const i18nConfig = {
  fallbackLng: 'en',
  debug: process.env.NODE_ENV === 'development',
  interpolation: {
    escapeValue: false
  },
  react: {
    useSuspense: false
  },
  backend: {
    loadPath: '/locales/{{lng}}/{{ns}}.json'
  }
}

export const initI18n = () => {
  i18n.use(initReactI18next).init(i18nConfig)

  return i18n
}

export default i18nConfig
