import type { CountryData } from 'react-phone-input-2'

import type { ILogoAndAvatarVariants, IRouterWithID, IUserInformation } from '~/core/@types/global'
import type { CLOUD, LOCAL, RESUME } from '~/core/constants/enum'
import type { IAttachmentsFile } from '~/core/ui/RichEditor'
import { IAttachmentsFiles } from '~/core/ui/RichEditor'
import type { ISelectOption } from '~/core/ui/Select'
import type { DatePickerValue } from '~/core/ui/SingleDateWithYearOnlyPicker'

import type { FieldSettingType } from '~/components/DisplayConfig'
import type { IPlacement } from '~/features/placements/placement'

import type { ICompanyPermittedFields } from '../../agency/companies/types'
import { CompanyDetailType } from '../../agency/companies/types/company-detail'
import type { CompanyDetailResponseType } from '../../agency/companies/types/company-detail'
import { IContactPermittedFields } from '../../agency/companies/types/company-detail'
import type { IApplicableJobs } from '../../calendar/types'
import { InterviewParamsType } from '../../calendar/types'
import { IMemberInfo } from '../../jobs/graphql/query-job-detail-mini'
import type { IJobStage, IJobStages } from '../../jobs/types'
import type { CustomFieldFormType, CustomFieldResponseItem } from '../../settings/profile-fields/types/custom-field'
import { ITags } from '../../settings/tags/types'

export type IPermittedFieldsName =
  | 'birthday'
  | 'totalYearsOfExp'
  | 'profileLevel'
  | 'expectedSalary'
  | 'expectedSalaryCurrency'
  | 'expectedSalaryInUsd'
  | 'currentSalary'
  | 'currentSalaryCurrency'
  | 'currentSalaryInUsd'
  | 'languages'
  | 'skills'
  | 'language'
  | 'nationality'
  | 'noticeToPeriodDays'
  | 'summary'
  | 'willingToRelocate'
  | 'openToWork'
  | 'workExperiences'
  | 'references'
  | 'certificates'
  | 'educations'
  | 'headline'
  | 'email'
  | 'fullName'
  | 'phoneNumber'
  | 'location'
  | 'links'
  | 'profileCvs'
  | 'avatar'
  | 'talentPools'
  | 'publicId'
  | 'departments'
  | 'preferredWorkState'
export interface IPermittedFields {
  birthday: {
    value?: {
      birth_date?: number
      birth_year?: number
      birth_month?: number
      year?: number
      month?: number
      date?: number
    }
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  totalYearsOfExp: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  profileLevel: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  expectedSalary: {
    value?: number
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  expectedSalaryCurrency: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  expectedSalaryInUsd: {
    value?: number
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  typeOfExpectedSalary: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  typeOfCurrentSalary: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  currentSalary: {
    value?: number
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  currentSalaryCurrency: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  currentSalaryInUsd: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  languages: {
    value?: LanguagesType[]
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  skills: {
    value?: string[]
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  language: {
    value?: string[]
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  nationality: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  noticeToPeriodDays: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  summary: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  willingToRelocate: {
    value?: boolean
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  openToWork: {
    value?: boolean
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  workExperiences: {
    value?: WorkExperiencesType[]
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  references: {
    value?: ReferencesType[]
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  certificates: {
    value?: CertificatesType[]
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  educations: {
    value?: EducationsType[]
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  headline: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  email: {
    value?: string[]
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  fullName: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  phoneNumber: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  location: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  links: {
    value?: { [key: string]: string[] }
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  profileCvs: {
    value?: profileCvsProps
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  avatar: {
    value?: ILogoAndAvatarVariants
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  talentPools: {
    value?: Array<TalentPoolType>
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  publicId: {
    value?: string
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  departments: {
    value?: { departments: string[]; all_departments: boolean }
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }

  preferredWorkStates: {
    value?: Array<PreferredWorkStatesType>
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
}

export interface AddCandidateFormType {
  fullName?: string
  email?: string
  phoneNumber?: string
  resumeFile?: File[]
  jobId?: Array<ISelectOption>
  profileTalentPoolIds?: Array<ISelectOption>
  countryCode?: CountryData
  links?: {
    links: Array<string>
    _destroy: boolean
  }
}

export interface IInfoRejectCandidate {
  id?: IRouterWithID
  email?: string[]
  fullName?: string
}

export interface IRejectCandidateForm {
  id: string
  rejectedReason: ISelectOption
  emailTemplate?: ISelectOption & { body: string; subject: string }
  sendRejectEmail?: boolean
  subject?: string
  status: string
  to?: ISelectOption | ISelectOption[]
  cc: { value: string }[]
  bcc: { value: string }[]
  htmlBody?: string
  emailTemplateId?: number
}

export interface JobType {
  id: number
  title: string
}

export interface IProfileListType {
  id?: number | string
  employeeId?: number
  defaultAccessibleApplicantId?: number
  recommendationMatchedFields?: {
    field: string
    total_rate: number
    total_rate_string: string
  }[]
  fullName?: string
  email?: string
  phoneNumber?: string
  avatarVariants?: ILogoAndAvatarVariants
  createdAt?: string
  coverLetter?: string
  updatedAt?: string
  applicants?: Array<{
    id?: number
    job?: {
      id?: number
      title?: string
      status: string
      statusDescription: string
      currentUserAccessible: boolean
      permittedFields?: {
        [key: string]: {
          role_changeable?: boolean
          visibility_changeable?: boolean
          roles?: Array<string>
          value?: string & { name?: string; id?: string }
        }
      }
      company?: {
        permittedFields?: ICompanyPermittedFields
      }
    }
    jobStage?: { stageLabel?: string; stageTypeId?: number }
    flagNew: boolean
    rejectedReasonLabel: string
    status: string
  }>
  headline: string
  links?: {
    [key: string]: Array<string>
  }
  location: string
  tags: Array<{
    name: string
  }>
  owner: {
    id: string
    fullName: string
    avatarVariants?: ILogoAndAvatarVariants
    defaultColour: string
  }
  profileCvs: Array<{
    attachments: Array<{
      file: string
      blobs: {
        filename: string
      }
    }>
  }>
  permittedFields?: IPermittedFields
  customFields?: CustomFieldResponseItem[]
  defaultColour?: string
}

export interface ICandidatesFilter {
  sorting?: {
    created_at?: 'desc' | 'asc'
  }
  page?: number
  search?: string
  key?: string
  isSubmitCount?: number
  isFilterTouched?: boolean
  operator?: string
  job_id?: ISelectOption
  applicant_statuses?: ISelectOption[]
  stage_type_id?: ISelectOption
  department_ids?: ISelectOption[]
  preferred_work_state_ids?: ISelectOption[]
  tagged_ids?: ISelectOption[]
  company_id?: ISelectOption
  owner_id?: ISelectOption[]
  sourced?: ISelectOption
  profile_talent_pool_ids?: ISelectOption[]
  country_state_id?: ISelectOption
  profile_level?: ISelectOption
  created_at?: {
    from?: string
    to?: string
  }
  updated_at?: {
    from?: string
    to?: string
  }
  profile_cvs_empty?: ISelectOption
  full_name?: string
  public_id?: string
  email?: string
  phone_number?: string
  links?: string
  summary?: string
  headline?: string
  birthday?: DatePickerValue
  open_to_work?: ISelectOption
  skills?: ISelectOption[]
  languages?: ISelectOption[]
  willing_to_relocate?: ISelectOption
  notice_to_period_days?: ISelectOption
  total_years_of_exp?: ISelectOption
  nationality?: string
  expected_salary?: string
  current_salary?: string
  educations?: string
  work_experiences?: string
  fieldsFilter?: {
    id?: string
    field?: string
    direction?: string
    directions?: Array<string>
  }[]
}

export interface ICandidateApplicant {
  id: string
  jobId?: string
  profileId?: number
  status: string
  statusDescription?: string
  rejectedReasonLabel?: string
  createdAt?: string | Date
  job?: {
    id: number
    title: string
    pitch: string
    description: string
    owner: {
      email: string
      fullName: string
    }
  } & IJobInfoType
  createdBy: {
    email: string
    fullName: string
    avatarVariants?: ILogoAndAvatarVariants
    defaultColour?: string
  }
  incoming?: string
  jobStage: IJobStage
  currentStagedDate?: string
  tagLabel?: string
  profile?: ICandidateProfile
  jobIkitEvaluation?: {
    id: number
    name: string
    note: string
    overallFeedback: string
    updatedAt: string
    status: string
    statusDescription?: string
    jobStages: {
      stageTypeId: string
      stageLabel: string
    }[]
    user: {
      id: string
      defaultColour: string
      fullName: string
      avatarVariants?: ILogoAndAvatarVariants
    }
  }
  hiredBy?: {
    id: string
    avatarVariants: ILogoAndAvatarVariants
    fullName: string
    email: string
    defaultColour: string
  }
  hiredDate?: string
  onboardDate?: string
  timeToHire?: number
  referral?: {
    user: {
      avatarVariants: ILogoAndAvatarVariants
      fullName: string
      defaultColour: string
    }
  }
  placement?: IPlacement
}
export interface CreateProfileNoteInput {
  commentableId?: number
  parentId?: number
  content?: string
  sharedUserIds?: Array<ISelectOption>
  attachments?: Array<{
    id: string
    blobs: {
      filename: string
      content_type: string
      size: number
    }
    file: string
  }>
}

export type UpdateProfileNoteInput = {
  id: number
  content: string
  sharedUserIds?: Array<ISelectOption>
}
export type DeleteProfileNoteInput = {
  id: number
}
export type ProfileSourcedType = {
  job_board: string
  career_page: string
  referral: string
  sourced: string
  social: string
}
export type LanguagesType = {
  index?: number
  language?: string
  languageDescription?: string
  proficiency?: string
  proficiencyDescription?: string
  _destroy?: boolean
}
export type WorkExperiencesType = {
  company?: string
  createdAt?: string
  currentWorking?: boolean
  description?: string
  from?: string
  fromMonth?: number
  fromYear?: number
  id: number
  location?: string
  countryStateId?: string
  position?: string
  profileId: number
  tenantId?: number
  title?: string
  to?: string
  toMonth?: number
  toYear?: number
  updatedAt?: string
}
export type WorkExperienceParamType = {
  company?: string
  createdAt?: string
  currentWorking?: boolean
  description?: string
  from?: string
  fromMonth?: string
  fromYear?: string
  id: number
  location?: ISelectOption
  position?: string
  profileId: number
  tenantId?: number
  title?: string
  to?: string
  toMonth?: string
  toYear?: string
  updatedAt?: string
}
export type ReferencesType = {
  index?: number
  id?: string
  email?: string
  name?: string
  _destroy?: boolean
}
export type CertificatesType = {
  id?: string
  institution?: string
  issueMonth?: string
  issueYear?: string
  certificateName?: string
  position?: number
  _destroy?: boolean
}
export type EducationsType = {
  createdAt?: string
  degree?: string
  degreeSubject?: string
  description?: string
  from?: string
  id: number
  position?: string
  profileId: number
  schoolName?: string
  tenantId?: number
  to?: string
  updatedAt?: string
}
export type EducationParamType = {
  createdAt?: string
  degree?: string
  degreeSubject?: string
  description?: string
  from?: {
    month?: string
    year?: string
  }
  id: number
  position?: string
  profileId: number
  schoolName?: string
  tenantId?: number
  to?: {
    month?: string
    year?: string
  }
  updatedAt?: string
}
export type SkillsType = {
  name: string
  id: string
  similar?: Array<string>
}
export type profileCvsProps = Array<{
  id: string
  attachments?: Array<{
    id: string
    file: string
    blobs: { content_type: string; size: number; filename: string }
  }>
}>

export type ICandidateProfile = {
  applicantId?: number
  employeeId?: number
  id?: number
  createdAt?: string
  fullName?: string
  phoneNumber?: string
  email?: string[] | string
  resumeFile?: Array<File>
  avatarObject?: object
  avatarVariants?: ILogoAndAvatarVariants
  coverLetter?: string
  headline?: string
  location?: string
  countryStateId?: string
  sourced?: string
  ownerId?: ISelectOption
  links?: {
    [key: string]: Array<string>
  }
  pendingInterviewFeedbacks?: [
    {
      id: number
      fromDatetime: string
      timezone: string
      ikitFeedbacksSummary: Array<Array<string | number>>
    }
  ]
  job?: {
    id?: number
    title?: string
    permittedFields?: {
      [key: string]: {
        role_changeable?: boolean
        visibility_changeable?: boolean
        roles?: Array<string>
        value?: string & { name?: string; id?: number }
      }
    }
    status?: string
  }
  accountManagers?: IUserInformation[]
  jobStages?: IJobStages
  overallFeedbacksCount?: any
  references?: Array
  certificates?: Array
  workExperiences?: Array
  educations?: Array
  applicants?: Array<IApplicableJobs>
  profileCvs?: profileCvsProps
  sourcedDescription?: string
  sourcedNameDescription?: string
  user?: {
    id?: number
    fullName?: string
    avatarVariants?: ILogoAndAvatarVariants
    defaultColour?: string
    email?: string
  }
  totalYearsOfExp?: string
  owner?: {
    id?: string
    fullName?: string
    avatarVariants?: ILogoAndAvatarVariants
    defaultColour?: string
  }
  currentSalaryInUsd?: string
  tags?: Array
  permittedFields?: IPermittedFields
  warning?: Array<{ short?: string; full?: string }>
  defaultAccessibleApplicantId?: number
  talentPools?: Array<TalentPoolType>
  placement?: IPlacement
  company?: CompanyDetailResponseType
} & {
  customFields?: CustomFieldResponseItem[]
  avatarBase64?: string
  avatarRoundedBase64?: string
  logoBase64?: string
  locationWithStateID?: ISelectOption
}

export type Departments = {
  departments?: Array<Department>
  all_departments?: boolean
}

export type Department = {
  id?: string
  name?: string
  parent_id?: string
}

export type TalentPoolType = {
  name: string
  id: string
  createdBy?: {
    fullName?: string
    email?: string
  }
}

export type PreferredWorkStatesType = {
  full_name?: string
  id: string
}

export type CandidateProfileInputType = {
  id?: number
  fullName?: string
  phoneNumber?: string
  email?: string[] | string
  resumeFile?: Array<File>
  avatarObject?: object
  avatarVariants?: ILogoAndAvatarVariants
  headline?: string
  location?: string
  locationWithStateID?: ISelectOption
  countryStateId?: string
  sourced?: string
  links?: {
    [key: string]: Array<string>
  }
  references?: Array
  certificates?: Array
  workExperiences?: Array
  educations?: Array
  applicants?: Array
  birthday?: DatePickerValue
  typeOfExpectedSalary?: string
  typeOfCurrentSalary?: string
  expectedSalary?: number
  expectedSalaryCurrency?: string
  currentSalary?: number
  currentSalaryCurrency?: string
  expectedSalaryInUsd?: string
  currentSalaryInUsd?: string
  languages?: LanguagesType[]
  skills?: string[]
  jobs?: ISelectOption[]
  nationality?: string
  noticeToPeriodDays?: string
  summary?: string
  willingToRelocate?: boolean
  openToWork?: boolean
  sourcedName?: string
  sourcedNameDescription?: string
  ownerId?: ISelectOption
  totalYearsOfExp?: string
  profileLevel?: string
  tags?: Array
  profileTalentPoolIds?: Array<string | number>
  preferredWorkStateIds?: Array<string | number>
  addActionLogsRelatedToAi?: {
    summaryAiRegenerated?: boolean
    summaryAiGenerated?: boolean
  }
} & CustomFieldFormType

export type ITemplateType = ['event' | 'general' | 'auto_confirmation']
export type IDisabledFieldsEmailForm = 'to' | 'cc' | 'bcc' | 'subject' | 'htmlBody' | string[]
export interface FilterHiringMembersListType {
  search?: string
  page?: number
  limit?: number
  profileId?: number
}
export interface HiringMembersType {
  id: number
  fullName: string
  email?: string
  avatarVariants?: ILogoAndAvatarVariants
  defaultColour: string
  roles?: Array<{
    id?: number
    name?: string
  }>
}
export interface NoteFormInActiveType {
  type?: 'edit' | 'add'
  noteId?: number
}
export interface NoteFormType {
  content: string
  sharedUsers: ISelectOption[]
  withFollowUpTask?: boolean
  dueDate?: string
}
export interface NoteItemType {
  type?: 'job' | 'profile'
}

export interface SummaryReferralType {
  id: number
  introduction: string
  user: {
    avatarVariants?: ILogoAndAvatarVariants
    fullName: string
    email: string
    defaultColour: string
    roles: Array<{
      name: string
    }>
  }
  job: {
    id: number
    title: string
    tenant: {
      slug: string
    }
  }
  applicant: {
    createdAt: string
  }
}

export type ITenantUser = {
  email: string
  avatarVariants?: ILogoAndAvatarVariants
  fullName: string
  roles: Array<{ name: string }>
  defaultColour: string
}
export interface SourceListType {
  id: number
  key: string
  name: string
}
export interface IPromiseSourceListOption {
  search?: string
  page?: number
  limit?: number
  sourcedName?: string
}
export interface IRejectReasonGroup {
  value: string
  label: string
  options: ISelectOption[]
}

export interface ISendToClientForm {
  to?: Array<ISelectOption>
  cc?: Array<ISelectOption>
  bcc?: Array<ISelectOption>
  htmlBody?: string
  subject?: string
  files?: Array<IAttachmentsFile & { profileId?: number }>
  profileId?: number
  optionsToSearch?: ISelectOption[]
  moveStage?: boolean
}
export interface IJobIkitEvaluations {
  id: string
  job: {
    id: string
    title: string
  }
  overallFeedback: string
  overallFeedbackDescription: string
  note: string
  createdAt: string
  updatedAt: string
  user: {
    id: string
    fullName: string
    avatar: ILogoAndAvatarVariants
    defaultColour: string
  }
  status: string
  interview: {
    id: string
    eventType: string
    eventTypeDescription: string
  }
}
export type IJobIkitEvaluationsList = Array<{
  overallFeedbacksCount?: { [key: string]: string }
  jobIkitEvaluations: IJobIkitEvaluations[]
  key: string
  stageLabel: string
  stageTypeId: number
}>
export interface IAIWriterForm {
  profileId: number
  skills: Array<ISelectOption>
  headline?: string
  summary?: string
  language?: string
}
export type IUploadDrawerJob = Array<{
  defaultValue?: boolean
  profileId?: string
  jobId?: string
  jobTitle?: string
}>

export interface ISource {
  label?: string
  attachments?: Array<IAttachmentsFile>
  onGetData: () => Promise<IPagination>
  type?: typeof LOCAL | typeof CLOUD | typeof RESUME
  loading?: boolean
}

export interface IPagination {
  data: Array<IAttachmentsFile>
  meta?: {
    totalRowCount?: number
    currentPage?: number
  }
}

export interface IProfileFiltersList {
  direction?: Array<string>
  field_name: string
  field_type: string
  field_kind?: string
  object?: string
  id?: number
}

export interface IProfileFilters {
  fieldType: string
  fieldKind?: string
  object?: string
  direction?: string
  value: unknown
  id?: number
}

export interface IProfileViewDisplay {
  id?: string | number
  group?: string
  createdById?: number
  name?: string
  state?: string
  stateDescription?: string
  createdById?: string
  profileDisplay?: FieldSettingType[]
  profileFilters?: {
    fieldsFilter?: IProfileFilters[]
    search?: string
    operator?: string
    relatedObjects?: any
  }
  profileColumnsIsDefault?: boolean
  isFEDeleted?: boolean // FE only
}

export interface CallLogsFormType {
  callTo?: ISelectOption
  phoneNumber: string
  callTime?: Date
  phoneNumber: string
  direction?: ISelectOption
  outCome?: ISelectOption
  notes?: string
}

export type CareerGoalType = {
  id: string
  skills: Array<string>
  skillRecords?: Record<string, string>
  skillsYouNeed?: Record<string, string>
  position: PositionType
}
