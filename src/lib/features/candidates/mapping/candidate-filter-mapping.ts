import { endOfDay, startOfDay } from 'date-fns'

import type { IUserInformation } from '~/core/@types/global'
import type { ISelectOption } from '~/core/ui/Select'
import { formatDatePickerToDate } from '~/core/ui/SingleDateWithYearOnlyPicker'

import { OPERATOR } from '~/lib/features/candidates/utilities/enum'

import { addTzToDate } from '../../calendar/utilities/helper-schedule-interview'
import { mappingTypeToFieldKind } from '../../settings/profile-fields/mapping/custom-field-mapping'
import type { ICandidatesFilter, IProfileFilters } from '../types'
import { CANDIDATE_YES_NO, FILTER_CANDIDATE_FIELDS_CONDITION, FILTER_CANDIDATE_FIELDS_VALUE } from '../utilities/enum'

type IValueSearch = ISelectOption | ISelectOption[] | { from: string; to: string } | string | number

export const mappingAdvancedFilterCandidates = (pageParam: ICandidatesFilter, user?: IUserInformation) => {
  const {
    key, // don't need use this field
    isSubmitCount, // don't need use this field
    isFilterTouched, // don't need use this field
    page,
    search,
    operator,
    sorting,

    job_id,
    applicant_statuses,
    stage_type_id,
    tagged_ids,
    company_id,
    owner_id,
    sourced,
    profile_talent_pool_ids,
    preferred_work_state_ids,
    department_ids,
    country_state_id,
    profile_level,
    created_at,
    updated_at,
    profile_cvs_empty,
    full_name,
    public_id,
    email,
    phone_number,
    links,
    summary,
    headline,
    birthday,
    open_to_work,
    skills,
    languages,
    willing_to_relocate,
    notice_to_period_days,
    total_years_of_exp,
    nationality,
    expected_salary,
    current_salary,
    fieldsFilter,
    educations,
    work_experiences,
    ...restCustomField
  } = pageParam
  const timeZone = user?.timezone || 'Asia/Saigon'
  const arrFieldsFilter: Array<IProfileFilters> = []
  let relatedObjects: any

  const formatDateValue = ({ value }: { value: IValueSearch }) => {
    const formatRangeSelection = value as {
      from?: string
      to?: string
    }
    if (formatRangeSelection.from && formatRangeSelection.to) {
      return `${addTzToDate(String(startOfDay(new Date(formatRangeSelection?.from))), timeZone)} - ${addTzToDate(
        String(endOfDay(new Date(formatRangeSelection?.to))),
        timeZone
      )}`
    }

    const formatSelection = value as {
      year?: number
      month?: number
      date?: number
    }

    if (formatSelection.year && !formatSelection.month && !formatSelection.date) {
      return formatSelection.year
    }

    if (!formatSelection.year && !formatSelection.month && !formatSelection.date) {
      if (Object.keys(formatSelection).length === 0) {
        return addTzToDate(String(startOfDay(new Date(formatSelection as string))), timeZone)
      }

      return ''
    }

    return addTzToDate(
      String(
        startOfDay(
          formatDatePickerToDate({
            year: formatSelection?.year,
            month: formatSelection?.month,
            date: formatSelection?.date
          })
        )
      ),
      timeZone
    )
  }

  const formattedValue = ({
    key,
    customFieldKind,
    value
  }: {
    key: string
    customFieldKind?: string
    value: IValueSearch
  }) => {
    if (key === 'custom_field') {
      if (customFieldKind === 'toggle') {
        const formatSelection = value as ISelectOption
        return formatSelection.value === CANDIDATE_YES_NO[0]?.value ? true : false
      }

      if (customFieldKind === 'select') {
        const formatSelection = value as ISelectOption
        return [String(formatSelection.value)]
      }

      if (customFieldKind === 'date') {
        return formatDateValue({ value })
      }

      if (customFieldKind === 'number') {
        return value
      }

      if (isFinite(Number(value))) {
        return Number(value)
      }

      if (customFieldKind === 'multiple') {
        const formatSelection = value as ISelectOption[]
        return formatSelection.map((val) => val.value)
      }

      return value
    }

    if (
      [
        FILTER_CANDIDATE_FIELDS_VALUE.created_at,
        FILTER_CANDIDATE_FIELDS_VALUE.updated_at,
        FILTER_CANDIDATE_FIELDS_VALUE.birthday
      ].includes(key)
    ) {
      return formatDateValue({ value })
    }

    if (key === FILTER_CANDIDATE_FIELDS_VALUE.phone_number) {
      return value
    }

    if (typeof value === 'object') {
      if (Array.isArray(value)) {
        const formatSelection = value as ISelectOption[]

        return (
          formatSelection.map((item) => {
            if ([CANDIDATE_YES_NO[0]?.value, CANDIDATE_YES_NO[1]?.value].includes(String(item.value))) {
              return item.value === CANDIDATE_YES_NO[0]?.value ? true : false
            }

            if (isFinite(Number(item.value))) {
              return Number(item.value)
            }

            return String(item.value)
          }) || undefined
        )
      } else {
        const formatSelection = value as ISelectOption
        if ([CANDIDATE_YES_NO[0]?.value, CANDIDATE_YES_NO[1]?.value].includes(String(formatSelection.value))) {
          return formatSelection.value === CANDIDATE_YES_NO[0]?.value ? true : false
        }

        if (isFinite(Number(formatSelection.value))) {
          return Number(formatSelection.value)
        }

        return String(formatSelection.value)
      }
    }

    if ([CANDIDATE_YES_NO[0]?.value, CANDIDATE_YES_NO[1]?.value].includes(String(value))) {
      return value === CANDIDATE_YES_NO[0]?.value ? true : false
    }

    if (isFinite(Number(value))) {
      return Number(value)
    }

    return value
  }
  ;(fieldsFilter || []).forEach((fieldFilter) => {
    const key = fieldFilter.field as keyof ICandidatesFilter
    const valueSearch = pageParam[key]

    if (fieldFilter?.id) {
      if (
        [FILTER_CANDIDATE_FIELDS_CONDITION.isEmpty, FILTER_CANDIDATE_FIELDS_CONDITION.isNotEmpty].includes(
          String(fieldFilter?.direction)
        )
      ) {
        arrFieldsFilter.push({
          fieldType: String(fieldFilter.id),
          object: fieldFilter?.field,
          direction: fieldFilter?.direction,
          value: undefined,
          id: undefined
        })
      } else if (valueSearch) {
        if (
          [
            FILTER_CANDIDATE_FIELDS_VALUE.created_at,
            FILTER_CANDIDATE_FIELDS_VALUE.updated_at,
            FILTER_CANDIDATE_FIELDS_VALUE.birthday
          ].includes(key)
        ) {
          const value = formatDateValue({
            value: valueSearch as IValueSearch
          })

          if (value) {
            arrFieldsFilter.push({
              fieldType: String(fieldFilter.id),
              object: fieldFilter?.field,
              direction: fieldFilter?.direction,
              value: formattedValue({
                key: String(fieldFilter?.field),
                customFieldKind: '',
                value: valueSearch as IValueSearch
              }),
              id: undefined
            })
          }
        } else {
          const formatValue = formattedValue({
            key: String(fieldFilter?.field),
            customFieldKind: '',
            value: valueSearch as IValueSearch
          })
          if (Array.isArray(formatValue)) {
            if (formatValue.length) {
              arrFieldsFilter.push({
                fieldType: String(fieldFilter.id),
                object: fieldFilter?.field,
                direction: fieldFilter?.direction,
                value: formatValue,
                id: undefined
              })
            }
          } else {
            arrFieldsFilter.push({
              fieldType: String(fieldFilter.id),
              object: fieldFilter?.field,
              direction: fieldFilter?.direction,
              value: formatValue,
              id: undefined
            })
          }
        }
      }

      relatedObjects = {
        ...relatedObjects,
        [String(fieldFilter?.field)]: valueSearch
      }
    }
  })

  if (Object.keys(restCustomField).length) {
    Object.keys(restCustomField).forEach((key) => {
      const valueSearch = pageParam[key as keyof ICandidatesFilter]
      const findElement = fieldsFilter?.find((item) => item.field === key)
      const splitCustomField = findElement?.id?.split('_')
      const customFieldKind = splitCustomField?.[0]
      const customFieldId = splitCustomField?.[1]

      if (customFieldId && valueSearch) {
        if (
          ![FILTER_CANDIDATE_FIELDS_CONDITION.isEmpty, FILTER_CANDIDATE_FIELDS_CONDITION.isNotEmpty].includes(
            String(findElement?.direction)
          )
        ) {
          if (customFieldKind === 'date') {
            const value = formatDateValue({
              value: valueSearch as IValueSearch
            })

            if (value) {
              arrFieldsFilter.push({
                fieldType: 'custom_field',
                fieldKind: mappingTypeToFieldKind(String(customFieldKind)),
                object: '',
                direction: findElement?.direction,
                value: formattedValue({
                  key: 'custom_field',
                  customFieldKind,
                  value: valueSearch as IValueSearch
                }),
                id: Number(customFieldId)
              })
            }
          } else {
            const formatValue = formattedValue({
              key: 'custom_field',
              customFieldKind,
              value: valueSearch as IValueSearch
            })
            if (Array.isArray(formatValue)) {
              if (formatValue.length) {
                arrFieldsFilter.push({
                  fieldType: 'custom_field',
                  fieldKind: mappingTypeToFieldKind(String(customFieldKind)),
                  object: '',
                  direction: findElement?.direction,
                  value: formatValue,
                  id: Number(customFieldId)
                })
              }
            } else {
              arrFieldsFilter.push({
                fieldType: 'custom_field',
                fieldKind: mappingTypeToFieldKind(String(customFieldKind)),
                object: '',
                direction: findElement?.direction,
                value: formatValue,
                id: Number(customFieldId)
              })
            }
          }

          relatedObjects = {
            ...relatedObjects,
            [String(findElement?.field)]: valueSearch
          }
        }
      }
    })
  }

  if (fieldsFilter?.length) {
    const filterCustomFields = fieldsFilter.filter(
      (item) => !['default_field', 'system_field'].includes(String(item.id))
    )
    if (filterCustomFields.length) {
      filterCustomFields.forEach((field) => {
        const splitCustomField = field?.id?.split('_')
        const customFieldKind = splitCustomField?.[0]
        const customFieldId = splitCustomField?.[1]

        if (customFieldId) {
          if (
            [FILTER_CANDIDATE_FIELDS_CONDITION.isEmpty, FILTER_CANDIDATE_FIELDS_CONDITION.isNotEmpty].includes(
              String(field?.direction)
            )
          ) {
            arrFieldsFilter.push({
              fieldType: 'custom_field',
              fieldKind: mappingTypeToFieldKind(String(customFieldKind)),
              object: '',
              direction: field?.direction,
              value: undefined,
              id: Number(customFieldId)
            })

            relatedObjects = {
              ...relatedObjects,
              [String(field?.field)]: undefined
            }
          }
        }
      })
    }
  }

  return {
    operator: operator || OPERATOR.and,
    search,
    sorting,
    basedOnApplicant: job_id?.value !== undefined || !!stage_type_id?.value || (applicant_statuses || [])?.length > 0,
    fieldsFilter: arrFieldsFilter,
    relatedObjects: relatedObjects
  }
}
