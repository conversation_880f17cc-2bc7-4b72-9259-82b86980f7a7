import { gql } from 'urql'

const QueryCandidateApplicantDetail = gql`
  query ($id: Int!) {
    applicantsShow(id: $id) {
      id
      createdAt
      jobStage {
        stageTypeId
        stageLabel
        stageGroup
      }
      job {
        permittedFields
        jobStages {
          id
          stageLabel
          stageTypeId
        }
        accountManagers {
          fullName
          email
        }
      }
      overallFeedbacksCount
      pendingInterviewFeedbacks {
        id
        fromDatetime
        timezone
        eventTypeDescription
        attendees {
          id
        }
        job {
          owner {
            id
          }
          jobRecruiters {
            user {
              id
            }
          }
        }
      }
      profile {
        id
        fullName
        email
        headline
        phoneNumber
        address
        avatarVariants
        coverLetter
        countryStateId
        links
        sourced
        sourcedDescription
        sourcedName
        sourcedNameDescription
        createdAt
        profileCvs {
          id
          attachments {
            id
            file
            blobs
          }
        }
        jobs {
          id
          title
          jobStages {
            id
            stageLabel
            stageTypeId
          }
        }
        applicants {
          id
          coverLetter
          jobStage {
            stageGroup
          }
          job {
            status
            id
            title
            slug
            owner {
              id
              fullName
              email
            }
          }
          overallFeedbacksCount
        }
        user {
          id
          fullName
          avatarVariants
          defaultColour
        }
        owner {
          id
          fullName
          avatarVariants
          defaultColour
        }
        totalYearsOfExp
        permittedFields
        tags {
          value
          name
          id
        }
        warning
        defaultAccessibleApplicantId
        customFields
        employeeId
      }
      placement {
        id
        createdAt
        updatedAt
        customFields
        permittedFields
        editablePlacement
      }
    }
  }
`

export default QueryCandidateApplicantDetail
