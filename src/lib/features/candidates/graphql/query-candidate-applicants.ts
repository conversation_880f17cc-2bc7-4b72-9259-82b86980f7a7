import { gql } from 'urql'

import type { ICandidateApplicant } from '../types'

const QueryCandidateApplicants = gql<
  {
    profileApplicantsList: {
      collection: Array<ICandidateApplicant>
      metadata: {
        totalCount: number
      }
    }
  },
  { profileId?: number; page?: number; limit?: number }
>`
  query ($profileId: Int!, $page: Int, $limit: Int) {
    profileApplicantsList(profileId: $profileId, page: $page, limit: $limit) {
      collection {
        id
        jobId
        profileId
        profile {
          fullName
          email
        }
        status
        statusDescription
        rejectedReasonLabel
        placement {
          id
          customFields
          permittedFields
          createdAt
          editablePlacement
        }
        job {
          id
          title
          status
          statusDescription
          permittedFields
          jobStages {
            id
            stageLabel
            stageTypeId
          }
          owner {
            id
            email
            defaultColour
            avatarVariants
            fullName
          }
          jobRecruiters {
            user {
              id
            }
          }
          department {
            name
          }
          jobLocations {
            state
            country
          }
          currentUserAccessible
        }
        createdBy {
          email
          fullName
          avatarVariants
        }
        hiredBy {
          id
          email
          fullName
          avatarVariants
          defaultColour
        }
        hiredDate
        onboardDate
        createdAt
        jobStage {
          id
          stageLabel
          stageTypeId
          stageGroup
        }
        currentStagedDate
        incoming
        tagLabel
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryCandidateApplicants
