import { gql } from 'urql'

import type { IProfileListType } from '../types'

const QueryProfilesList = gql<
  {
    profilesList: {
      collection: IProfileListType[]
      metadata: { totalCount: number }
    }
  },
  {}
>`
  query (
    $limit: Int
    $page: Int
    $search: String
    $jobId: Int
    $stageTypeId: Int
    $sorting: JSON
    $tagIds: [Int!]
    $ownerIds: [Int!]
    $operator: String
    $profileTalentPoolIds: [Int!]
    $profileTalentPoolId: Int
    $applicantStatuses: [String!]
    $basedOnApplicant: Boolean
    $tagOperator: String
    $countryStateId: Int
    $profileLevel: ProfileProfileLevel
    $fromCreatedAt: ISO8601DateTime
    $toCreatedAt: ISO8601DateTime
    $fromUpdatedAt: ISO8601DateTime
    $toUpdatedAt: ISO8601DateTime
    $profileCvsEmpty: Boolean
    $fieldsFilter: [JSON!]
    $uSettingId: Int
  ) {
    profilesList(
      limit: $limit
      page: $page
      search: $search
      jobId: $jobId
      stageTypeId: $stageTypeId
      sorting: $sorting
      tagIds: $tagIds
      ownerIds: $ownerIds
      operator: $operator
      profileTalentPoolIds: $profileTalentPoolIds
      profileTalentPoolId: $profileTalentPoolId
      applicantStatuses: $applicantStatuses
      basedOnApplicant: $basedOnApplicant
      tagOperator: $tagOperator
      countryStateId: $countryStateId
      profileLevel: $profileLevel
      fromCreatedAt: $fromCreatedAt
      toCreatedAt: $toCreatedAt
      fromUpdatedAt: $fromUpdatedAt
      toUpdatedAt: $toUpdatedAt
      profileCvsEmpty: $profileCvsEmpty
      fieldsFilter: $fieldsFilter
      uSettingId: $uSettingId
    ) {
      collection {
        employeeId
        defaultAccessibleApplicantId
        id
        fullName
        email
        phoneNumber
        avatarVariants
        createdAt
        updatedAt
        headline
        links
        tags {
          id
          name
        }
        owner {
          id
          fullName
          avatarVariants
          defaultColour
        }
        profileCvs {
          id
          attachments {
            id
            file
            blobs
          }
        }
        applicants {
          id
          job {
            id
            title
            currentUserAccessible
            status
            statusDescription
            currentUserAccessible
            permittedFields
          }
          jobStage {
            stageLabel
            stageTypeId
          }
          flagNew
          rejectedReasonLabel
          status
        }
        permittedFields
        customFields
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryProfilesList
