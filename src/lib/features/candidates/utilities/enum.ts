import type { TFunction } from 'react-i18next'

import type { ISelectOption } from '~/core/@types/global'
import type { LucideIconName } from '~/core/ui/IconWrapper'

export const OPTION_SORT_CREATE_AT_CANDIDATE: ISelectOption[] = [
  {
    value: 'desc',
    label: 'desc',
    icon: 'ArrowDown'
  },
  {
    value: 'asc',
    label: 'asc',
    icon: 'ArrowUp'
  }
]
export const DESC_SORTING = 'desc'
export const ASC_SORTING = 'asc'
export const OVERVIEW_TAB = 'overview'
export const FILE_TAB = 'files'
export const JOBS_TAB = 'jobs'
export const INTERVIEWS_TAB = 'interviews'
export const EMAIL_TAB = 'messages'
export const ACTIVITY_TAB = 'activities'
export const NOTE_TAB = 'notes'
export const TASK_TAB = 'tasks'
export const FEEDBACK_TAB = 'feedback'
export const RECOMMEND_TAB = 'recommend'
export const CAREER = 'career'
export const CANDIDATE_TAB = 'candidate'

export const ListSuggestNoticeOfPeriod = [{ value: '30' }, { value: '45' }, { value: '60' }]
export const DEFAULT_CURRENCY = 'USD'
export const DEFAULT_CURRENCY_NAME = 'US Dollar'
export const LIMIT_JOB_SHOW = 5

export const FIELDS_USER_SETTING_DISPLAY: {
  [key: string]: { key: string; iconMenus: LucideIconName }
} = {
  hiredBy: {
    key: 'hiredBy',
    iconMenus: 'UserCheck'
  },
  comments: {
    key: 'comments',
    iconMenus: 'FileEdit'
  },
  jobStage: {
    key: 'jobStage',
    iconMenus: 'Database'
  },
  company: {
    key: 'company',
    iconMenus: 'Building'
  },
  fullName: {
    key: 'fullName',
    iconMenus: 'User'
  },
  applicant: {
    key: 'applicant',
    iconMenus: 'User'
  },
  status: {
    key: 'status',
    iconMenus: 'CheckSquare'
  },
  revenue: {
    key: 'revenue',
    iconMenus: 'DollarSign'
  },
  fee: {
    key: 'fee',
    iconMenus: 'File'
  },
  hiredDate: {
    key: 'hiredDate',
    iconMenus: 'CalendarCheck'
  },
  onboardDate: {
    key: 'onboardDate',
    iconMenus: 'CalendarCheck'
  },
  endOfProbationDate: {
    key: 'endOfProbationDate',
    iconMenus: 'CalendarCheck'
  },
  profitSplits: {
    key: 'profitSplits',
    iconMenus: 'Users'
  },
  createdBy: {
    key: 'createdBy',
    iconMenus: 'UserPlus'
  },
  headline: {
    key: 'headline',
    iconMenus: 'Newspaper'
  },
  departments: {
    key: 'departments',
    iconMenus: 'Network'
  },
  preferredWorkStates: {
    key: 'preferredWorkStates',
    iconMenus: 'Map'
  },
  email: {
    key: 'email',
    iconMenus: 'Mail'
  },
  phoneNumber: {
    key: 'phoneNumber',
    iconMenus: 'Phone'
  },
  location: {
    key: 'location',
    iconMenus: 'MapPin'
  },
  links: {
    key: 'links',
    iconMenus: 'Link'
  },
  resume: {
    key: 'cv',
    iconMenus: 'FileText'
  },
  tag: {
    key: 'tags',
    iconMenus: 'Tag'
  },
  owner: {
    key: 'owner',
    iconMenus: 'UserCog'
  },
  jobs: {
    key: 'jobs',
    iconMenus: 'Briefcase'
  },
  job: {
    key: 'job',
    iconMenus: 'Briefcase'
  },
  stage: {
    key: 'stage',
    iconMenus: 'Database'
  },
  createdAt: {
    key: 'createdAt',
    iconMenus: 'Clock'
  },
  lastActivity: {
    key: 'lastActivity',
    iconMenus: 'Zap'
  },
  applicantDisqualified: {
    key: 'disqualifyCandidates',
    iconMenus: 'Zap'
  },
  jobArchived: {
    key: 'archivedJobs',
    iconMenus: 'Zap'
  },
  talentPool: {
    key: 'talentPools',
    iconMenus: 'FolderSearch'
  },
  summary: {
    key: 'summary',
    iconMenus: 'AlignLeft'
  },
  skills: {
    key: 'skills',
    iconMenus: 'ListChecks'
  },
  openToWork: {
    key: 'openToWork',
    iconMenus: 'Briefcase'
  },
  languages: {
    key: 'languages',
    iconMenus: 'Languages'
  },
  nationality: {
    key: 'nationality',
    iconMenus: 'Flag'
  },
  birthday: {
    key: 'birthday',
    iconMenus: 'Cake'
  },
  willingToRelocate: {
    key: 'willingToRelocate',
    iconMenus: 'Rotate3d'
  },
  noticeToPeriodDays: {
    key: 'noticeToPeriodDays',
    iconMenus: 'CalendarClock'
  },
  salary: {
    key: 'salary',
    iconMenus: 'DollarSign'
  },
  currentSalary: {
    key: 'currentSalary',
    iconMenus: 'DollarSign'
  },
  expectedSalary: {
    key: 'expectedSalary',
    iconMenus: 'DollarSign'
  },
  profileLevel: {
    key: 'profileLevel',
    iconMenus: 'Zap'
  },
  totalYearsOfExp: {
    key: 'totalYearsOfExp',
    iconMenus: 'Boxes'
  },
  publicId: {
    key: 'publicId',
    iconMenus: 'ClipboardList'
  },
  workExperiences: {
    key: 'workExperiences',
    iconMenus: 'ClipboardSignature'
  },
  educations: {
    key: 'educations',
    iconMenus: 'BookOpen'
  }
}

export const totalYoeOptions: ISelectOption[] = Array.from(Array(52).keys()).map((item) => ({
  value: String(item)
}))

export const PROFILE_CANDIDATE_TAB = {
  profile: 'profile',
  jobRelated: 'jobRelated'
}

export const CANDIDATE_STATUS_FILTER: ISelectOption[] = [
  { value: 'qualified' },
  { value: 'disqualified' },
  { value: 'new' }
]

export const CANDIDATE_CV_FILTER: ISelectOption[] = [{ value: 'empty' }, { value: 'not_empty' }]

export const CANDIDATE_YES_NO: ISelectOption[] = [{ value: 'yes' }, { value: 'no' }]

export const FILTER_CANDIDATE_FIELDS_VALUE = {
  job_id: 'job_id',
  applicant_statuses: 'applicant_statuses',
  stage_type_id: 'stage_type_id',
  tagged_ids: 'tagged_ids',
  company_id: 'company_id',
  owner_id: 'owner_id',
  sourced: 'sourced',
  profile_talent_pool_ids: 'profile_talent_pool_ids',
  country_state_id: 'country_state_id',
  profile_level: 'profile_level',
  created_at: 'created_at',
  updated_at: 'updated_at',
  profile_cvs_empty: 'profile_cvs_empty',
  full_name: 'full_name',
  public_id: 'public_id',
  email: 'email',
  phone_number: 'phone_number',
  links: 'links',
  summary: 'summary',
  headline: 'headline',
  department_ids: 'department_ids',
  birthday: 'birthday',
  open_to_work: 'open_to_work',
  skills: 'skills',
  languages: 'languages',
  willing_to_relocate: 'willing_to_relocate',
  notice_to_period_days: 'notice_to_period_days',
  total_years_of_exp: 'total_years_of_exp',
  nationality: 'nationality',
  expected_salary: 'expected_salary',
  current_salary: 'current_salary',
  operator: 'operator',
  educations: 'educations',
  work_experiences: 'work_experiences',
  preferred_work_state_ids: 'preferred_work_state_ids'
}

export const FILTER_CANDIDATE_FIELDS_CONDITION = {
  equal: 'equal',
  greaterThan: 'greater_than',
  lessThan: 'less_than',
  contains: 'contains',
  is: 'is',
  isNot: 'is_not',
  isAnyOf: 'is_any_of',
  isNoneOf: 'is_none_of',
  isEmpty: 'is_empty',
  isNotEmpty: 'is_not_empty',
  range: 'range',
  anyOfAll: 'any_of_all',
  anyOf: 'any_of',
  all: 'all',
  and: 'and',
  or: 'or'
}

export const PROFILE_VIEW_DISPLAY_DEFAULT = {
  id: undefined,
  group: undefined,
  createdById: undefined,
  name: undefined,
  state: undefined,
  stateDescription: undefined,
  profileDisplay: undefined,
  profileFilters: undefined
}

export const PROFILE_VIEW_DISPLAY_STATE = [
  {
    value: 'public'
  },
  {
    value: 'private'
  }
]

export const OPERATOR = {
  and: 'and',
  or: 'or'
}

export const REQUIRED_KEYS = [
  'fullName',
  'avatar',
  'certificates',
  'educations',
  'references',
  'workExperiences',
  'summary',
  'email',
  'links',
  'location',
  'phoneNumber'
]

export const MAX_ITEM_TAG = 10
export const VIEW_MODE = {
  page: 'page',
  drawer: 'drawer'
}
export const MAX_ITEM_EXPORT_PDF = 100

export const ENUMS_CALL_TO_OBJECTS = {
  profile: 'profile',
  contact: 'contact'
}

export const OPTIONS_DIRECTIONS = (t: TFunction) => [
  {
    value: 'outbound',
    supportingObj: {
      name: `${t('label:callLogDirectionOptions:outbound')}`
    }
  },
  {
    value: 'inbound',
    supportingObj: {
      name: `${t('label:callLogDirectionOptions:inbound')}`
    }
  }
]

export const OPTIONS_OUTCOME = (t: TFunction) => [
  {
    value: 'answered',
    supportingObj: {
      name: `${t('label:callLogOutComeOptions:answered')}`
    },
    color: 'green'
  },
  {
    value: 'busy',
    supportingObj: {
      name: `${t('label:callLogOutComeOptions:busy')}`
    },
    color: 'red'
  },
  {
    value: 'left_message',
    supportingObj: {
      name: `${t('label:callLogOutComeOptions:left_message')}`
    },
    color: 'blue'
  },
  {
    value: 'no_answer',
    supportingObj: {
      name: `${t('label:callLogOutComeOptions:no_answer')}`
    },
    color: 'gray'
  },
  {
    value: 'wrong_number',
    supportingObj: {
      name: `${t('label:callLogOutComeOptions:wrong_number')}`
    },
    color: 'yellow'
  }
]
