import type { IDotColorProps } from '~/core/ui/Dot'
import type { ISelectOption } from '~/core/ui/Select'
import { trimFirstContentBreakLine } from '~/core/utilities/common'

import type { ICareerListing, ICareerPath, IPositionForm } from '../type'
import { POSITION_DOT_STATUS, POSITION_STATUS_ENUM } from '../utilities/enum'

export const mappingPositionAddGraphQL = (data: IPositionForm) => {
  return {
    name: data.name,
    status: data.status || POSITION_STATUS_ENUM.active,
    description: trimFirstContentBreakLine(data.description),
    skills: (data.skills || []).length > 0 ? data.skills || [] : undefined
  }
}

export const mappingPositionEditGraphQL = (data: IPositionForm) => {
  return {
    id: data.id,
    name: data.name,
    status: data.status || POSITION_STATUS_ENUM.active,
    description: trimFirstContentBreakLine(data.description),
    skills: (data.skills || []).length > 0 ? data.skills || [] : undefined
  }
}

export const mappingCareerPathAddGraphQL = (data: ICareerPath) => {
  return {
    positionId: Number(data.position?.value),
    nextPositionId: Number(data.nextPosition?.value),
    status: data.status || POSITION_STATUS_ENUM.active
  }
}

export const mappingCareerPathEditGraphQL = (data: ICareerPath) => {
  console.log(data)
  return {
    positionId: Number(data.position?.value),
    nextPositionId: Number(data.nextPosition?.value),
    status: data.status || POSITION_STATUS_ENUM.active,
    id: Number(data.id)
  }
}

export const getStatusLabelMap = (t: (key: string) => string) => ({
  [POSITION_STATUS_ENUM.active]: t('settings:positions:positionStatus:active'),
  [POSITION_STATUS_ENUM.archived]: t('settings:positions:positionStatus:archived'),
  [POSITION_STATUS_ENUM.draft]: t('settings:positions:positionStatus:draft')
})

export const mappingCareerPathEditFormGraphQL = (defaultValue?: ICareerListing, t?: (key: string) => string) => {
  return {
    ...defaultValue,
    position: defaultValue?.position?.id
      ? {
          value: String(defaultValue?.position?.id || undefined),
          supportingObj: {
            name: defaultValue?.position?.name
          }
        }
      : undefined,
    nextPosition: defaultValue?.nextPosition?.id
      ? {
          value: String(defaultValue?.nextPosition?.id || undefined),
          supportingObj: {
            name: defaultValue?.nextPosition?.name
          }
        }
      : undefined,
    status: defaultValue?.status
  } as ICareerPath
}

export const mappingCareerPathStatusFormGraphQL = (status?: string, t?: (key: string) => string) => {
  console.log(status)

  const statusLabelMap = t ? getStatusLabelMap(t) : ({} as Record<string, string>)

  return {
    value: String(status || ''),
    supportingObj: {
      name: statusLabelMap[status as POSITION_STATUS_ENUM] ?? ''
    },
    dot: POSITION_DOT_STATUS(status || 'gray') as IDotColorProps
  } as ISelectOption
}
