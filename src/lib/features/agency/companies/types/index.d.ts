import type { ILogoAndAvatarVariants } from '~/core/@types/global'

import type { LocationCompanyDetailType } from './company-detail'

export interface IUserCompanySettingResponse {
  id: number
  kind: string
  kindDescription: string
  group: string
  groupDescription: string
  companyColumnsIsDefault: boolean
  values: InputFieldsSettingType[]
  companyDisplay: InputFieldsSettingType[]
}

export interface IUserCompanySettingResponse {
  id: number
  kind: string
  kindDescription: string
  group: string
  groupDescription: string
  companyColumnsIsDefault: boolean
  values: InputFieldsSettingType[]
  companyDisplay: InputFieldsSettingType[]
}

export interface ICompaniesFilter {
  jobId?: { value?: number }
  stageTypeId?: { value?: number }
  page?: number
  search?: string
  ownerIds?: ISelectOption[]
  location?: ISelectOption
  departmentIds?: ISelectOption[]
  industryIds?: ISelectOption[]
  jobStatistic?: { value?: number }
  companyStatusIds?: ISelectOption[]
  sorting?: {
    created_at?: 'desc' | 'asc'
    name?: 'desc' | 'asc'
    updated_at?: 'desc' | 'asc'
    openJobsCount?: 'desc' | 'asc'
  }
  isFilterTouched?: boolean
  dateRange?: {
    from?: string
    to?: string
  }
}

export interface ISortingCompanies {
  [key: string]: string | null
}
export interface IChubCompaniesFilterOption {
  id: string
  name?: string
  permittedFields: ICompanyPermittedFields
  logoVariants?: ILogoAndAvatarVariants
}
export interface IChubCompaniesFilterOption {
  id: string
  name?: string
  permittedFields: ICompanyPermittedFields
  logoVariants?: ILogoAndAvatarVariants
}

export interface IAgencyCompanies {
  id: string
  name: string
  permittedFields?: ICompanyPermittedFields
  logoVariants?: ILogoAndAvatarVariants
}
export interface ICompanyPermittedFields {
  industries: {
    value?: {
      id?: number
      name: string
    }[]
    visibility_changeable?: boolean
  }
  name: {
    value?: string
    visibility_changeable?: boolean
  }
  companyStatus: { value: { name: string }; visibility_changeable?: boolean }
  companyStatistic: {
    value: { openJobsCount: number; archivedJobsCount: number }
    visibility_changeable?: boolean
  }
  domain: {
    value?: string
    visibility_changeable?: boolean
  }
  companySize: {
    value?: string
    visibility_changeable?: boolean
  }
  companyLocations: {
    value?: Array<LocationCompanyDetailType>
    visibility_changeable?: boolean
  }
  description: { value: string }
  links: {
    value?: { [key: string]: string[] }
    roles?: string[]
    visibility_changeable?: boolean
    client_user_visibility?: boolean
  }
  taxCode: {
    value?: string
    visibility_changeable?: boolean
  }
  foundedYear: {
    value?: string
    visibility_changeable?: boolean
  }
  owner: {
    value?: {
      id: number
      email: string
      avatar: ILogoAndAvatarVariants
      full_name: string
      fullName: string
      avatarVariants: ILogoAndAvatarVariants
      default_colour: string
    }
    visibility_changeable?: boolean
  }
  description: {
    value?: string
    visibility_changeable?: boolean
  }
  createdAt: {
    value?: string
    visibility_changeable?: boolean
  }
  createdBy: {
    value?: {
      id: number
      fullName: string
      avatarVariants: ILogoAndAvatarVariants
      defaultColour: string
    }
    visibility_changeable?: boolean
  }
  updatedAt: {
    value?: string
    visibility_changeable?: boolean
  }
  updatedBy: {
    value?: {
      id: number
      fullName: string
      avatarVariants: ILogoAndAvatarVariants
      defaultColour: string
    }
    visibility_changeable?: boolean
  }
}
