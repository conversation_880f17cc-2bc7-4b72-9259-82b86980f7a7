import type { RefObject } from 'react'

import type { ILogoAndAvatarVariants, ISelectOption, IUserInformation } from '~/core/@types/global'

import type {
  CustomFieldFormType,
  CustomFieldResponseItem
} from '~/lib/features/settings/profile-fields/types/custom-field'

import type { ICompanyPermittedFields } from '../../agency/companies/types'
import type { CompanyDetailResponseType } from '../../agency/companies/types/company-detail'
import type { IApplicableJobs } from '../../calendar/types'
import type { LanguagesType } from '../../candidates/types'
import type { JOB_STATUS_ENUM } from '../utilities/enum'

export type IJobForm = {
  id?: string | number
  title?: string
  status?: string
  applicants?: IJobApplicants[]
  remoteStatus?: string
  departmentId?: Array<ISelectOption>
  ownerId?: Array<ISelectOption>
  hiringManagerIds?: Array<ISelectOption>
  description?: string
  pitch?: string
  salaryFrom?: string
  salaryTo?: string
  currency?: string
  employmentType?: string
  typeOfSalary?: string
  industryIds?: Array<ISelectOption>
  locationIds?: Array<ISelectOption>
  jobLevel?: string
  education?: string
  jobCategoryId?: ISelectOption
  skills?: Array<ISelectOption>
  languages?: Array<ISelectOption>
  jobTalentPoolIds?: Array<ISelectOption>
  hiringProcess?: ISelectOption & IPipelineTemplate
  pipelineTemplateId?: string
  hiringProcessName?: string
  jobStages?: {
    index: string
    stageLabel: string
    stageTypeId: number
    _destroy?: boolean
  }[]
  jobReferable?: boolean
  enablingReward?: boolean
  referralRewardType?: string
  rewardAmount?: number | string
  rewardCurrency?: string
  rewardGift?: string
  requisitionId?: number
  headcount?: number | string
  companyId?: ISelectOption
  referenceId?: string
  taggedIds?: ISelectOption[]
  jobId?: number | string
  toneType?: string
  language?: Array<ISelectOption>
} & CustomFieldFormType

export interface IJobDetailForm {
  id?: string
  title: string
  headcount?: number
  status?: string
  applicants?: IJobApplicants[]
  remoteStatus?: string
  department?: {
    id: string
    name: string
  }
  company?: {
    id: string
    permittedFields?: ICompanyPermittedFields
  }
  owner: {
    id: string
    email: string
    fullName?: string
  }
  hiringManagers: {
    id: string
    email: string
    fullName?: string
  }[]
  recruiters: Array<{
    id: string
    email: string
    fullName?: string
    defaultColour?: string
    avatarVariants?: ILogoAndAvatarVariants
  }>
  jobLocations: Array<{
    locationId?: string
    name?: string

    id?: number
    companyLocationId?: number
    address?: string
    country?: string
    city?: string
    state?: string
  }>
  industries: Array<{
    id: string
    name: string
  }>
  description: string
  pitch?: string
  salaryFrom?: number
  salaryTo?: number
  currency?: string
  employmentType?: string
  typeOfSalary?: string
  industryIds?: string
  jobLevel?: string
  education?: string
  jobCategory?: { id: number; name: string }
  skills?: string[]
  hiringProcess?: object
  jobStages?: IJobStages
  customFields?: CustomFieldResponseItem[]
  languages?: Array<LanguagesType>
  referenceId?: string
  permittedFields?: {
    [key: string]: {
      role_changeable?: boolean
      visibility_changeable?: boolean
      roles?: Array<string>
      value?: string
    }
    talentPools: {
      value?: Array<{
        id: number
        name: string
        status: string
        createdAt: string
        updatedAt: string
      }>
      roles?: string[]
      visibility_changeable?: boolean
      client_user_visibility?: boolean
    }
    company: {
      value?: { id: number; name: string }
      roles?: string[]
      visibility_changeable?: boolean
      client_user_visibility?: boolean
    }
  }
  tags?: Array<{
    id: number
    name: string
  }>
}

export interface InfiniteScrollProps {
  wrapperRef?: RefObject<HTMLDivElement | null>
  loadData: () => Promise<any>
  haveNext: boolean
  children: React.ReactNode
  skeletonElement?: React.ReactElement<any>
  isShowEndLabel?: boolean
  className?: string
  classNameEndOfList?: string
}

export type IJobStages = Array<IJobStage>

export type IJobStage = {
  stageLabel: string
  id: number
  index?: number
  clientShared?: boolean
  stageTypeId?: number
  stageGroup?: string
  updateable?: boolean
}

export type IStageType = {
  label: string
  id: string
  colorClassName: string
}

export type IStageTypes = Array<IStageType>

export interface IJobApplicants {
  id: string
  createdAt: string
  updatedAt: string
  hiredDate: string
  profile: {
    id: number
    fullName: string
    links?: { [key: string]: Array<string> }
    profileCvs?: profileCvsProps
    email: string[]
    applicants: IApplicableJobs[]
    permittedFields?: IPermittedFields
    user?: {
      id?: number
      fullName?: string
      avatarVariants?: ILogoAndAvatarVariants
      defaultColour?: string
      email?: string
    }
  }
  job: {
    id: number
    title: string
    permittedFields?: {
      [key: string]: {
        role_changeable?: boolean
        visibility_changeable?: boolean
        roles?: Array<string>
        value?: string & { name?: string; id?: number }
      }
    }
    status?: string
    accountManagers: IUserInformation[]
    owner?: {
      email?: string
      fullName?: string
    }
    company?: CompanyDetailResponseType
  }
  status: string
  jobStage: IJobStage
  createdBy: {
    email: string
    fullName: string
    avatarVariants?: ILogoAndAvatarVariants
    defaultColour: string
  }
  flagNew: boolean
  lastestInterview: {
    fromDatetime: string
    ikitFeedbacksSummary: Array<{}>
  }
  nearestInterview: {
    fromDatetime: string
    ikitFeedbacksSummary: Array<{}>
  }
  placement?: IPlacement
  jobId: number
  coverLetter?: string
  incoming?: string
  hiredBy?: {
    id: string
    avatarVariants: ILogoAndAvatarVariants
    fullName: string
    email: string
    defaultColour: string
  }
  onboardDate?: string
  rejectedAt?: string
}

export interface IJobBoardItem {
  code: string
  label: string
  icon: string
  tooltip: string
  w: number
  h: number
}

export type IJobInfoType = {
  createdAt?: string
  company?: CompanyDetailResponseType
  headcount?: Int
  jobLocations?: {
    name: string
    address: string
    country: string
    state: string
    city: string
    zipCode: string
  }[]

  salaryFrom?: number
  salaryTo?: number
  currency?: string
  typeOfSalary?: string
  typeOfSalaryDescription?: string

  employmentType?: string
  employmentTypeDescription?: string

  department?: {
    name: string
  }
  skills?: string[]
  languages?: Array<LanguagesType>
  jobCategory?: {
    id: string
    name: string
    description: string
  }
  jobLevelDescription?: string
  educationDescription?: string
  jobRecruiters?: {
    id: string
    responsibility: string
    responsibilityDescription: string
    user: IMemberInfo
  }[]
  owner?: IMemberInfo
  hiringManagers?: {
    id: number
  }[]
  referenceId?: string
  tags: {
    id: string
    name: string
  }[]
} & {
  customFields?: CustomFieldResponseItem[]
  permittedFields?: {
    remoteStatus?: {
      value?: string
      visibility_changeable?: boolean
    }
    employmentType?: {
      value?: string
      visibility_changeable?: boolean
    }
    employmentTypeDescription?: {
      value?: string
      visibility_changeable?: boolean
    }
    jobLevel?: {
      value?: string
      visibility_changeable?: boolean
    }
    jobLevelDescription?: {
      value?: string
      visibility_changeable?: boolean
    }
    typeOfSalary?: {
      value?: string
      visibility_changeable?: boolean
    }
    typeOfSalaryDescription?: {
      value?: string
      visibility_changeable?: boolean
    }
    currency?: {
      value?: string
      visibility_changeable?: boolean
    }
    salaryFrom?: {
      value?: string
      visibility_changeable?: boolean
    }
    salaryTo?: {
      value?: string
      visibility_changeable?: boolean
    }
    company?: {
      value?: { id: number; name?: string }
      visibility_changeable?: boolean
    }
    talentPools?: {
      value?: Array<{
        name: string
      }>
    }
    publicId?: {
      value?: string
      visibility_changeable?: boolean
    }
  }
}
export interface IMemberInHiringTeamOfJob {
  value: number
  avatar: string
  roleId?: string
  avatarVariants: ILogoAndAvatarVariants | undefined
  supportingObj: {
    email: string
    name: string
  }
  skills?: string[]
  jobCategory?: {
    id: string
    name: string
    description: string
  }
  jobLevelDescription?: string
  educationDescription?: string
}
export interface IMarkAsHiredForm {
  id?: number
  hiredDate: Date
  onboardDate: Date
  hiredById?: ISelectOption
}
export interface IMarkAsHiredBulkForm {
  id?: number
  hiredDate: Date
  onboardDate: Date
  hiredById?: ISelectOption
  jobStageId?: number
}

export type MatchedFieldTotal = {
  field: string
  total_rate: number
  total_rate_string: string
}

export type MatchedField = {
  field: string
  label: string
  matched_rate: string
  data: Array<{
    matched: boolean
    label: string
  }>
}

export type IJobManagementItemQuery = {
  id: string
  title: string
  status: JOB_STATUS_ENUM
  statusDescription: string
  jobReferable?: boolean
  jobLocations: {
    name: string
    address: string
    city: string
    state: string
    country: string
  }[]
  slug: string
  jobStages?: IJobStages
  department?: { name: string }
  industries?: { name: string }[]
  statistic?: {
    viewCount: number
    newApplicantCount: number
    processedCount: number
    hireCount: number
    inprocessGroupCount: number
    hiredGroupCount: number
    applicantsCount: Array<{
      count: number
      job_stage_id: number
    }>
  }
  createdAt: string
  teamMembers: IMemberInfo[]
  owner: {
    id: string
    fullName: string
    avatarVariants?: ILogoAndAvatarVariants
    email: string
    defaultColour: string
    roles: Array<{ name: string }>
  }
  tenant: {
    slug: string
  }
  clientMembers?: {
    id: string
    fullName: string
    avatarVariants?: ILogoAndAvatarVariants
    email: string
    defaultColour: string
  }[]
  jobRecruiters: {
    id: string
    responsibility: string
    responsibilityDescription: string
    user: IMemberInfo
  }[]
  hiringManagers: {
    id: string
    fullName: string
    avatarVariants?: ILogoAndAvatarVariants
    email: string
    defaultColour: string
    roles: Array<{ name: string }>
  }[]
  headcount: number
  company?: {
    id: string
    permittedFields?: ICompanyPermittedFields
  }
  recommendationMatchedFields?: Array<MatchedFieldTotal | MatchedField>

  salaryFrom?: number
  salaryTo?: number
  currency?: string
  typeOfSalary?: string
  typeOfSalaryDescription?: string

  permittedFields?: {
    [key: string]: {
      role_changeable?: boolean
      visibility_changeable?: boolean
      roles?: Array<string>
      value?: string & { name?: string; id?: string }
    }
  }
  tags?: Array<{
    id: number
    name: string
  }>
  applicants?: Array<{
    id: number
  }>
  listSavedJobMemberIds?: number[]
}
export interface IJobDetailParams {
  contactId?: number
  clientInvitationEmail?: string
}
export interface IAIWriterJobType {
  language?: string
  toneType?: string
}
export interface IAIWriterJobForm {
  jobId: number
  title: string
  description: string
  toneType: string
  skills?: Array<ISelectOption>
  languages?: Array<ISelectOption>
  jobLevel?: string
  employmentType?: string
  locationIds?: Array<ISelectOption>
  salaryFrom?: string
  salaryTo?: string
  currency?: string
  typeOfSalary?: string
}
