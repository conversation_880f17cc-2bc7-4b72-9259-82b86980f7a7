import { gql } from 'urql'

const QueryTenantJobDetail = gql`
  query ($id: Int!) {
    jobsShow(id: $id) {
      id
      title
      status
      applicants {
        id
        jobStage {
          id
        }
        status
      }
      departmentId
      headcount
      owner {
        id
        email
        fullName
        defaultColour
      }
      hiringManagers {
        id
        email
        fullName
        defaultColour
      }
      jobRecruiters {
        id
        responsibility
        user {
          email
          fullName
          defaultColour
        }
      }
      description
      pitch
      education
      skills
      jobCategory {
        id
        name
      }
      industries {
        id
        name
      }
      jobLocations {
        id
        locationId
        companyLocationId
        name
        country
        state
      }
      department {
        id
        name
      }
      hiringProcessName
      jobStages {
        id
        stageTypeId
        stageLabel
        index
        updateable
        clientShared
        stageGroup
      }
      jobIkits {
        id
        name
        guideline
        jobIkitSessions {
          id
          name
          position
          jobIkitMetrics {
            id
            name
          }
          jobIkitQuestions {
            id
            content
          }
        }
        jobStages {
          id
          stageTypeId
          stageLabel
          stageGroup
          updateable
          index
        }
        stageIds
      }
      customFields
      permittedFields
      languages
      referenceId
      tags {
        id
        name
      }
    }
  }
`

export default QueryTenantJobDetail
