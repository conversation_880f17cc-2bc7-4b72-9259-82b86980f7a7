import { gql } from 'urql'

const QueryTenantJobApplicants = gql`
  query ($page: Int, $limit: Int, $jobId: Int, $jobStageId: Int, $search: String, $status: ApplicantStatus) {
    jobApplicantsList(
      jobId: $jobId
      jobStageId: $jobStageId
      page: $page
      limit: $limit
      search: $search
      status: $status
    ) {
      collection {
        id
        jobId
        coverLetter
        incoming
        createdAt
        updatedAt
        hiredDate
        hiredBy {
          id
          fullName
          avatarVariants
          defaultColour
        }
        profile {
          id
          fullName
          links
          email
          permittedFields
          profileCvs {
            attachments {
              id
              file
              blobs
            }
          }
          avatarVariants
          user {
            id
            fullName
            avatarVariants
            defaultColour
          }
        }
        status
        jobStage {
          id
          stageLabel
          stageTypeId
          stageGroup
        }
        createdBy {
          email
          fullName
          avatarVariants
          defaultColour
        }
        flagNew
        lastestInterview {
          fromDatetime
          ikitFeedbacksSummary
        }
        nearestInterview {
          fromDatetime
          ikitFeedbacksSummary
        }
        job {
          status
          id
          title
          slug
          permittedFields
          accountManagers {
            fullName
            email
          }
          owner {
            id
            email
            fullName
          }
        }
        placement {
          id
          customFields
          permittedFields
          createdAt
          editablePlacement
        }
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryTenantJobApplicants
