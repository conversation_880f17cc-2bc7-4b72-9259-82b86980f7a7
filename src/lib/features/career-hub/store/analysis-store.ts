import { create } from 'zustand'

import type { AnalysisData } from '../types'

interface AnalysisState {
  data: AnalysisData | null
  isRedirecting: boolean
  setData: (data: AnalysisData) => void
  clearData: () => void
  setIsRedirecting: (value: boolean) => void
  // Clear data for specific position (useful when switching positions)
  clearDataAnalysis: () => void
}

export const useAnalysisStore = create<AnalysisState>((set, get) => ({
  data: null,
  isRedirecting: false,
  setData: (data) => set({ data }),
  clearData: () => set({ data: null }),
  setIsRedirecting: (value) => set({ isRedirecting: value }),
  clearDataAnalysis: () => {
    set({ data: null })
  }
}))
