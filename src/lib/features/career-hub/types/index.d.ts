import type { ISelectOption } from '~/core/ui/Select'

export type SkillsType = {
  name: string
  id: string
}

export type PositionType = {
  id: string
  name: string
  skills?: Array<string>
  matchingData?: {
    totalRate?: number
  }
}

export type CareerGoalType = {
  id: string
  skills: Array<string>
  skillRecords: Array<string>
  skillsYouNeed: Array<string>
  position: PositionType
}

export type CourseType = {
  id?: number
  thumbnailVariants?: ThumbnailVariants
  link?: string
  title?: string
  provider?: string
  price?: number
  language?: string
  status?: string
  duration?: string
  courseLevel?: string
  skills?: Array<string>
  skillRecords?: Array<SkillRecords>
}

export type SkillRecords = {
  id?: string
  name?: string
  inProfile?: string
}

export type AIAnalysisType = {
  id?: string
  content?: string
  status?: 'loading' | 'completed' | 'error'
  error?: string
}

export type AIAnalysisSectionType = {
  title: string
  content: string
  icon?: string
  type?: 'success' | 'warning' | 'info' | 'error'
}

export type ProfileValidationType = {
  hasRequiredData: boolean
  missingFields: string[]
  hasSkills: boolean
  hasTitle: boolean
  hasWorkExperience: boolean
  hasEducation: boolean
  hasCertifications: boolean
}

export type CourseFilter = {
  search?: string
  skills?: ISelectOption[]
  provider?: ISelectOption[]
  level?: ISelectOption[]
  type?: ISelectOption[]
  languages?: ISelectOption[]
  isFilterTouched?: boolean
}

export type TenantCourseParams = {
  limit?: number
  page?: number
  search?: string
  locationIds?: Array<number>
  departmentId?: number | string
  talentPoolIds?: Array<number>
  jobsKey?: string
  jobLevel?: string
}

export type Sorting = {
  createdAt?: string
  lastActivity?: string
  relevant?: string
}

export interface ThumbnailVariants {
  url: string
  medium?: {
    url: string
  }
  thumb?: {
    url: string
  }
}

export interface AnalysisData {
  positionId: string
  content: string
  status: (typeof ANALYSIS_STATUS)[keyof typeof ANALYSIS_STATUS]
  error?: string
}

export type IOpenJobsPositionFilter = {
  countryStateIds?: ISelectOption[]
  search?: string
  positionRecommendId?: string | number
  [key: string]: FilterValue
}

export interface FilterValue {
  [key: string]:
    | string
    | number
    | boolean
    | ISelectOption
    | ISelectOption[]
    | undefined
}

export interface CountryState {
  countryStateId: string | number
  country: string
  state?: string | undefined
}
export interface CountryStateExtras {
  countryStateList?: CountryState[]
}
