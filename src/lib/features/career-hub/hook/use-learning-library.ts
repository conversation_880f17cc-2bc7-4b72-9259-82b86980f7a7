'use client'

import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import useEnumsData from 'src/hooks/data/use-enums-data'
import { ASC_SORTING, DEFAULT_MOUNT_PAGE_SIZE, DESC_SORTING } from '~/core/constants/enum'
import { convertObjectWithEnum } from '~/core/utilities/common'

import { useRouterContext } from '~/lib/next/use-router-context'

import { useInfinityGraphPage } from '../../jobs/hooks/use-infinity-graph-page'
import { trimObjectProps } from '../../tasks/utilities/common'
import QueryTenantCourses from '../graphql/query-tenant-courses-list'
import type { CourseFilter } from '../types'

const useLearningLibrary = (
  props?: CourseFilter & {
    disableGetCourse?: boolean
    enableSort?: boolean
    sort?: string
  }
) => {
  const { i18n } = useTranslation()
  const { searchParams } = useRouterContext()
  const search = searchParams?.get('search')
  const [isResetting, setIsResetting] = useState<boolean>(false)
  const [sorting, setSorting] = useState<{
    createdAt?: string
    lastActivity?: string
    relevant?: string
  }>()

  const providerOptions = useEnumsData({
    enumType: 'CourseProvider',
    locale: i18n.language
  })
  const levelOptions = useEnumsData({
    enumType: 'CourseLevel',
    locale: i18n.language
  })
  const languagesOptions = useEnumsData({
    enumType: 'CourseLanguage',
    locale: i18n.language
  })
  const typeOptions = useEnumsData({
    enumType: 'Course',
    locale: i18n.language
  })
  const priceOptions = useEnumsData({
    enumType: 'CoursePrice',
    locale: i18n.language
  })

  const [filterValue, onChangeFilter] = useState<CourseFilter | undefined>({
    search: (search as string) || undefined,
    skills: props?.skills || undefined,
    provider: convertObjectWithEnum(providerOptions, props?.provider || undefined),
    level: convertObjectWithEnum(levelOptions, props?.level || undefined),
    type: convertObjectWithEnum(typeOptions, props?.type || undefined),
    languages: convertObjectWithEnum(languagesOptions, props?.languages || undefined)
  })

  const getCoursesWithPaging = useInfinityGraphPage({
    queryDocumentNote: QueryTenantCourses,
    getVariable: (page: number) => {
      const { search, skills, provider, level, type, languages } = filterValue || {}
      const objProps = trimObjectProps({
        limit: DEFAULT_MOUNT_PAGE_SIZE,
        page,
        search,
        skills: skills && skills?.length > 0 ? skills.map((s) => Number(s.value)) : undefined,
        providers: provider?.length ? provider?.map((p) => p.value) : undefined,
        levels: level?.length ? level?.map((l) => l.value) : undefined,
        type: type?.length ? type?.map((t) => t.value) : undefined,
        languages: languages?.length ? languages?.map((l) => l.value) : undefined
      })
      return { ...objProps, sorting }
    },
    getPageAttribute: (_lastGroup, groups) => ({
      totalCount: _lastGroup?.tenantCoursesList?.metadata?.totalCount,
      pageLength: Number(groups?.[0]?.tenantCoursesList?.collection?.length)
    }),
    queryKey: ['learning-library'],
    enabled: !props?.disableGetCourse && !props?.sort
  })

  const refetchData = async () => {
    if (props?.disableGetCourse) return
    setIsResetting(true)
    await getCoursesWithPaging.refetch()
    setIsResetting(false)
  }

  useEffect(() => {
    if (!filterValue?.isFilterTouched) return
    refetchData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterValue])

  useEffect(() => {
    if (sorting) refetchData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sorting])

  useEffect(() => {
    const sort =
      props?.sort === 'newest'
        ? { createdAt: DESC_SORTING }
        : props?.sort === 'oldest'
          ? { createdAt: ASC_SORTING }
          : { relevant: DESC_SORTING }

    setSorting(sort)
  }, [props?.sort])

  return {
    getCoursesWithPaging,
    filterControl: useMemo(
      () => ({
        value: filterValue,
        onChange: onChangeFilter
      }),
      [filterValue]
    ),
    action: {},
    refetch: () => getCoursesWithPaging.refetch(),
    providerOptions,
    levelOptions,
    typeOptions,
    languagesOptions,
    priceOptions,
    isResetting,
    setSorting,
    sorting
  }
}

export default useLearningLibrary
