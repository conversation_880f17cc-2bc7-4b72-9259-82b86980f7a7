'use client'

import { parseCookies } from 'nookies'
import { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'

import configuration from '~/configuration'
import { SESSION_COOKIE_NAME } from '~/core/constants/cookies'

import { ANALYSIS_STATUS } from '~/lib/features/career-hub/utilities/enum'

import { useAnalysisStore } from '../store/analysis-store'

const useAIAnalysis = () => {
  const { t } = useTranslation()
  const { data, setData, clearData, isRedirecting, setIsRedirecting } = useAnalysisStore()
  const [isStreaming, setIsStreaming] = useState(false)
  const [shouldAnimate, setShouldAnimate] = useState(false)

  // Enable analysis when entering position detail page
  const enableAnalysis = useCallback(() => {
    setIsRedirecting(false)
  }, [setIsRedirecting])

  // Disable analysis when leaving position detail page
  const disableAnalysis = useCallback(() => {
    setIsRedirecting(true)
    clearData()
  }, [setIsRedirecting, clearData])

  // Mark analysis as completed to prevent further API calls
  const markAnalysisCompleted = useCallback(() => {
    setIsRedirecting(true)
  }, [setIsRedirecting])

  // Reset typing animation state
  const resetTypingAnimation = useCallback(() => {
    setShouldAnimate(false)
  }, [setShouldAnimate])

  const handleAPIStreaming = useCallback(
    async (positionId: string, regenerate: boolean) => {
      setIsStreaming(true)
      setShouldAnimate(true) // Always show typing animation when API is called
      const cookies = parseCookies()
      const authenticationToken = cookies[SESSION_COOKIE_NAME]

      try {
        const response = await fetch(configuration.api.chubPositionAnalysis, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${authenticationToken}`
          },
          body: JSON.stringify({ positionId, regenerate })
        })

        if (response.status === 204) {
          setIsStreaming(false)
          setShouldAnimate(false)
          setData({
            positionId,
            content: '',
            status: ANALYSIS_STATUS.no_content,
            error: undefined
          })
          return
        }

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        if (!response.body) {
          throw new Error('No response body')
        }

        const reader = response.body.getReader()
        let accumulatedContent = ''

        if (reader) {
          while (true) {
            // Stop streaming if redirecting
            if (isRedirecting) {
              reader.cancel()
              break
            }

            const { done, value } = await reader.read()
            if (done) break

            const chunk = new TextDecoder().decode(value)
            const rawChunks = chunk.split('\n')
            const isDone = rawChunks.some((line) => line.includes('"status":"done"'))

            const formatted = rawChunks
              .filter((line) => !line.includes('"status"') && !line.startsWith('event'))
              .map((line) => line.replace(/^data:\s?/, ''))
              .join('\n')

            if (formatted.trim()) {
              // Format markdown content properly
              // const formattedChunk = formatted
              //   .replace(/^### (\d+\. .+)$/gm, '\n### $1\n') // headers
              //   .replace(/^---$/gm, '\n---\n') // separators
              //   .replace(/^\- /gm, '\n- ') // bullet points
              //   .replace(/[ \t]+$/gm, '') // xoá khoảng trắng cuối dòng

              const formattedChunk = formatted
                .replace(/^### (\d+\. .+)$/gm, '\n### $1\n') // Add newlines around headers
                .replace(/^---$/gm, '\n---\n') // Add newlines around separators
                .replace(/^\- /gm, '\n- ') // Add newline before bullet points

              accumulatedContent += formattedChunk + '\n'

              // Update data with streaming content and keep typing animation
              setData({
                positionId,
                content: accumulatedContent,
                status: ANALYSIS_STATUS.loading
              })
            }

            if (isDone) {
              // Store empty string if no content, null means never called API
              const finalContent = accumulatedContent.trim()

              setData({
                positionId,
                content: finalContent,
                status: ANALYSIS_STATUS.loading // Keep loading status to continue typing animation
              })
              // Don't stop typing animation yet, let it complete naturally
            }
          }
        }

        setIsStreaming(false)
      } catch (error) {
        setIsStreaming(false)
        setData({
          positionId,
          content: '', // Store empty string for error case
          status: ANALYSIS_STATUS.error,
          error: t('Failed to generate analysis') || ''
        })
        setShouldAnimate(false)
      }
    },
    [t, setData, isRedirecting]
  )

  // Generate analysis - call API if data is null, otherwise use cached data
  const generateAnalysis = useCallback(
    async (positionId: string) => {
      if (!positionId) {
        return
      }

      // Don't call API if redirecting
      if (isRedirecting) {
        return
      }

      // If data exists, use cached data without typing animation
      if (data !== null) {
        setShouldAnimate(false) // No typing animation for cached data
        return
      }

      // Call API if no data exists
      await handleAPIStreaming(positionId, false)
    },
    [handleAPIStreaming, data, isRedirecting, setShouldAnimate]
  )

  // Regenerate analysis - always call API and show typing animation
  const regenerateAnalysis = useCallback(
    async (positionId: string) => {
      if (!positionId) {
        return
      }

      // Always call API for regenerate, regardless of current state
      setIsRedirecting(false)

      await handleAPIStreaming(positionId, true)
    },
    [handleAPIStreaming]
  )

  return {
    analysis: data, // Return null when no data, let component handle it
    isStreaming,
    generateAnalysis,
    regenerateAnalysis,
    shouldAnimate,
    enableAnalysis,
    disableAnalysis,
    markAnalysisCompleted,
    resetTypingAnimation
  }
}

export default useAIAnalysis
