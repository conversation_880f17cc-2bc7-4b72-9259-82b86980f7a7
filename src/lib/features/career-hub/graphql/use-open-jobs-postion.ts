import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'

import { AGENCY_TENANT, DEFAULT_MOUNT_PAGE_SIZE } from '~/core/constants/enum'

import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'

import { useInfinityGraphPage } from '../../jobs/hooks/use-infinity-graph-page'
import type { IOpenJobsManagementFilter } from '../../referrals/types'
import { trimObjectProps } from '../../tasks/utilities/common'
import QueryDirectPositionOpenJobs from './query-direct-position-open-jobs'
import QueryPositionOpenJobsCareerHub from './query-position-open-jobs'

export const PAGE_SIZE_OPEN_JOBS_MANAGEMENT = 10

const useOpenJobsPosition = (props: IOpenJobsManagementFilter) => {
  const { t, i18n } = useTranslation()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })

  const [filterValue, onChangeFilter] = useState<IOpenJobsManagementFilter | undefined>({
    search: props.search || undefined,
    countryStateIds: props?.countryStateIds || undefined,
    positionRecommendId: Number(props?.positionRecommendId) || undefined
  })
  const jobsPaging = useInfinityGraphPage({
    queryDocumentNote: isCompanyKind ? QueryPositionOpenJobsCareerHub : QueryDirectPositionOpenJobs,
    getVariable: useCallback(
      (page: number) => {
        const { search, countryStateIds, positionRecommendId } = filterValue || {}

        return trimObjectProps({
          limit: DEFAULT_MOUNT_PAGE_SIZE,
          page,
          search,
          countryStateIds:
            (countryStateIds?.length || 0) > 0
              ? countryStateIds?.map((item: { value: string }) => parseInt(item.value))
              : undefined,
          positionRecommendId
        })
      },
      [filterValue]
    ),
    getPageAttribute: (_lastGroup, groups) => ({
      totalCount: _lastGroup?.jobsReferableList?.metadata?.totalCount,
      pageLength: groups?.[0]?.jobsReferableList?.collection?.length || 0
    }),
    queryKey: ['referral-open-jobs-position']
  })
  useEffect(() => {
    jobsPaging.refetch()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterValue])
  return {
    openJobsPaging: jobsPaging,
    filterControl: useMemo(
      () => ({
        value: filterValue,
        onChange: onChangeFilter
      }),
      [filterValue]
    ),
    action: {},
    refetch: () => jobsPaging.refetch()
  }
}

export default useOpenJobsPosition
