import { gql } from 'urql'

import type { JobDetailType, OpenJobsParams } from '../../referrals/types'

const QueryDirectPositionOpenJobs = gql<
  {
    jobsReferableList: {
      collection: Array<JobDetailType>
      metadata: {
        totalCount: number
        extras: any
      }
    }
  },
  OpenJobsParams
>`
  query ($limit: Int, $page: Int, $search: String, $countryStateIds: [Int!], $positionRecommendId: Int) {
    jobsReferableList(
      limit: $limit
      page: $page
      search: $search
      countryStateIds: $countryStateIds
      positionRecommendId: $positionRecommendId
    ) {
      collection {
        id
        title
        slug
        createdAt
        referralsCount
        jobReferable
        jobLocations {
          state
          country
        }
        department {
          name
        }
        tenant {
          slug
        }
        status
        enablingReward
        rewardAmount
        rewardCurrency
        rewardGift
        referralRewardType
        publicReferralUri
        recommendationMatchedFields
        permittedFields
        currentUserAppliedAt
        savedReferralJobMemberIds
        tags {
          id
          name
        }
        skills
      }
      metadata {
        totalCount
        extras
      }
    }
  }
`

export default QueryDirectPositionOpenJobs
