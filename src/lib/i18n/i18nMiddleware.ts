import { i18nRouter } from 'next-i18n-router'
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import i18nConfig, { LOCALE_COOKIE } from './i18nConfig'

async function determineLocale(req: NextRequest): Promise<void> {
  const pathname = req.nextUrl.pathname
  const pathLocale = i18nConfig.locales.find(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  )

  if (!pathLocale) {
    // Only set cookie if it doesn't already exist or is different
    const currentCookie = req.cookies.get(LOCALE_COOKIE)?.value
    if (!currentCookie || currentCookie !== i18nConfig.defaultLocale) {
      req.cookies.set(LOCALE_COOKIE, i18nConfig.defaultLocale)
    }
  }
}

export function handleI18nRouting(request: NextRequest): NextResponse | Response {
  try {
    const result = i18nRouter(request, i18nConfig)
    return result || NextResponse.next()
  } catch (error) {
    console.error('i18n Router Error:', error)
    return NextResponse.next()
  }
}

export function enhanceI18nResponse(response: NextResponse | Response): NextResponse {
  // If it's already a NextResponse, return as is
  if (response instanceof NextResponse) {
    return response
  }

  // Convert Response to NextResponse
  return new NextResponse(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: response.headers
  })
}

export async function processI18nMiddleware(request: NextRequest): Promise<NextResponse | Response> {
  try {
    await determineLocale(request)
    return handleI18nRouting(request)
  } catch (error) {
    console.error('processI18nMiddleware Error:', error)
    return NextResponse.next()
  }
}
