// Global types for i18n
import type { defaultI18nSchemas } from './config'
import type i18nConfig from './i18nConfig'

export type Locale = (typeof i18nConfig.locales)[number]
export type Namespace = (typeof defaultI18nSchemas)[number]

export interface TranslationOptions {
  [key: string]: any
  ns?: any
  defaultValue?: string
  count?: number
  context?: string
  replace?: Record<string, string | number>
  lng?: Locale
}

export type TFunction = (key: string, options?: TranslationOptions) => string
