'use client'

import { useTranslation } from 'react-i18next'

import pathConfiguration from 'src/configuration/path'
import { PUBLIC_APP_URL } from '~/core/constants/env'

import type { JobType } from '~/lib/features/apply/jobId/types'
import type { CareerPageSettingType } from '~/lib/features/careers/[id]/types'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import { GoogleStructureTag } from '~/components/GoogleJobTags'
import type { SettingEditorFormType } from '~/components/Settings/Careers/Editor/CustomizeSettingTab'
import JobDetailContainer from '~/features/careers/[id]/[jobId]'

function CustomDomainPublicCareerApp({
  jobRes,
  careerPageSetting,
  templateConfig
}: {
  jobRes: { publicJobsShow: JobType }
  careerPageSetting: CareerPageSettingType
  templateConfig?: SettingEditorFormType
}) {
  const job: JobType = jobRes?.publicJobsShow
  const { i18n } = useTranslation()

  return (
    <>
      <GoogleStructureTag jobDetail={job} />
      <AppHeadMetaTags
        title={`${job?.title} - ${job?.tenant?.name}`}
        description={
          (typeof job?.tenant?.careerSiteSettings?.description === 'string'
            ? job?.tenant?.careerSiteSettings?.description
            : job?.tenant?.careerSiteSettings?.description?.[i18n.language]) || ''
        }
        url={`${PUBLIC_APP_URL}${pathConfiguration.careers.jobDetail({
          tenantSlug: job?.tenant?.slug,
          jobId: job?.id
        })}`}
        image={`${PUBLIC_APP_URL}/img/feature/careers/opengraph_v2.jpg`}
      />
      <JobDetailContainer job={job} careerPageSetting={careerPageSetting} templateConfig={templateConfig} />
    </>
  )
}

export default CustomDomainPublicCareerApp
