'use client'

import { useEffect } from 'react'
import ReactGA from 'react-ga4'
import { useTranslation } from 'react-i18next'

import { PUBLIC_APP_URL } from '~/core/constants/env'

import type { CareerPageSettingType, ICareerForm } from '~/lib/features/careers/[id]/types'
import type { TemplateResponseType } from '~/lib/features/settings/careers/types/editor'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import SimpleCareersContainer from '~/features/careers/[id]'

function CustomDomainSimpleCareerApp({
  careerPageSetting,
  careerTemplate,
  queryStringParams
}: {
  careerPageSetting: CareerPageSettingType
  queryStringParams: ICareerForm
  careerTemplate?: {
    publicCareerTemplatesShow: TemplateResponseType
  }
}) {
  const { i18n } = useTranslation()

  useEffect(() => {
    if (careerPageSetting?.ga_measurement_id && !ReactGA.isInitialized) {
      ReactGA.initialize(careerPageSetting?.ga_measurement_id)
    }
  }, [careerPageSetting])

  return (
    <>
      <AppHeadMetaTags
        title={careerPageSetting.page_title || ''}
        description={careerPageSetting.description?.[i18n.language]}
        url={`${PUBLIC_APP_URL}${careerPageSetting.canonical_url}`}
        image={`${PUBLIC_APP_URL}/img/feature/careers/opengraph_v2.jpg`}
        hideFavicon={true}
      />

      <SimpleCareersContainer
        queryStringParams={queryStringParams}
        careerPageSetting={careerPageSetting}
        careerTemplate={careerTemplate}
      />
    </>
  )
}

export default CustomDomainSimpleCareerApp
