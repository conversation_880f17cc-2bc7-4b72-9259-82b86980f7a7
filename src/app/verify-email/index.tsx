'use client'

import { useTranslation } from 'react-i18next'

import { PUBLIC_APP_NAME } from '~/core/constants/env'

import AppHeadMetaTags from '~/components/AppHeadMetaTags'
import GoogleTagManagementPublic from '~/components/GoogleTagManagementPublic'
import VerifyEmailContainer from '~/features/verify-email'

function VerifyEmailApp() {
  const { t } = useTranslation()
  return (
    <>
      <AppHeadMetaTags title={`${t(`common:seo:verifyEmail`, { PUBLIC_APP_NAME })}`} />

      <GoogleTagManagementPublic />
      <VerifyEmailContainer />
    </>
  )
}

export default VerifyEmailApp
