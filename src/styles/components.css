/* Table Components */
.table-tbody-hover .group-hover:not(.group-selected),
.table-tbody-inline-hover .group-hover:not(.group-selected) {
  display: none;
}

.table-tbody-selected,
.table-tbody-inline-selected .td,
.table-tbody-hover:hover,
.table-tbody-hover:hover td,
.table-tbody-hover:hover .td,
.table-tbody-inline-hover:hover .td {
  background-color: var(--color-gray-50);
}

.table-tbody-hover:hover .group-hover,
.table-tbody-inline-hover:hover .group-hover {
  display: block;
}

.table-tbody-selected .checkedSystemId,
.table-tbody-inline-selected .checkedSystemId {
  display: flex !important;
}

.table-tbody-selected .systemId,
.table-tbody-inline-selected .systemId {
  display: none !important;
}

/* Prose Components */
.prose :where(ul > li):not(:where([class~='not-prose'] *))::marker {
  color: var(--color-gray-900);
}

.prose :where(tbody td):not(:where([class~='not-prose'] *)) {
  vertical-align: inherit;
  margin-top: 0;
  margin-bottom: 0;
}

.prose :where(p):not(:where([class~='not-prose'] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose :where(tbody td):not(:where([class~="not-prose"] *)) {
  vertical-align: inherit;
  margin-top: 0;
  margin-bottom: 0;
}

.prose :where(p):not(:where([class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

/* Chart Components */
.funnel-chart text {
  font-size: 0.875rem !important; /* 14px - equivalent to text-sm */
}
  
@keyframes slideIn {
  from {
    transform: translateX(calc(100% + 25px));
  }
  to {
    transform: translateX(0);
  }
}

@keyframes swipeOut {
  from {
    transform: translateX(var(--radix-toast-swipe-end-x));
  }
  to {
    transform: translateX(calc(100% + 25px));
  }
}

.slideIn {
  animation: slideIn 150ms cubic-bezier(0.16, 1, 0.3, 1);
}
.hide {
  animation: hide 100ms ease-in;
}
.swipeOut {
  animation: swipeOut 100ms ease-out;
}

.funnel-chart text {
  @apply !text-sm
}

.prose :where(tbody td):not(:where([class~="not-prose"] *)) {
  vertical-align: inherit;
  margin-top: 0;
  margin-bottom: 0;
}

.prose :where(p):not(:where([class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
